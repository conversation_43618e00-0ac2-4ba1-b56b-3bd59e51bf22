# TOL Python Documentation

This directory contains the Sphinx-based documentation for TOL Python.

## Phase 1 Infrastructure Setup - COMPLETED ✅

The following Phase 1 deliverables have been successfully implemented:

- ✅ Sphinx project structure under `docs/`
- ✅ PyData Sphinx theme configuration
- ✅ GitHub Actions workflow (`.github/workflows/docs.yml`)
- ✅ Successful local HTML build (`make html`)
- ✅ Basic navigation structure ready for content migration
- ✅ Documentation dependencies configured in `setup.py`
- ✅ Cross-platform build system (Makefile + make.bat)
- ✅ Link checking functionality (`make linkcheck`)

## Building Documentation

### Prerequisites

Install documentation dependencies:

```bash
pip install -e .[docs]
```

### Local Development

```bash
# Build HTML documentation
cd docs
make html

# Clean build files
make clean

# Check for broken links
make linkcheck

# Live reload during development (if sphinx-autobuild is installed)
make livehtml
```

### Available Make Targets

- `make html` - Build HTML documentation
- `make clean` - Remove build files
- `make linkcheck` - Check for broken links
- `make pdf` - Build PDF documentation (requires LaTeX)
- `make epub` - Build EPUB documentation

## Directory Structure

```
docs/
├── source/                     # Sphinx source files
│   ├── conf.py                # Sphinx configuration
│   ├── index.rst              # Main landing page
│   ├── _static/               # Static assets (CSS, images)
│   ├── _templates/            # Custom HTML templates
│   ├── getting-started/       # Installation and quick start
│   ├── user-guide/            # Tutorials and how-to guides
│   ├── api-reference/         # Auto-generated API docs
│   ├── examples/              # Example gallery
│   ├── developer-guide/       # Development documentation
│   └── reference/             # Additional reference material
├── build/                     # Generated documentation
│   ├── html/                  # HTML output
│   ├── pdf/                   # PDF output
│   └── epub/                  # EPUB output
├── notebooks/                 # Interactive Jupyter examples
├── requirements.txt           # Documentation dependencies
├── Makefile                   # Build automation (Unix)
└── make.bat                   # Build automation (Windows)
```

## Next Steps (Phase 2)

The infrastructure is now ready for Phase 2 content migration:

1. **Content Migration**: Convert existing markdown files to reStructuredText
2. **API Documentation**: Enable autodoc and enhance docstrings
3. **Interactive Examples**: Create Jupyter notebook gallery
4. **Mathematical Documentation**: Add LaTeX formulas and equations

## GitHub Actions

The documentation is automatically built and deployed via GitHub Actions:

- **Build**: Triggered on push to `main` and `develop` branches
- **Deploy**: Automatically deploys to GitHub Pages on `main` branch
- **Link Checking**: Validates all external links

## Configuration

Key configuration files:

- `docs/source/conf.py` - Main Sphinx configuration
- `docs/requirements.txt` - Documentation dependencies
- `.github/workflows/docs.yml` - CI/CD pipeline
- `setup.py` - Package configuration with docs extras

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the package is installed in development mode (`pip install -e .`)
2. **Missing Dependencies**: Install docs dependencies (`pip install -e .[docs]`)
3. **Build Failures**: Check `docs/build/html` for error logs
4. **Link Check Failures**: Review `docs/build/linkcheck/output.txt`

### Getting Help

- Check the migration guide: `DOCUMENTATION_MIGRATION.md`
- Review Sphinx documentation: https://www.sphinx-doc.org/
- PyData theme docs: https://pydata-sphinx-theme.readthedocs.io/
