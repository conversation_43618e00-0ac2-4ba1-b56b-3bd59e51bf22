API Reference
=============

Complete reference for all classes, functions, and modules in TOL Python.

This section provides detailed API documentation for all public classes and functions.
Auto-generated documentation will be added in Phase 2 of the documentation migration.

Core Module
-----------

Date and TimeSet classes for time series indexing.

Series Module
-------------

Main Serie class and time series operations.

Statistics Module
-----------------

Statistical functions and tests for time series analysis.

ARIMA Module
------------

ARIMA modeling and forecasting capabilities.

Bayesian Module
---------------

Bayesian ARIMA modeling with MCMC estimation.

Frequency Module
----------------

Frequency domain analysis and filtering operations.
