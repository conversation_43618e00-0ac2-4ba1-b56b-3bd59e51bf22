import os
import sys
sys.path.insert(0, os.path.abspath('../../'))

# Project information
project = 'TOL Python'
copyright = '2024, TOL Python Team'
author = 'TOL Python Team'
version = '0.2.0'
release = '0.2.0'

# Extensions
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.napoleon',
    'sphinx.ext.intersphinx',
    'sphinx.ext.mathjax',
    'sphinx.ext.viewcode',
    'sphinx.ext.githubpages',
    # 'sphinx_gallery.gen_gallery',  # Disabled until examples are ready
    'nbsphinx',
    'myst_parser',
    'sphinx_copybutton',
    'sphinxext.opengraph'
]

# Theme configuration
html_theme = 'pydata_sphinx_theme'
html_theme_options = {
    'github_url': 'https://github.com/m-marinucci/TOL-python',
    'use_edit_page_button': True,
    'show_toc_level': 2,
    'navbar_align': 'content',
    'navbar_end': ['navbar-icon-links', 'search-button'],
    'footer_start': ['copyright'],
    'footer_end': ['last-updated'],
    'secondary_sidebar_items': ['page-toc', 'edit-this-page']
}

# GitHub context for edit button
html_context = {
    'github_user': 'm-marinucci',
    'github_repo': 'TOL-python',
    'github_version': 'main',
    'doc_path': 'docs/source',
}

# Static files
html_static_path = ['_static']
html_css_files = ['custom.css']
# html_logo = '_static/logo.png'  # Disabled until logo is created
# html_favicon = '_static/favicon.ico'  # Disabled until favicon is created

# API documentation
autodoc_default_options = {
    'members': True,
    'undoc-members': True,
    'show-inheritance': True,
    'special-members': '__init__',
}

# Napoleon settings (for NumPy/Google style docstrings)
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False
napoleon_use_param = True
napoleon_use_rtype = True
napoleon_type_aliases = None

# Intersphinx mapping
intersphinx_mapping = {
    'python': ('https://docs.python.org/3/', None),
    'numpy': ('https://numpy.org/doc/stable/', None),
    'scipy': ('https://docs.scipy.org/doc/scipy/', None),
    'matplotlib': ('https://matplotlib.org/stable/', None),
    'pandas': ('https://pandas.pydata.org/docs/', None),
}

# Sphinx Gallery configuration (disabled until examples are ready)
# sphinx_gallery_conf = {
#     'examples_dirs': '../../examples',
#     'gallery_dirs': 'examples',
#     'filename_pattern': r'\.py$',
#     'ignore_pattern': r'__.*\.py$',
#     'download_all_examples': False,
#     'plot_gallery': 'True',
# }

# OpenGraph configuration
ogp_site_url = 'https://m-marinucci.github.io/TOL-python/'
ogp_site_name = 'TOL Python Documentation'
ogp_image = 'https://m-marinucci.github.io/TOL-python/_static/logo.png'

# Copy button configuration
copybutton_prompt_text = r'>>> |\.\.\. |\$ |In \[\d*\]: | {2,5}\.\.\.: | {5,8}: '
copybutton_prompt_is_regexp = True

# HTML options
html_title = f"{project} {version}"
html_short_title = project
html_show_sourcelink = True
html_show_sphinx = True
html_show_copyright = True

# Build options
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']
templates_path = ['_templates']
source_suffix = {
    '.rst': None,
    '.md': 'myst_parser',
}

# LaTeX options for PDF generation
latex_elements = {
    'papersize': 'letterpaper',
    'pointsize': '10pt',
    'preamble': r'''
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
''',
}

latex_documents = [
    ('index', 'TOLPython.tex', 'TOL Python Documentation',
     'TOL Python Team', 'manual'),
]

# EPUB options
epub_title = project
epub_author = author
epub_publisher = author
epub_copyright = copyright
epub_exclude_files = ['search.html']
