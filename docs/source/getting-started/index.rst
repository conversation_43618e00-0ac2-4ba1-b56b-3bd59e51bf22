Getting Started
===============

Welcome to TOL Python! This section will help you get up and running quickly.

.. Contents will be added in Phase 2 of the documentation migration.

Installation
------------

TOL Python requires Python 3.7 or later. Install using pip:

.. code-block:: bash

   pip install tol-python

Or for development:

.. code-block:: bash

   git clone https://github.com/m-marinucci/TOL-python.git
   cd TOL-python
   pip install -e .

Quick Start
-----------

Here's a simple example to get you started:

.. code-block:: python

   from tol_python import Serie, Date
   
   # Create a time series
   data = [1, 2, 3, 4, 5]
   serie = Serie(data=data, 
                first_date="y2023m01d01", 
                last_date="y2023m01d05")
   
   # Basic operations
   print(f"Length: {len(serie)}")
   print(f"Mean: {serie.mean()}")
   print(f"First date: {serie.first_date()}")
   print(f"Last date: {serie.last_date()}")

Next Steps
----------

- Read the :doc:`../user-guide/index` for comprehensive tutorials
- Explore the :doc:`../api-reference/index` for detailed API documentation
- Check out :doc:`../examples/index` for real-world examples
