TOL Python Documentation
========================

Welcome to TOL Python, a comprehensive time series analysis library that brings
the power of TOL (Time-Oriented Language) to Python.

Navigation
----------

* :doc:`getting-started/index` - New to TOL Python? Start here for installation instructions and your first time series analysis.

* :doc:`user-guide/index` - Comprehensive tutorials and how-to guides for time series analysis, statistical modeling, and advanced features.

* :doc:`api-reference/index` - Complete reference for all classes, functions, and modules in TOL Python with detailed examples.

* :doc:`developer-guide/index` - Contributing guidelines, architecture documentation, and development best practices.

Quick Example
-------------

.. code-block:: python

   from tol_python import Serie
   import numpy as np

   # Create a time series
   data = [100, 102, 105, 103, 107]
   serie = Serie(data=data, 
                first_date="y2023m01d01", 
                last_date="y2023m01d05")

   # Perform analysis
   print(f"Mean: {serie.mean():.2f}")
   print(f"Volatility: {serie.std():.2f}")
   
   # Time series operations
   returns = serie.pct_change()
   ma_3 = serie.moving_average(3)

Features
--------

- **TOL-Compatible Serie Class**: Full implementation of time series with flexible dating
- **Date/Time System**: TOL's date format (y2023m01d15) with Begin/End sentinels  
- **Flexible Dating**: Support for daily, weekly, monthly, quarterly, and yearly frequencies
- **Missing Values**: Native support for missing data
- **Operations**: Arithmetic, lag/lead, differences, moving averages
- **Statistical Analysis**: ACF, PACF, unit root tests, and more
- **ARIMA Modeling**: Classical and Bayesian ARIMA with automatic selection
- **Frequency Domain**: FFT operations and digital filtering
- **I/O Formats**: Binary (TOL-compatible), CSV, JSON
- **Integration**: NumPy arrays internally, pandas conversion

.. toctree::
   :maxdepth: 2
   :hidden:

   getting-started/index
   user-guide/index
   api-reference/index
   examples/index
   developer-guide/index
   reference/index
