User Guide
==========

Comprehensive tutorials and how-to guides for TOL Python.

.. Contents will be added in Phase 2 of the documentation migration.

This section contains detailed tutorials and guides for using TOL Python effectively.
Content will be migrated from existing markdown files in Phase 2 of the documentation migration.

Basic Concepts
--------------

Learn the fundamental concepts of TOL Python:

- Time series representation with the Serie class
- Date and TimeSet handling
- Data types and missing values
- Basic operations and transformations

Time Series Operations
----------------------

Master time series manipulations:

- Lag and lead operations
- Differences and percentage changes
- Moving averages and smoothing
- Resampling and frequency conversion

Statistical Analysis
--------------------

Perform statistical analysis:

- Descriptive statistics
- Autocorrelation and partial autocorrelation
- Unit root and stationarity tests
- Seasonal decomposition

Advanced Features
-----------------

Explore advanced functionality:

- ARIMA modeling (classical and Bayesian)
- Frequency domain analysis
- Complex time series operations
- Performance optimization techniques
