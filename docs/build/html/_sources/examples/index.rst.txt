Examples
========

Interactive examples and tutorials demonstrating TOL Python capabilities.

.. Contents will be added in Phase 2 of the documentation migration.

This section contains practical examples and use cases for TOL Python.
Interactive Jupyter notebooks will be added in Phase 2 of the documentation migration.

Basic Examples
--------------

Simple examples to get you started:

- Creating and manipulating time series
- Basic statistical operations
- Data visualization
- Import/export operations

Financial Examples
------------------

Financial time series analysis:

- Stock price analysis
- Return calculations
- Risk metrics
- Portfolio optimization

Scientific Examples
-------------------

Scientific computing applications:

- Signal processing
- Experimental data analysis
- Time series forecasting
- Statistical modeling

Advanced Examples
-----------------

Advanced use cases:

- Bayesian ARIMA modeling
- Frequency domain analysis
- Custom statistical functions
- Performance optimization
