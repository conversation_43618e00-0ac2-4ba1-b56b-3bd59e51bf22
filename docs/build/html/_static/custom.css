/* Custom CSS for TOL Python Documentation */

/* Brand colors and styling */
:root {
    --tol-primary: #2E86AB;
    --tol-secondary: #A23B72;
    --tol-accent: #F18F01;
    --tol-dark: #C73E1D;
}

/* Custom header styling */
.navbar-brand {
    font-weight: bold;
    color: var(--tol-primary) !important;
}

/* Code block styling */
.highlight {
    border-radius: 6px;
    border: 1px solid #e1e4e8;
}

/* Grid card styling */
.sd-card {
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    transition: box-shadow 0.2s ease-in-out;
}

.sd-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Table styling */
table.docutils {
    border-collapse: collapse;
    margin: 1em 0;
}

table.docutils th,
table.docutils td {
    border: 1px solid #e1e4e8;
    padding: 8px 12px;
}

table.docutils th {
    background-color: #f6f8fa;
    font-weight: 600;
}

/* Math equation styling */
.math {
    text-align: center;
    margin: 1em 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sd-grid {
        grid-template-columns: 1fr !important;
    }
}
