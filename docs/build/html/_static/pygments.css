html[data-theme="light"] .highlight pre { line-height: 125%; }
html[data-theme="light"] .highlight td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
html[data-theme="light"] .highlight span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
html[data-theme="light"] .highlight td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
html[data-theme="light"] .highlight span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
html[data-theme="light"] .highlight .hll { background-color: #fae4c2 }
html[data-theme="light"] .highlight { background: #fefefe; color: #080808 }
html[data-theme="light"] .highlight .c { color: #515151 } /* Comment */
html[data-theme="light"] .highlight .err { color: #A12236 } /* Error */
html[data-theme="light"] .highlight .k { color: #6730C5 } /* Keyword */
html[data-theme="light"] .highlight .l { color: #7F4707 } /* Literal */
html[data-theme="light"] .highlight .n { color: #080808 } /* Name */
html[data-theme="light"] .highlight .o { color: #00622F } /* Operator */
html[data-theme="light"] .highlight .p { color: #080808 } /* Punctuation */
html[data-theme="light"] .highlight .ch { color: #515151 } /* Comment.Hashbang */
html[data-theme="light"] .highlight .cm { color: #515151 } /* Comment.Multiline */
html[data-theme="light"] .highlight .cp { color: #515151 } /* Comment.Preproc */
html[data-theme="light"] .highlight .cpf { color: #515151 } /* Comment.PreprocFile */
html[data-theme="light"] .highlight .c1 { color: #515151 } /* Comment.Single */
html[data-theme="light"] .highlight .cs { color: #515151 } /* Comment.Special */
html[data-theme="light"] .highlight .gd { color: #005B82 } /* Generic.Deleted */
html[data-theme="light"] .highlight .ge { font-style: italic } /* Generic.Emph */
html[data-theme="light"] .highlight .gh { color: #005B82 } /* Generic.Heading */
html[data-theme="light"] .highlight .gs { font-weight: bold } /* Generic.Strong */
html[data-theme="light"] .highlight .gu { color: #005B82 } /* Generic.Subheading */
html[data-theme="light"] .highlight .kc { color: #6730C5 } /* Keyword.Constant */
html[data-theme="light"] .highlight .kd { color: #6730C5 } /* Keyword.Declaration */
html[data-theme="light"] .highlight .kn { color: #6730C5 } /* Keyword.Namespace */
html[data-theme="light"] .highlight .kp { color: #6730C5 } /* Keyword.Pseudo */
html[data-theme="light"] .highlight .kr { color: #6730C5 } /* Keyword.Reserved */
html[data-theme="light"] .highlight .kt { color: #7F4707 } /* Keyword.Type */
html[data-theme="light"] .highlight .ld { color: #7F4707 } /* Literal.Date */
html[data-theme="light"] .highlight .m { color: #7F4707 } /* Literal.Number */
html[data-theme="light"] .highlight .s { color: #00622F } /* Literal.String */
html[data-theme="light"] .highlight .na { color: #912583 } /* Name.Attribute */
html[data-theme="light"] .highlight .nb { color: #7F4707 } /* Name.Builtin */
html[data-theme="light"] .highlight .nc { color: #005B82 } /* Name.Class */
html[data-theme="light"] .highlight .no { color: #005B82 } /* Name.Constant */
html[data-theme="light"] .highlight .nd { color: #7F4707 } /* Name.Decorator */
html[data-theme="light"] .highlight .ni { color: #00622F } /* Name.Entity */
html[data-theme="light"] .highlight .ne { color: #6730C5 } /* Name.Exception */
html[data-theme="light"] .highlight .nf { color: #005B82 } /* Name.Function */
html[data-theme="light"] .highlight .nl { color: #7F4707 } /* Name.Label */
html[data-theme="light"] .highlight .nn { color: #080808 } /* Name.Namespace */
html[data-theme="light"] .highlight .nx { color: #080808 } /* Name.Other */
html[data-theme="light"] .highlight .py { color: #005B82 } /* Name.Property */
html[data-theme="light"] .highlight .nt { color: #005B82 } /* Name.Tag */
html[data-theme="light"] .highlight .nv { color: #A12236 } /* Name.Variable */
html[data-theme="light"] .highlight .ow { color: #6730C5 } /* Operator.Word */
html[data-theme="light"] .highlight .pm { color: #080808 } /* Punctuation.Marker */
html[data-theme="light"] .highlight .w { color: #080808 } /* Text.Whitespace */
html[data-theme="light"] .highlight .mb { color: #7F4707 } /* Literal.Number.Bin */
html[data-theme="light"] .highlight .mf { color: #7F4707 } /* Literal.Number.Float */
html[data-theme="light"] .highlight .mh { color: #7F4707 } /* Literal.Number.Hex */
html[data-theme="light"] .highlight .mi { color: #7F4707 } /* Literal.Number.Integer */
html[data-theme="light"] .highlight .mo { color: #7F4707 } /* Literal.Number.Oct */
html[data-theme="light"] .highlight .sa { color: #00622F } /* Literal.String.Affix */
html[data-theme="light"] .highlight .sb { color: #00622F } /* Literal.String.Backtick */
html[data-theme="light"] .highlight .sc { color: #00622F } /* Literal.String.Char */
html[data-theme="light"] .highlight .dl { color: #00622F } /* Literal.String.Delimiter */
html[data-theme="light"] .highlight .sd { color: #00622F } /* Literal.String.Doc */
html[data-theme="light"] .highlight .s2 { color: #00622F } /* Literal.String.Double */
html[data-theme="light"] .highlight .se { color: #00622F } /* Literal.String.Escape */
html[data-theme="light"] .highlight .sh { color: #00622F } /* Literal.String.Heredoc */
html[data-theme="light"] .highlight .si { color: #00622F } /* Literal.String.Interpol */
html[data-theme="light"] .highlight .sx { color: #00622F } /* Literal.String.Other */
html[data-theme="light"] .highlight .sr { color: #A12236 } /* Literal.String.Regex */
html[data-theme="light"] .highlight .s1 { color: #00622F } /* Literal.String.Single */
html[data-theme="light"] .highlight .ss { color: #005B82 } /* Literal.String.Symbol */
html[data-theme="light"] .highlight .bp { color: #7F4707 } /* Name.Builtin.Pseudo */
html[data-theme="light"] .highlight .fm { color: #005B82 } /* Name.Function.Magic */
html[data-theme="light"] .highlight .vc { color: #A12236 } /* Name.Variable.Class */
html[data-theme="light"] .highlight .vg { color: #A12236 } /* Name.Variable.Global */
html[data-theme="light"] .highlight .vi { color: #A12236 } /* Name.Variable.Instance */
html[data-theme="light"] .highlight .vm { color: #7F4707 } /* Name.Variable.Magic */
html[data-theme="light"] .highlight .il { color: #7F4707 } /* Literal.Number.Integer.Long */
html[data-theme="dark"] .highlight pre { line-height: 125%; }
html[data-theme="dark"] .highlight td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
html[data-theme="dark"] .highlight span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
html[data-theme="dark"] .highlight td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
html[data-theme="dark"] .highlight span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
html[data-theme="dark"] .highlight .hll { background-color: #ffd9002e }
html[data-theme="dark"] .highlight { background: #2b2b2b; color: #F8F8F2 }
html[data-theme="dark"] .highlight .c { color: #FFD900 } /* Comment */
html[data-theme="dark"] .highlight .err { color: #FFA07A } /* Error */
html[data-theme="dark"] .highlight .k { color: #DCC6E0 } /* Keyword */
html[data-theme="dark"] .highlight .l { color: #FFD900 } /* Literal */
html[data-theme="dark"] .highlight .n { color: #F8F8F2 } /* Name */
html[data-theme="dark"] .highlight .o { color: #ABE338 } /* Operator */
html[data-theme="dark"] .highlight .p { color: #F8F8F2 } /* Punctuation */
html[data-theme="dark"] .highlight .ch { color: #FFD900 } /* Comment.Hashbang */
html[data-theme="dark"] .highlight .cm { color: #FFD900 } /* Comment.Multiline */
html[data-theme="dark"] .highlight .cp { color: #FFD900 } /* Comment.Preproc */
html[data-theme="dark"] .highlight .cpf { color: #FFD900 } /* Comment.PreprocFile */
html[data-theme="dark"] .highlight .c1 { color: #FFD900 } /* Comment.Single */
html[data-theme="dark"] .highlight .cs { color: #FFD900 } /* Comment.Special */
html[data-theme="dark"] .highlight .gd { color: #00E0E0 } /* Generic.Deleted */
html[data-theme="dark"] .highlight .ge { font-style: italic } /* Generic.Emph */
html[data-theme="dark"] .highlight .gh { color: #00E0E0 } /* Generic.Heading */
html[data-theme="dark"] .highlight .gs { font-weight: bold } /* Generic.Strong */
html[data-theme="dark"] .highlight .gu { color: #00E0E0 } /* Generic.Subheading */
html[data-theme="dark"] .highlight .kc { color: #DCC6E0 } /* Keyword.Constant */
html[data-theme="dark"] .highlight .kd { color: #DCC6E0 } /* Keyword.Declaration */
html[data-theme="dark"] .highlight .kn { color: #DCC6E0 } /* Keyword.Namespace */
html[data-theme="dark"] .highlight .kp { color: #DCC6E0 } /* Keyword.Pseudo */
html[data-theme="dark"] .highlight .kr { color: #DCC6E0 } /* Keyword.Reserved */
html[data-theme="dark"] .highlight .kt { color: #FFD900 } /* Keyword.Type */
html[data-theme="dark"] .highlight .ld { color: #FFD900 } /* Literal.Date */
html[data-theme="dark"] .highlight .m { color: #FFD900 } /* Literal.Number */
html[data-theme="dark"] .highlight .s { color: #ABE338 } /* Literal.String */
html[data-theme="dark"] .highlight .na { color: #FFD900 } /* Name.Attribute */
html[data-theme="dark"] .highlight .nb { color: #FFD900 } /* Name.Builtin */
html[data-theme="dark"] .highlight .nc { color: #00E0E0 } /* Name.Class */
html[data-theme="dark"] .highlight .no { color: #00E0E0 } /* Name.Constant */
html[data-theme="dark"] .highlight .nd { color: #FFD900 } /* Name.Decorator */
html[data-theme="dark"] .highlight .ni { color: #ABE338 } /* Name.Entity */
html[data-theme="dark"] .highlight .ne { color: #DCC6E0 } /* Name.Exception */
html[data-theme="dark"] .highlight .nf { color: #00E0E0 } /* Name.Function */
html[data-theme="dark"] .highlight .nl { color: #FFD900 } /* Name.Label */
html[data-theme="dark"] .highlight .nn { color: #F8F8F2 } /* Name.Namespace */
html[data-theme="dark"] .highlight .nx { color: #F8F8F2 } /* Name.Other */
html[data-theme="dark"] .highlight .py { color: #00E0E0 } /* Name.Property */
html[data-theme="dark"] .highlight .nt { color: #00E0E0 } /* Name.Tag */
html[data-theme="dark"] .highlight .nv { color: #FFA07A } /* Name.Variable */
html[data-theme="dark"] .highlight .ow { color: #DCC6E0 } /* Operator.Word */
html[data-theme="dark"] .highlight .pm { color: #F8F8F2 } /* Punctuation.Marker */
html[data-theme="dark"] .highlight .w { color: #F8F8F2 } /* Text.Whitespace */
html[data-theme="dark"] .highlight .mb { color: #FFD900 } /* Literal.Number.Bin */
html[data-theme="dark"] .highlight .mf { color: #FFD900 } /* Literal.Number.Float */
html[data-theme="dark"] .highlight .mh { color: #FFD900 } /* Literal.Number.Hex */
html[data-theme="dark"] .highlight .mi { color: #FFD900 } /* Literal.Number.Integer */
html[data-theme="dark"] .highlight .mo { color: #FFD900 } /* Literal.Number.Oct */
html[data-theme="dark"] .highlight .sa { color: #ABE338 } /* Literal.String.Affix */
html[data-theme="dark"] .highlight .sb { color: #ABE338 } /* Literal.String.Backtick */
html[data-theme="dark"] .highlight .sc { color: #ABE338 } /* Literal.String.Char */
html[data-theme="dark"] .highlight .dl { color: #ABE338 } /* Literal.String.Delimiter */
html[data-theme="dark"] .highlight .sd { color: #ABE338 } /* Literal.String.Doc */
html[data-theme="dark"] .highlight .s2 { color: #ABE338 } /* Literal.String.Double */
html[data-theme="dark"] .highlight .se { color: #ABE338 } /* Literal.String.Escape */
html[data-theme="dark"] .highlight .sh { color: #ABE338 } /* Literal.String.Heredoc */
html[data-theme="dark"] .highlight .si { color: #ABE338 } /* Literal.String.Interpol */
html[data-theme="dark"] .highlight .sx { color: #ABE338 } /* Literal.String.Other */
html[data-theme="dark"] .highlight .sr { color: #FFA07A } /* Literal.String.Regex */
html[data-theme="dark"] .highlight .s1 { color: #ABE338 } /* Literal.String.Single */
html[data-theme="dark"] .highlight .ss { color: #00E0E0 } /* Literal.String.Symbol */
html[data-theme="dark"] .highlight .bp { color: #FFD900 } /* Name.Builtin.Pseudo */
html[data-theme="dark"] .highlight .fm { color: #00E0E0 } /* Name.Function.Magic */
html[data-theme="dark"] .highlight .vc { color: #FFA07A } /* Name.Variable.Class */
html[data-theme="dark"] .highlight .vg { color: #FFA07A } /* Name.Variable.Global */
html[data-theme="dark"] .highlight .vi { color: #FFA07A } /* Name.Variable.Instance */
html[data-theme="dark"] .highlight .vm { color: #FFD900 } /* Name.Variable.Magic */
html[data-theme="dark"] .highlight .il { color: #FFD900 } /* Literal.Number.Integer.Long */