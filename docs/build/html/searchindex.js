Search.setIndex({"alltitles": {"API Reference": [[0, null]], "ARIMA Module": [[0, "arima-module"]], "Advanced Examples": [[2, "advanced-examples"]], "Advanced Features": [[6, "advanced-features"]], "Architecture": [[1, "architecture"]], "Basic Concepts": [[6, "basic-concepts"]], "Basic Examples": [[2, "basic-examples"]], "Bayesian Module": [[0, "bayesian-module"]], "Bibliography": [[5, "bibliography"]], "Changelog": [[5, "changelog"]], "Contributing": [[1, "contributing"]], "Core Module": [[0, "core-module"]], "Developer Guide": [[1, null]], "Examples": [[2, null]], "Features": [[4, "features"]], "Financial Examples": [[2, "financial-examples"]], "Frequency Module": [[0, "frequency-module"]], "Getting Started": [[3, null]], "Glossary": [[5, "glossary"]], "Installation": [[3, "installation"]], "Navigation": [[4, "navigation"]], "Next Steps": [[3, "next-steps"]], "Quick Example": [[4, "quick-example"]], "Quick Start": [[3, "quick-start"]], "Reference": [[5, null]], "Release Process": [[1, "release-process"]], "Scientific Examples": [[2, "scientific-examples"]], "Series Module": [[0, "series-module"]], "Statistical Analysis": [[6, "statistical-analysis"]], "Statistics Module": [[0, "statistics-module"]], "TOL Python Documentation": [[4, null]], "Testing": [[1, "testing"]], "Time Series Operations": [[6, "time-series-operations"]], "User Guide": [[6, null]]}, "docnames": ["api-reference/index", "developer-guide/index", "examples/index", "getting-started/index", "index", "reference/index", "user-guide/index"], "envversion": {"nbsphinx": 4, "sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.viewcode": 1, "sphinxext.opengraph": 1}, "filenames": ["api-reference/index.rst", "developer-guide/index.rst", "examples/index.rst", "getting-started/index.rst", "index.rst", "reference/index.rst", "user-guide/index.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [3, 4, 5], "1": 3, "100": 4, "102": 4, "103": 4, "105": 4, "107": 4, "2": [0, 1, 2, 3, 6], "2f": 4, "3": [3, 4], "4": 3, "5": 3, "7": 3, "Or": 3, "abbrevi": 5, "academ": 5, "acf": 4, "ad": [0, 2], "addit": 5, "advanc": 4, "all": [0, 4], "analysi": [0, 2, 4, 5], "api": [3, 4], "applic": 2, "architectur": 4, "arima": [2, 4, 6], "arithmet": 4, "arrai": 4, "auto": 0, "autocorrel": 6, "automat": 4, "averag": [4, 6], "basic": 3, "bayesian": [2, 4, 6], "begin": 4, "best": [1, 4], "binari": 4, "break": 5, "bring": 4, "bug": 5, "calcul": 2, "capabl": [0, 2], "case": 2, "cd": 3, "chang": [5, 6], "check": 3, "checklist": 1, "class": [0, 4, 6], "classic": [4, 6], "clone": 3, "code": 1, "com": 3, "compat": 4, "complet": [0, 4], "complex": 6, "comprehens": [3, 4, 6], "comput": 2, "concept": [1, 5], "consider": 1, "contain": [1, 2, 5, 6], "content": [1, 6], "continu": 1, "contribut": 4, "convers": [4, 6], "core": 1, "coverag": 1, "creat": [2, 3, 4], "csv": 4, "custom": 2, "daili": 4, "data": [2, 3, 4, 6], "date": [0, 3, 4, 6], "decomposit": 6, "definit": 5, "demonstr": 2, "descript": 6, "design": 1, "detail": [0, 3, 4, 6], "develop": [3, 4, 5], "differ": [4, 6], "digit": 4, "distribut": 1, "document": [0, 1, 2, 3, 5, 6], "domain": [0, 2, 4, 6], "e": 3, "each": 5, "effect": 6, "end": 4, "environ": 1, "estim": 0, "exampl": 3, "exist": [1, 6], "experiment": 2, "explor": [3, 6], "export": 2, "f": [3, 4], "fft": 4, "file": [1, 6], "filter": [0, 4], "first": [3, 4], "first_dat": [3, 4], "fix": 5, "flexibl": 4, "forecast": [0, 2], "format": 4, "frequenc": [2, 4, 6], "from": [1, 3, 4, 6], "full": 4, "function": [0, 2, 4, 6], "fundament": 6, "gener": 0, "get": [2, 4], "git": 3, "github": 3, "guid": [3, 4, 5], "guidelin": [1, 4], "handl": 6, "help": 3, "here": [3, 4], "histori": 5, "how": [1, 4, 6], "http": 3, "i": 4, "implement": 4, "import": [2, 3, 4], "improv": 5, "index": 0, "inform": 1, "instal": 4, "instruct": 4, "integr": [1, 4], "interact": 2, "intern": 4, "issu": 1, "json": 4, "jupyt": 2, "lag": [4, 6], "languag": [4, 5], "last": 3, "last_dat": [3, 4], "later": 3, "lead": [4, 6], "learn": 6, "len": 3, "length": 3, "librari": [4, 5], "literatur": 5, "m": 3, "ma_3": 4, "main": 0, "manag": 1, "manipul": [2, 6], "marinucci": 3, "markdown": [1, 6], "master": 6, "materi": 5, "mcmc": 0, "mean": [3, 4], "method": 5, "metric": 2, "migrat": [0, 1, 2, 5, 6], "miss": [4, 6], "model": [0, 2, 4, 6], "modul": [1, 4], "monthli": 4, "more": 4, "move": [4, 6], "moving_averag": 4, "nativ": 4, "new": [1, 4, 5], "note": 5, "notebook": 2, "np": 4, "number": 1, "numpi": 4, "o": 4, "oper": [0, 2, 3, 4], "optim": [2, 6], "organ": 1, "orient": 4, "out": 3, "pacf": 4, "packag": 1, "panda": 4, "partial": 6, "pct_chang": 4, "percentag": 6, "perform": [1, 2, 4, 6], "phase": [0, 1, 2, 6], "pip": 3, "portfolio": 2, "power": 4, "practic": [1, 2, 4], "price": 2, "principl": 1, "print": [3, 4], "procedur": 1, "process": 2, "project": 1, "provid": 0, "public": 0, "pull": 1, "python": [0, 1, 2, 3, 5, 6], "quarterli": 4, "quickli": 3, "read": 3, "real": 3, "refer": [3, 4], "relat": 5, "releas": 5, "report": 1, "represent": 6, "request": 1, "requir": [1, 3], "resampl": 6, "resourc": 5, "return": [2, 4], "risk": 2, "root": [4, 6], "run": [1, 3], "season": 6, "section": [0, 1, 2, 3, 5, 6], "select": 4, "sentinel": 4, "seri": [2, 3, 4, 5], "set": 1, "signal": 2, "simpl": [2, 3], "smooth": 6, "specif": 5, "start": [2, 4], "stationar": 6, "statist": [2, 4, 5], "std": 4, "stock": 2, "structur": 1, "style": 1, "submit": 1, "suit": 1, "support": 4, "system": 4, "technic": [1, 5], "techniqu": 6, "term": 5, "terminologi": 5, "test": [0, 4, 6], "thi": [0, 1, 2, 3, 5, 6], "time": [0, 2, 3, 4, 5], "timeset": [0, 6], "tol": [0, 1, 2, 3, 5, 6], "tol_python": [3, 4], "transform": 6, "tutori": [2, 3, 4, 6], "type": 6, "unit": [4, 6], "up": [1, 3], "updat": 1, "us": [2, 3, 6], "user": [3, 4, 5], "valu": [4, 6], "version": [1, 5], "visual": 2, "volatil": 4, "want": 1, "weekli": 4, "welcom": [3, 4], "what": 5, "who": 1, "work": 5, "world": 3, "write": 1, "y2023m01d01": [3, 4], "y2023m01d05": [3, 4], "y2023m01d15": 4, "yearli": 4, "you": [2, 3], "your": 4}, "titles": ["API Reference", "Developer Guide", "Examples", "Getting Started", "TOL Python Documentation", "Reference", "User Guide"], "titleterms": {"advanc": [2, 6], "analysi": 6, "api": 0, "architectur": 1, "arima": 0, "basic": [2, 6], "bayesian": 0, "bibliographi": 5, "changelog": 5, "concept": 6, "contribut": 1, "core": 0, "develop": 1, "document": 4, "exampl": [2, 4], "featur": [4, 6], "financi": 2, "frequenc": 0, "get": 3, "glossari": 5, "guid": [1, 6], "instal": 3, "modul": 0, "navig": 4, "next": 3, "oper": 6, "process": 1, "python": 4, "quick": [3, 4], "refer": [0, 5], "releas": 1, "scientif": 2, "seri": [0, 6], "start": 3, "statist": [0, 6], "step": 3, "test": 1, "time": 6, "tol": 4, "user": 6}})