#!/usr/bin/env python3
"""
Setup script for TOL Python package
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as fh:
            return fh.read()
    except FileNotFoundError:
        return "TOL Python - Time-Oriented Language implementation in Python"

# Read requirements
def read_requirements():
    requirements = []
    try:
        with open("requirements.txt", "r") as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]
    except FileNotFoundError:
        # Default requirements
        requirements = [
            "numpy>=1.18.0",
            "scipy>=1.5.0",
            "pandas>=1.0.0",
            "matplotlib>=3.0.0"
        ]
    return requirements

setup(
    name="tol_python",
    version="0.2.0",
    author="TOL Python Team",
    author_email="",
    description="Python implementation of TOL (Time-Oriented Language) for time series analysis",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/tol_python",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering",
        "Topic :: Scientific/Engineering :: Mathematics",
        "Topic :: Office/Business :: Financial",
    ],
    python_requires=">=3.7",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov",
            "black",
            "mypy",
            "flake8"
        ],
        "docs": [
            "sphinx",
            "sphinx-rtd-theme"
        ],
        "full": [
            "numba",
            "cvxpy",
            "statsmodels"
        ]
    },
    entry_points={
        "console_scripts": [
            "tol_python=tol_python.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "tol_python": [
            "data/*.csv",
            "data/*.json", 
            "docs/*.md"
        ]
    },
    zip_safe=False,
)