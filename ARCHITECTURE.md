# TOL Python Architecture Documentation

## Overview

TOL Python is a comprehensive port of the TOL (Time-Oriented Language) time series library from C++ to Python. The architecture maintains compatibility with TOL's core concepts while leveraging Python's ecosystem for enhanced functionality.

## Design Principles

### 1. TOL Compatibility
- **API Preservation**: Core TOL functions maintain similar names and behavior
- **Date Format**: Uses TOL's `yYYYYmMMdDD` date format with Begin/End sentinels
- **Data Structures**: Serie class mirrors TOL's BSerie functionality
- **Operations**: Mathematical and statistical operations maintain TOL semantics

### 2. Python Integration
- **NumPy Foundation**: Efficient array operations using NumPy
- **Pandas Compatibility**: Seamless conversion to/from pandas DataFrames
- **Pythonic API**: Method chaining and context managers where appropriate
- **Type Hints**: Full type annotation support

### 3. Extensibility
- **Modular Design**: Clear separation of concerns across modules
- **Plugin Architecture**: Easy addition of new statistical methods
- **Flexible I/O**: Multiple serialization formats and data sources
- **Performance Optimization**: NumPy/Numba integration for speed

## Core Architecture

```
TOL Python Architecture
├── Core Layer (Fundamental Types)
│   ├── Date System
│   ├── TimeSet Management
│   └── Data Storage
├── Series Layer (Time Series Operations)
│   ├── Serie Class
│   ├── Operations
│   └── Transformations
├── Analysis Layer (Statistical Methods)
│   ├── Descriptive Statistics
│   ├── Time Series Analysis
│   └── Hypothesis Testing
├── Advanced Layer (Specialized Modules)
│   ├── ARIMA Modeling
│   ├── Bayesian Methods
│   ├── Frequency Domain
│   └── Complex Series
└── I/O Layer (Data Exchange)
    ├── Serialization
    ├── Format Conversion
    └── External Interfaces
```

## Module Breakdown

### Core Module (`core/`)

#### Purpose
Provides fundamental data types and infrastructure for time series operations.

#### Components

**dates.py**
```python
# Date representation and TimeSet management
class Date:
    """TOL-compatible date with Begin/End sentinels"""
    
class TimeSet:
    """Dating patterns for time series"""
    
class TimeSetBase:
    """Abstract base for custom dating patterns"""
```

**data.py**
```python
# Efficient data storage with missing value support
class Data:
    """Optimized storage for time series data"""
```

#### Key Features
- **Sentinel Support**: `Date.Begin` and `Date.End` for unbounded ranges
- **Flexible Dating**: Multiple TimeSet types (daily, monthly, custom)
- **Memory Efficiency**: Optimized storage for sparse and dense data
- **Type Safety**: Comprehensive validation and error handling

#### Design Patterns
```python
# Factory pattern for TimeSet creation
def create_timeset(dating_type: str, **kwargs) -> TimeSetBase:
    timeset_classes = {
        'daily': DayTimeSet,
        'monthly': MonthTimeSet,
        'yearly': YearTimeSet,
        'last_day_of_month': LastDayOfMonthTimeSet,
        'specific_dates': SpecificDatesTimeSet
    }
    return timeset_classes[dating_type](**kwargs)

# Strategy pattern for date operations
class DateStrategy:
    def next_date(self, date: Date) -> Date: pass
    def prev_date(self, date: Date) -> Date: pass
```

### Series Module (`series/`)

#### Purpose
Core time series functionality with TOL-compatible operations.

#### Architecture
```
series/
├── serie.py           # Main Serie class
├── corrected_serie.py # Enhanced implementation
└── operations.py      # Series operations
```

#### Class Hierarchy
```python
class SerieBase:
    """Abstract base for all series types"""
    
class Serie(SerieBase):
    """Main time series class"""
    
class IndicatorSerie(Serie):
    """Boolean indicator series"""
    
class SerieTable:
    """Multiple series container"""
```

#### Data Flow
```
User Input → Validation → Date Conversion → Data Storage → Operations → Output
     ↓           ↓             ↓              ↓           ↓         ↓
  Raw Data → TOL Format → TimeSet Index → NumPy Array → Result → Format
```

#### Key Algorithms

**Date Indexing**
```python
def _date_to_index(self, date: Union[str, Date]) -> int:
    """Convert TOL date to array index"""
    if isinstance(date, str):
        date = Date(date)
    
    # Calculate offset from first date
    offset = self.dating.calculate_offset(self.first_date, date)
    
    if 0 <= offset < len(self.data):
        return offset
    else:
        raise IndexError(f"Date {date} outside series range")
```

**Missing Value Handling**
```python
def _handle_missing_values(self, operation):
    """Apply operation while handling missing values"""
    mask = ~np.isnan(self.data.values)
    
    if np.any(mask):
        return operation(self.data.values[mask])
    else:
        return np.nan
```

### Statistics Module (`stats/`)

#### Purpose
Statistical analysis and hypothesis testing for time series.

#### Architecture
```
stats/
├── statistics.py      # Descriptive statistics
├── test_functions.py  # Hypothesis tests
└── tests.py          # Test implementations
```

#### Statistical Framework
```python
class StatisticalMethod:
    """Base class for statistical methods"""
    
    def __init__(self, serie: Serie):
        self.serie = serie
        self.cache = {}
    
    def compute(self, **kwargs):
        """Template method for statistical computation"""
        key = self._generate_cache_key(**kwargs)
        if key not in self.cache:
            self.cache[key] = self._compute_implementation(**kwargs)
        return self.cache[key]
    
    def _compute_implementation(self, **kwargs):
        """Override in subclasses"""
        raise NotImplementedError
```

#### Performance Optimization
```python
# Lazy evaluation for expensive computations
@property
def autocorrelation(self):
    if not hasattr(self, '_autocorrelation'):
        self._autocorrelation = self._compute_autocorrelation()
    return self._autocorrelation

# Vectorized operations using NumPy
def rolling_statistics(self, window: int, func: Callable):
    """Efficient rolling window calculations"""
    return np.array([
        func(self.data[i:i+window]) 
        for i in range(len(self.data) - window + 1)
    ])
```

### ARIMA Module (`arima/`)

#### Purpose
Advanced time series modeling with ARIMA family models.

#### Architecture
```
arima/
├── arima_model.py     # Core ARIMA implementation
├── auto_arima.py      # Automatic model selection
├── estimation.py      # Parameter estimation
├── diagnostics.py     # Model diagnostics
└── kalman.py         # Kalman filtering
```

#### Model Pipeline
```
Data Input → Preprocessing → Model Selection → Estimation → Diagnostics → Forecasting
     ↓            ↓              ↓              ↓            ↓            ↓
  Serie → Stationarity → ARIMA(p,d,q) → MLE/OLS → Residual → Predictions
```

#### State Space Representation
```python
class StateSpaceModel:
    """State space representation for ARIMA models"""
    
    def __init__(self, arima_order):
        self.p, self.d, self.q = arima_order
        self._setup_matrices()
    
    def _setup_matrices(self):
        """Initialize state space matrices"""
        # Transition matrix
        self.F = self._build_transition_matrix()
        # Observation matrix  
        self.H = self._build_observation_matrix()
        # Error covariance
        self.Q = self._build_error_covariance()
    
    def kalman_filter(self, observations):
        """Kalman filtering for likelihood computation"""
        # Implementation of Kalman filter
        pass
```

#### Estimation Algorithms
```python
class MaximumLikelihoodEstimator:
    """MLE estimation for ARIMA parameters"""
    
    def estimate(self, serie: Serie, order: Tuple[int, int, int]):
        """Estimate ARIMA parameters using MLE"""
        
        def log_likelihood(params):
            model = self._build_model(params, order)
            return model.log_likelihood(serie)
        
        # Optimization using scipy
        from scipy.optimize import minimize
        result = minimize(
            fun=lambda x: -log_likelihood(x),
            x0=self._initial_parameters(order),
            method='BFGS'
        )
        
        return ARIMAResult(result.x, result.fun, result)
```

### Bayesian Module (`bayesian/`)

#### Purpose
Bayesian inference for time series models with MCMC estimation.

#### Architecture
```
bayesian/
├── arima.py           # Bayesian ARIMA
├── core.py            # Bayesian infrastructure
├── priors.py          # Prior distributions
├── model_selection.py # Bayesian model selection
└── mcmc/             # MCMC algorithms
    ├── gibbs.py       # Gibbs sampling
    ├── metropolis.py  # Metropolis-Hastings
    ├── arms.py        # Adaptive rejection sampling
    └── diagnostics.py # MCMC diagnostics
```

#### Bayesian Framework
```python
class BayesianModel:
    """Base class for Bayesian time series models"""
    
    def __init__(self, priors: Dict[str, Distribution]):
        self.priors = priors
        self.posterior_samples = None
    
    def sample_posterior(self, data: Serie, n_samples: int = 1000):
        """Sample from posterior using MCMC"""
        sampler = self._create_sampler()
        self.posterior_samples = sampler.sample(data, n_samples)
        return self.posterior_samples
    
    def posterior_predictive(self, n_steps: int):
        """Generate posterior predictive samples"""
        predictions = []
        for sample in self.posterior_samples:
            pred = self._predict_with_parameters(sample, n_steps)
            predictions.append(pred)
        return np.array(predictions)
```

#### MCMC Implementation
```python
class GibbsSampler:
    """Gibbs sampling for Bayesian ARIMA"""
    
    def sample(self, data: Serie, n_samples: int):
        """Main sampling loop"""
        samples = []
        current_state = self._initialize_state(data)
        
        for i in range(n_samples):
            # Sample each parameter conditional on others
            current_state = self._sample_ar_params(data, current_state)
            current_state = self._sample_ma_params(data, current_state)
            current_state = self._sample_variance(data, current_state)
            
            if i >= self.burn_in:
                samples.append(current_state.copy())
        
        return np.array(samples)
```

### Frequency Module (`frequency/`)

#### Purpose
Frequency domain analysis and digital signal processing.

#### Architecture
```
frequency/
├── fft_ops.py         # FFT operations
└── filters.py         # Digital filters
```

#### FFT Pipeline
```
Time Domain → Windowing → FFT → Frequency Analysis → IFFT → Time Domain
     ↓           ↓         ↓          ↓              ↓         ↓
   Serie → Preprocessing → Complex → Power Spectrum → Filter → Serie
```

#### Filter Design
```python
class FrequencyFilter:
    """Base class for frequency domain filters"""
    
    def __init__(self, filter_type: str, **params):
        self.filter_type = filter_type
        self.params = params
        self._design_filter()
    
    def _design_filter(self):
        """Design filter coefficients"""
        from scipy import signal
        
        if self.filter_type == 'butterworth':
            self.coefficients = signal.butter(**self.params)
        elif self.filter_type == 'chebyshev':
            self.coefficients = signal.cheby1(**self.params)
    
    def apply(self, serie: Serie) -> Serie:
        """Apply filter to time series"""
        filtered_data = signal.filtfilt(*self.coefficients, serie.values())
        return Serie(data=filtered_data, 
                    first_date=serie.first_date,
                    last_date=serie.last_date,
                    dating=serie.dating)
```

### I/O Module (`io/`)

#### Purpose
Data serialization and external format support.

#### Architecture
```
io/
└── serialize.py       # Serialization functions
```

#### Serialization Framework
```python
class SerieSerializer:
    """Pluggable serialization framework"""
    
    _formats = {}
    
    @classmethod
    def register_format(cls, name: str, serializer):
        """Register new serialization format"""
        cls._formats[name] = serializer
    
    @classmethod
    def save(cls, serie: Serie, filename: str, format: str = 'json'):
        """Save serie in specified format"""
        if format not in cls._formats:
            raise ValueError(f"Unknown format: {format}")
        
        serializer = cls._formats[format]
        return serializer.save(serie, filename)
    
    @classmethod  
    def load(cls, filename: str, format: str = 'json'):
        """Load serie from specified format"""
        if format not in cls._formats:
            raise ValueError(f"Unknown format: {format}")
        
        serializer = cls._formats[format]
        return serializer.load(filename)
```

## Data Flow Architecture

### 1. Creation Pipeline
```
User Input → Validation → Date Processing → Data Storage → Serie Object
     ↓           ↓             ↓              ↓              ↓
  Parameters → Type Check → TimeSet Init → NumPy Array → Ready Serie
```

### 2. Operation Pipeline
```
Serie Input → Operation → Result Processing → Output Serie
     ↓           ↓              ↓              ↓
  Validation → Compute → Missing Value → New Serie
```

### 3. Analysis Pipeline
```
Serie Input → Statistical Method → Cache Check → Computation → Result
     ↓              ↓                ↓             ↓           ↓
  Preprocessing → Method Select → Cache Hit → Algorithm → Output
```

## Performance Considerations

### 1. Memory Management
- **Lazy Evaluation**: Defer expensive computations until needed
- **Copy-on-Write**: Share data between series when possible
- **Memory Pools**: Reuse allocated arrays for temporary operations
- **Sparse Storage**: Efficient storage for series with many missing values

### 2. Computational Optimization
- **Vectorization**: Use NumPy operations instead of Python loops
- **Numba JIT**: Compile performance-critical functions
- **Caching**: Cache expensive computations with intelligent invalidation
- **Parallel Processing**: Use multiprocessing for independent operations

### 3. Scaling Strategies
```python
# Memory-efficient operations for large series
def process_large_serie(serie: Serie, chunk_size: int = 10000):
    """Process large series in chunks"""
    results = []
    for i in range(0, len(serie), chunk_size):
        chunk = serie[i:i + chunk_size]
        result = expensive_operation(chunk)
        results.append(result)
    return combine_results(results)

# Parallel processing for multiple series
from concurrent.futures import ProcessPoolExecutor

def parallel_analysis(series_list: List[Serie]):
    """Analyze multiple series in parallel"""
    with ProcessPoolExecutor() as executor:
        futures = [executor.submit(analyze_serie, s) for s in series_list]
        results = [f.result() for f in futures]
    return results
```

## Error Handling Strategy

### 1. Exception Hierarchy
```python
class TOLError(Exception):
    """Base exception for TOL Python"""
    
class TOLDateError(TOLError):
    """Date-related errors"""
    
class TOLSerieError(TOLError):
    """Serie operation errors"""
    
class TOLTimeSetError(TOLError):
    """TimeSet configuration errors"""
    
class TOLStatisticsError(TOLError):
    """Statistical computation errors"""
```

### 2. Error Recovery
```python
class RobustOperation:
    """Robust wrapper for operations with fallbacks"""
    
    def __init__(self, primary_func, fallback_func=None):
        self.primary_func = primary_func
        self.fallback_func = fallback_func
    
    def execute(self, *args, **kwargs):
        try:
            return self.primary_func(*args, **kwargs)
        except Exception as e:
            if self.fallback_func:
                return self.fallback_func(*args, **kwargs)
            else:
                raise
```

## Testing Architecture

### 1. Test Structure
```
tests/
├── unit/              # Unit tests for individual components
├── integration/       # Integration tests for workflows
├── performance/       # Performance benchmarks
└── compatibility/     # TOL compatibility tests
```

### 2. Test Categories
- **Unit Tests**: Individual class and function testing
- **Integration Tests**: End-to-end workflow testing
- **Performance Tests**: Benchmarking and regression testing
- **Compatibility Tests**: TOL equivalence verification

### 3. Testing Framework
```python
class TOLTestCase(unittest.TestCase):
    """Base test case with TOL-specific utilities"""
    
    def assertSerieEqual(self, serie1: Serie, serie2: Serie, tolerance=1e-10):
        """Assert two series are equal within tolerance"""
        self.assertEqual(len(serie1), len(serie2))
        np.testing.assert_allclose(
            serie1.values(), serie2.values(), 
            rtol=tolerance, atol=tolerance
        )
    
    def create_test_serie(self, length=100, pattern='random'):
        """Create test series with specified pattern"""
        if pattern == 'random':
            data = np.random.randn(length)
        elif pattern == 'trend':
            data = np.arange(length) + np.random.randn(length) * 0.1
        elif pattern == 'seasonal':
            data = np.sin(np.arange(length) * 2 * np.pi / 12)
        
        return Serie(data=data, 
                    first_date="y2020m01d01", 
                    last_date=f"y2020m{length//30 + 1:02d}d{length%30 + 1:02d}")
```

This architecture provides a solid foundation for time series analysis while maintaining compatibility with the original TOL language and enabling future extensions.