# TOL Python API Reference

## Core Classes

### Serie Class

The main time series class providing TOL-compatible functionality.

#### Constructor

```python
Serie(data=None, first_date=None, last_date=None, dating=None, name=None)
```

**Parameters:**
- `data` (list/array): Time series data values
- `first_date` (str/Date): Starting date in TOL format (e.g., "y2023m01d01")
- `last_date` (str/Date): Ending date in TOL format
- `dating` (TimeSet): Dating pattern for the series
- `name` (str): Optional series name

#### Methods

##### Data Access
- `__getitem__(key)`: Access value by date or index
- `__setitem__(key, value)`: Set value by date or index
- `__len__()`: Get series length
- `values()`: Get data values as array
- `dates()`: Get date range

##### Time Operations
- `lag(periods=1)`: Lag series by specified periods
- `lead(periods=1)`: Lead series by specified periods
- `diff(periods=1)`: Calculate differences
- `moving_average(window)`: Calculate moving average
- `sub_ser(start_date, end_date)`: Extract subseries

##### Arithmetic Operations
- `__add__(other)`: Addition (+)
- `__sub__(other)`: Subtraction (-)
- `__mul__(other)`: Multiplication (*)
- `__truediv__(other)`: Division (/)
- `__pow__(other)`: Power (**)

##### Statistical Methods
- `sum()`: Sum of all values
- `mean()`: Mean value
- `std()`: Standard deviation
- `var()`: Variance
- `min()`: Minimum value
- `max()`: Maximum value
- `count()`: Count of non-missing values

##### I/O Operations
- `to_json(filename)`: Export to JSON
- `from_json(filename)`: Import from JSON (class method)
- `to_csv(filename)`: Export to CSV
- `from_csv(filename)`: Import from CSV (class method)
- `to_pandas()`: Convert to pandas DataFrame
- `from_pandas(df)`: Create from pandas DataFrame (class method)

### Date Class

TOL-compatible date representation with Begin/End sentinels.

#### Constructor

```python
Date(date_str=None, year=None, month=None, day=None)
```

**Parameters:**
- `date_str` (str): TOL date string (e.g., "y2023m01d15")
- `year` (int): Year component
- `month` (int): Month component
- `day` (int): Day component

#### Methods

##### Date Operations
- `add_days(days)`: Add days to date
- `add_months(months)`: Add months to date
- `add_years(years)`: Add years to date
- `to_string()`: Convert to TOL string format
- `to_datetime()`: Convert to Python datetime

##### Comparison
- `__eq__(other)`: Equality comparison
- `__lt__(other)`: Less than comparison
- `__le__(other)`: Less than or equal
- `__gt__(other)`: Greater than comparison
- `__ge__(other)`: Greater than or equal

##### Special Dates
- `Date.Begin`: Beginning sentinel
- `Date.End`: End sentinel

### TimeSet Class

Defines dating patterns for time series.

#### Constructor

```python
TimeSet(dating_type, **kwargs)
```

**Parameters:**
- `dating_type` (str): Type of dating ("daily", "monthly", "yearly", etc.)
- `**kwargs`: Additional parameters specific to dating type

#### Dating Types

##### Basic Types
- `"daily"`: Daily frequency
- `"weekly"`: Weekly frequency
- `"monthly"`: Monthly frequency
- `"quarterly"`: Quarterly frequency
- `"yearly"`: Yearly frequency

##### Advanced Types
- `"last_day_of_month"`: Last business day of month
- `"specific_dates"`: Custom date list
- `"periodic"`: Custom periodic patterns

#### Methods

- `contains(date)`: Check if date is in timeset
- `next_date(date)`: Get next date in sequence
- `prev_date(date)`: Get previous date in sequence
- `dates_in_range(start, end)`: Get all dates in range

## Statistics Module

### SerieStatistics Class

Advanced statistical functions for time series.

#### Methods

##### Descriptive Statistics
- `autocorrelation(lags=None)`: Calculate autocorrelation
- `cross_correlation(other, lags=None)`: Cross-correlation with another series
- `seasonal_decompose()`: Seasonal decomposition
- `trend_analysis()`: Trend detection and analysis

##### Distribution Analysis
- `histogram(bins=None)`: Generate histogram
- `quantiles(q=None)`: Calculate quantiles
- `box_stats()`: Box plot statistics
- `normality_test()`: Test for normality

##### Time Series Specific
- `stationarity_test()`: Augmented Dickey-Fuller test
- `seasonality_test()`: Test for seasonality
- `changepoint_detection()`: Detect structural breaks

### StatisticalTests Class

Hypothesis testing for time series.

#### Methods

##### Unit Root Tests
- `adf_test(series)`: Augmented Dickey-Fuller test
- `kpss_test(series)`: KPSS stationarity test
- `phillips_perron_test(series)`: Phillips-Perron test

##### Correlation Tests
- `ljung_box_test(series, lags=10)`: Ljung-Box test for autocorrelation
- `breusch_godfrey_test(series, lags=1)`: Breusch-Godfrey test

##### Normality Tests
- `jarque_bera_test(series)`: Jarque-Bera normality test
- `shapiro_wilk_test(series)`: Shapiro-Wilk test

## ARIMA Module

### ARIMAModel Class

ARIMA modeling and forecasting.

#### Constructor

```python
ARIMAModel(order=(1,1,1), seasonal_order=(0,0,0,0))
```

**Parameters:**
- `order` (tuple): (p, d, q) for ARIMA
- `seasonal_order` (tuple): (P, D, Q, s) for seasonal ARIMA

#### Methods

##### Model Fitting
- `fit(series, **kwargs)`: Fit model to series
- `predict(steps, confidence_interval=True)`: Generate predictions
- `forecast(series, steps)`: Out-of-sample forecast

##### Diagnostics
- `residual_analysis()`: Analyze model residuals
- `information_criteria()`: AIC, BIC, HQIC
- `ljung_box_residuals()`: Test residual autocorrelation

##### Model Selection
- `auto_arima(series, **kwargs)`: Automatic model selection
- `grid_search(series, p_range, d_range, q_range)`: Grid search for parameters

### AutoARIMA Class

Automatic ARIMA model selection.

#### Methods

- `select_model(series, max_p=5, max_d=2, max_q=5)`: Select best model
- `stepwise_search(series)`: Stepwise model selection
- `information_criterion_search(series)`: IC-based selection

## Bayesian Module

### BayesianARIMA Class

Bayesian ARIMA modeling with MCMC estimation.

#### Constructor

```python
BayesianARIMA(order=(1,1,1), priors=None)
```

#### Methods

##### MCMC Estimation
- `mcmc_estimate(series, n_samples=1000, burn_in=500)`: MCMC estimation
- `gibbs_sampler(series, **kwargs)`: Gibbs sampling
- `metropolis_hastings(series, **kwargs)`: Metropolis-Hastings

##### Bayesian Analysis
- `posterior_summary()`: Posterior statistics
- `credible_intervals(alpha=0.05)`: Bayesian credible intervals
- `bayes_factor(alternative_model)`: Model comparison

## Frequency Domain Module

### FrequencyDomain Class

Frequency domain analysis and filtering.

#### Methods

##### Fourier Analysis
- `fft(series)`: Fast Fourier Transform
- `ifft(frequency_data)`: Inverse FFT
- `power_spectrum(series)`: Power spectral density
- `periodogram(series)`: Periodogram estimation

##### Spectral Analysis
- `spectral_density(series, method='welch')`: Spectral density estimation
- `cross_spectrum(series1, series2)`: Cross-spectral analysis
- `coherence(series1, series2)`: Coherence function

### FrequencyFilters Class

Frequency domain filtering operations.

#### Methods

##### Filters
- `low_pass_filter(series, cutoff)`: Low-pass filter
- `high_pass_filter(series, cutoff)`: High-pass filter
- `band_pass_filter(series, low_cutoff, high_cutoff)`: Band-pass filter
- `band_stop_filter(series, low_cutoff, high_cutoff)`: Band-stop filter

##### Filter Design
- `butterworth_filter(order, cutoff, filter_type)`: Butterworth filter
- `chebyshev_filter(order, cutoff, ripple)`: Chebyshev filter
- `elliptic_filter(order, cutoff, ripple, attenuation)`: Elliptic filter

## Complex Series Module

### ComplexSerie Class

Complex-valued time series for frequency domain analysis.

#### Constructor

```python
ComplexSerie(real_part, imag_part=None, **kwargs)
```

#### Methods

##### Complex Operations
- `magnitude()`: Calculate magnitude
- `phase()`: Calculate phase
- `conjugate()`: Complex conjugate
- `real_part()`: Extract real component
- `imag_part()`: Extract imaginary component

##### Frequency Domain
- `from_fft(fft_result)`: Create from FFT result
- `to_polar()`: Convert to polar form
- `from_polar(magnitude, phase)`: Create from polar form

## I/O Module

### SerieIO Class

Input/output operations for time series data.

#### Methods

##### File Formats
- `save_binary(series, filename)`: Save in TOL binary format
- `load_binary(filename)`: Load TOL binary format
- `save_hdf5(series, filename, key)`: Save to HDF5
- `load_hdf5(filename, key)`: Load from HDF5

##### Database Operations
- `to_sql(series, connection, table_name)`: Export to SQL database
- `from_sql(query, connection)`: Import from SQL query
- `to_mongodb(series, collection)`: Export to MongoDB
- `from_mongodb(collection, query)`: Import from MongoDB

##### Cloud Storage
- `to_s3(series, bucket, key)`: Upload to AWS S3
- `from_s3(bucket, key)`: Download from AWS S3
- `to_gcs(series, bucket, blob_name)`: Upload to Google Cloud Storage
- `from_gcs(bucket, blob_name)`: Download from Google Cloud Storage

## Utility Functions

### Series Operations

```python
from tol_python.series import dat_ch, first_s, last_s, avr_s, sum_s_range
```

- `dat_ch(series, new_dating)`: Change series dating
- `first_s(series)`: Get first valid value
- `last_s(series)`: Get last valid value
- `avr_s(series, start_date=None, end_date=None)`: Calculate average
- `sum_s_range(series, start_date, end_date)`: Sum over date range

### Calendar Functions

```python
from tol_python.series import cal_ind
```

- `cal_ind(base_date, target_date, dating_type)`: Calculate calendar index

## Error Handling

### TOL Exceptions

- `TOLDateError`: Date parsing and validation errors
- `TOLTimeSetError`: TimeSet configuration errors
- `TOLSerieError`: Serie operation errors
- `TOLIOError`: Input/output operation errors
- `TOLStatisticsError`: Statistical calculation errors

### Usage Examples

```python
try:
    serie = Serie(data=[1, 2, 3], first_date="y2023m01d01", last_date="y2023m01d03")
    result = serie.mean()
except TOLSerieError as e:
    print(f"Serie error: {e}")
except TOLDateError as e:
    print(f"Date error: {e}")
```

## Constants and Enumerations

### DatingType Enumeration

```python
from tol_python.core import DatingType

DatingType.DAILY
DatingType.WEEKLY  
DatingType.MONTHLY
DatingType.QUARTERLY
DatingType.YEARLY
DatingType.CUSTOM
```

### Missing Value Handling

```python
from tol_python import MISSING_VALUE

# Check for missing values
if value == MISSING_VALUE:
    # Handle missing value
    pass
```

## Integration Examples

### NumPy Integration

```python
import numpy as np
from tol_python import Serie

# Create from NumPy array
data = np.random.randn(100)
serie = Serie(data=data, first_date="y2023m01d01", last_date="y2023m04d10")

# Convert to NumPy
array = serie.values()
```

### Pandas Integration

```python
import pandas as pd
from tol_python import Serie

# Create from pandas Series
df = pd.DataFrame({'values': [1, 2, 3]}, 
                  index=pd.date_range('2023-01-01', periods=3))
serie = Serie.from_pandas(df['values'])

# Convert to pandas
df_new = serie.to_pandas()
```

### Matplotlib Integration

```python
import matplotlib.pyplot as plt
from tol_python import Serie

serie = Serie(data=[1, 2, 3, 2, 1], first_date="y2023m01d01", last_date="y2023m01d05")

# Plot series
plt.figure(figsize=(10, 6))
plt.plot(serie.dates(), serie.values())
plt.title("TOL Serie Plot")
plt.show()
```