# Core dependencies
import numpy as np
import scipy as sp
import matplotlib.pyplot as plt

# TOL Python modules
from series import Serie
from core import Date, TimeSet
from stats import statistics, tests
from arima import ARIMA, ARIMAFactor
from bayesian import BayesianARIMA, MCMCConfig
from bayesian.priors import NormalPrior, InverseGammaPrior
# Test basic functionality
data = [1.2, 1.5, 1.3, 1.8, 1.6, 1.9, 1.7, 2.1]
serie = Serie(data=data)
print(f"Serie created successfully: {len(serie)} observations")
# Creating Series
from series import Serie
from core import Date, TimeSet

# Method 1: Simple data array
data = [10.5, 11.2, 10.8, 12.1, 11.9]
serie = Serie(data=data)

# Method 2: With explicit dates
from datetime import datetime
start_date = Date(datetime(2023, 1, 1))
end_date = Date(datetime(2023, 1, 5))
serie_dated = Serie(data=data, first_date=start_date, last_date=end_date)

# Method 3: With time frequency
monthly_dating = TimeSet("monthly")
serie_monthly = Serie(data=data, dating=monthly_dating)

print(f"Serie length: {len(serie)}")
print(f"First value: {serie[0]}")
print(f"Data type: {type(serie._data)}")
# TOL-style date creation
date1 = Date("y2023m01d15")  # January 15, 2023
date2 = Date("y2023m03")     # March 2023 (monthly)
date3 = Date("y2023")        # Year 2023

# Date arithmetic
next_day = date1.successor()
prev_day = date1.predecessor()
print(f"Next day: {next_day}")

# Date ranges
date_range = Date.range(date1, date2, TimeSet("daily"))
print(f"Days between: {len(date_range)}")
# Data with missing values (None or np.nan)
data_with_missing = [1.0, 2.0, None, 4.0, np.nan, 6.0]
serie = Serie(data=data_with_missing)

# Missing values are automatically handled
print(f"Valid observations: {serie.count_valid()}")
print(f"Has missing: {serie.has_missing()}")

# Statistical operations automatically exclude missing values
mean_value = serie.mean()
print(f"Mean (excluding missing): {mean_value}")
# Create sample series
ts1 = Serie(data=[1, 2, 3, 4, 5])
ts2 = Serie(data=[2, 4, 6, 8, 10])

# Basic arithmetic
sum_series = ts1 + ts2
diff_series = ts2 - ts1
product_series = ts1 * ts2
ratio_series = ts2 / ts1

print(f"Sum: {sum_series._data.to_numpy()}")
print(f"Ratio: {ratio_series._data.to_numpy()}")

# Scalar operations
scaled = ts1 * 2.5
shifted = ts1 + 10
print(f"Scaled: {scaled._data.to_numpy()}")
# Lag operations (fundamental for time series)
ts = Serie(data=[10, 15, 12, 18, 14, 20, 16])

# Create lags
lag1 = ts.lag(1)    # t-1
lag2 = ts.lag(2)    # t-2

# Differences (1-L operator in TOL)
diff1 = ts.diff(1)  # First difference
diff2 = ts.diff(2)  # Second difference

print(f"Original: {ts._data.to_numpy()}")
print(f"Lag 1: {lag1._data.to_numpy()}")
print(f"Diff 1: {diff1._data.to_numpy()}")

# Moving averages
ma3 = ts.moving_average(3)  # 3-period moving average
print(f"MA(3): {ma3._data.to_numpy()}")

# Extract portions of series (TOL's SubSer equivalent)
full_series = Serie(data=list(range(100)))

# Extract by index
subset1 = full_series.subseries(10, 50)

# Extract by date (if series has dates)
start_date = Date("y2023m01d01")
end_date = Date("y2023m12d31")
daily_series = Serie(
    data=np.random.randn(365), 
    first_date=start_date,
    dating=TimeSet("daily")
)

# Extract Q1 2023
q1_start = Date("y2023m01d01")
q1_end = Date("y2023m03d31")
q1_data = daily_series.subseries_by_date(q1_start, q1_end)

print(f"Q1 observations: {len(q1_data)}")
from stats import statistics

# Generate sample AR(1) data
np.random.seed(42)
n = 200
phi = 0.7
data = np.zeros(n)
for t in range(1, n):
    data[t] = phi * data[t-1] + np.random.normal(0, 1)

ts = Serie(data=data)

# Basic statistics
mean = statistics.mean(ts)
variance = statistics.variance(ts)
std_dev = statistics.std_dev(ts)
skewness = statistics.skewness(ts)
kurtosis = statistics.kurtosis(ts)

print(f"Mean: {mean:.4f}")
print(f"Std Dev: {std_dev:.4f}")
print(f"Skewness: {skewness:.4f}")
print(f"Kurtosis: {kurtosis:.4f}")

# Quantiles
median = statistics.quantile(ts, 0.5)
q25 = statistics.quantile(ts, 0.25)
q75 = statistics.quantile(ts, 0.75)
print(f"Median: {median:.4f}")
print(f"IQR: [{q25:.4f}, {q75:.4f}]")

# Autocorrelation function
acf_result = statistics.autocorr_function(ts, max_lag=20)
print(f"ACF lags 1-5: {acf_result[:5]}")

# Partial autocorrelation function  
pacf_result = statistics.partial_autocorr_function(ts, max_lag=20)
print(f"PACF lags 1-5: {pacf_result[:5]}")

# Cross-correlation between two series
ts2 = Serie(data=np.random.randn(200))
ccf_result = statistics.cross_correlation(ts, ts2, max_lag=10)
print(f"CCF at lag 0: {ccf_result[10]}")  # Middle element is lag 0

from stats import tests

# Normality tests
jb_stat, jb_pvalue = tests.jarque_bera_test(ts)
print(f"Jarque-Bera test: statistic={jb_stat:.4f}, p-value={jb_pvalue:.4f}")

# Serial correlation tests
lb_stat, lb_pvalue = tests.ljung_box_test(ts, lags=10)
print(f"Ljung-Box test: statistic={lb_stat:.4f}, p-value={lb_pvalue:.4f}")

# Unit root test
adf_stat, adf_pvalue = tests.augmented_dickey_fuller_test(ts)
print(f"ADF test: statistic={adf_stat:.4f}, p-value={adf_pvalue:.4f}")
if adf_pvalue < 0.05:
    print("Series appears to be stationary")
else:
    print("Series appears to have a unit root")

# Durbin-Watson test for autocorrelation
dw_stat = tests.durbin_watson_test(ts)
print(f"Durbin-Watson: {dw_stat:.4f}")

from arima import ARIMA, ARIMAFactor

# Define ARIMA model specification
# ARIMA(1,1,1) model
factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1)

# Create and fit model
arima_model = ARIMA(factor)
results = arima_model.fit(ts, method="mle")  # Maximum likelihood estimation

# Display results
print(results.summary())

# Get parameter estimates
print(f"AR coefficient: {results.ar_params[0]:.4f}")
print(f"MA coefficient: {results.ma_params[0]:.4f}")
print(f"Sigma-squared: {results.sigma2:.4f}")

# Information criteria
print(f"AIC: {results.aic:.4f}")
print(f"BIC: {results.bic:.4f}")

# SARIMA(1,1,1)(1,1,1)[12] model for monthly data
seasonal_factor = ARIMAFactor(
    ar_order=1, diff_order=1, ma_order=1,
    seasonal_ar=1, seasonal_diff=1, seasonal_ma=1,
    season_length=12
)

# Generate monthly data
monthly_data = np.random.randn(120)  # 10 years of monthly data
monthly_ts = Serie(data=monthly_data, dating=TimeSet("monthly"))

sarima_model = ARIMA(seasonal_factor)
seasonal_results = sarima_model.fit(monthly_ts)

print(f"Seasonal model: {seasonal_factor.get_model_string()}")
print(f"Regular AR: {seasonal_results.ar_params}")
print(f"Seasonal AR: {seasonal_results.seasonal_ar_params if hasattr(seasonal_results, 'seasonal_ar_params') else 'N/A'}")

# Generate forecasts
forecast_horizon = 12
forecasts, lower_bounds, upper_bounds = arima_model.forecast(forecast_horizon)

print(f"12-step ahead forecasts:")
for i in range(forecast_horizon):
    print(f"  Step {i + 1}: {forecasts[i]:.4f} [{lower_bounds[i]:.4f}, {upper_bounds[i]:.4f}]")

# Plot forecasts (if matplotlib available)
try:
    import matplotlib.pyplot as plt

    plt.figure(figsize=(12, 6))

    # Plot original data (last 50 points)
    original_data = ts._data.to_numpy()[-50:]
    plt.plot(range(len(original_data)), original_data, 'b-', label='Observed', linewidth=2)

    # Plot forecasts
    forecast_start = len(original_data)
    forecast_range = range(forecast_start, forecast_start + forecast_horizon)
    plt.plot(forecast_range, forecasts, 'r-', label='Forecast', linewidth=2)
    plt.fill_between(forecast_range, lower_bounds, upper_bounds,
                     alpha=0.3, color='red', label='95% CI')

    plt.legend()
    plt.title('ARIMA Forecast')
    plt.xlabel('Time')
    plt.ylabel('Value')
    plt.grid(True, alpha=0.3)
    plt.show()

except ImportError:
    print("Matplotlib not available for plotting")

# Residual analysis
residuals = results.residuals
print(f"Residual diagnostics:")

# Test residuals for remaining autocorrelation
lb_residuals = tests.ljung_box_test(residuals, lags=10)
print(f"  Ljung-Box on residuals: p-value = {lb_residuals[1]:.4f}")

# Test for normality
jb_residuals = tests.jarque_bera_test(residuals)
print(f"  Jarque-Bera on residuals: p-value = {jb_residuals[1]:.4f}")

# Plot residual ACF
residual_acf = statistics.autocorr_function(residuals, max_lag=20)
print(f"  Residual ACF (lags 1-5): {residual_acf[:5]}")

from bayesian import BayesianARIMA, MCMCConfig
from bayesian.priors import NormalPrior, InverseGammaPrior, HierarchicalPrior

# Basic Bayesian workflow
print("=== BAYESIAN ARIMA ESTIMATION ===")

# 1. Define priors for ARIMA parameters
# For AR(1) model: y_t = φ * y_{t-1} + ε_t, ε_t ~ N(0, σ²)

# Prior for AR coefficient: φ ~ N(0, 0.5²)
ar_prior = NormalPrior(mean=0.0, variance=0.25, name="AR_coefficient")

# Prior for error variance: σ² ~ InverseGamma(3, 2)
variance_prior = InverseGammaPrior(shape=3.0, scale=2.0, name="error_variance")

# Combine in dictionary
custom_priors = {
    'ar_0': ar_prior,
    'sigma2': variance_prior
}

print("Prior distributions specified:")
print(f"  AR coefficient: {ar_prior.name}")
print(f"  Error variance: {variance_prior.name}")

# Hierarchical prior example: AR coefficients with common variance
# φ_i ~ N(0, τ²), τ² ~ InverseGamma(3, 2)

# Hyperprior for common variance
hyperprior = InverseGammaPrior(shape=3.0, scale=2.0, name="AR_common_variance")

# Individual AR coefficient priors
ar1_hierarchical = HierarchicalPrior(
    parameter_prior=NormalPrior(0.0, 1.0, name="AR1"),
    hyperprior=hyperprior,
    name="AR1_hierarchical"
)

hierarchical_priors = {
    'ar_0': ar1_hierarchical,
    'sigma2': variance_prior
}

print("Hierarchical prior structure defined")
# Configure MCMC sampling (following TOL's BysMcmc structure)
mcmc_config = MCMCConfig(
    # Core MCMC settings
    mcmc_burnin=1000,  # Burn-in iterations
    mcmc_sample_length=5000,  # Post-burn-in samples
    mcmc_cache_length=500,  # Cache size

    # Convergence settings
    convergence_tolerance=1e-6,
    max_iterations=50000,

    # Diagnostic settings (TOL-style)
    raftery_diag_q=0.025,  # Quantile of interest
    raftery_diag_r=0.007,  # Desired margin of error
    raftery_diag_s=0.950,  # Probability of achieving precision
    acf_max_lag=20,  # Max lag for autocorrelation

    # Reproducibility
    random_seed=12345
)

print("MCMC configuration:")
print(f"  Burn-in: {mcmc_config.mcmc_burnin}")
print(f"  Samples: {mcmc_config.mcmc_sample_length}")
print(f"  Random seed: {mcmc_config.random_seed}")
# Create Bayesian ARIMA model
factor = ARIMAFactor(1, 0, 0)  # AR(1) specification

# Option 1: Use default priors
bayesian_arima = BayesianARIMA(factor, config=mcmc_config)

# Option 2: Use custom priors
bayesian_arima_custom = BayesianARIMA(
    factor=factor,
    priors=custom_priors,
    config=mcmc_config
)

# Fit the model to data
print("Fitting Bayesian ARIMA model...")
bayesian_results = bayesian_arima_custom.fit(ts)

print("Bayesian estimation completed!")
print(f"Posterior samples: {bayesian_results.n_samples}")
print(f"Burn-in: {bayesian_results.burn_in}")
# Examine posterior distributions
print("\n=== POSTERIOR ANALYSIS ===")

# AR coefficient posterior
ar_posterior_mean = bayesian_results.ar_posterior_mean[0]
ar_posterior_std = bayesian_results.ar_posterior_std[0]
ar_credible_interval = bayesian_results.ar_credible_intervals

print(f"AR Coefficient:")
print(f"  Posterior mean: {ar_posterior_mean:.4f}")
print(f"  Posterior std:  {ar_posterior_std:.4f}")
print(f"  95% Credible interval: [{ar_credible_interval[0][0]:.4f}, {ar_credible_interval[1][0]:.4f}]")

# Error variance posterior
variance_posterior_mean = bayesian_results.variance_posterior_mean
variance_posterior_std = bayesian_results.variance_posterior_std
variance_credible_interval = bayesian_results.variance_credible_interval

print(f"Error Variance:")
print(f"  Posterior mean: {variance_posterior_mean:.4f}")
print(f"  Posterior std:  {variance_posterior_std:.4f}")
print(f"  95% Credible interval: [{variance_credible_interval[0]:.4f}, {variance_credible_interval[1]:.4f}]")

# Check MCMC convergence
print("\n=== MCMC DIAGNOSTICS ===")

if bayesian_results.mcmc_diagnostics:
    for param_name, diagnostics in bayesian_results.mcmc_diagnostics.items():
        print(f"\n{param_name}:")

        # Effective sample size
        if 'effective_sample_size' in diagnostics:
            ess = diagnostics['effective_sample_size']
            print(f"  Effective Sample Size: {ess:.1f}")

        # Geweke diagnostic
        if 'geweke' in diagnostics:
            geweke = diagnostics['geweke']
            print(f"  Geweke Z-score: {geweke['z_score']:.3f}")
            print(f"  Geweke p-value: {geweke['p_value']:.3f}")

            if geweke['p_value'] > 0.05:
                print("  ✓ No evidence of non-convergence")
            else:
                print("  ⚠ Potential convergence issues")

# Effective sample size summary
if bayesian_results.effective_sample_sizes:
    print(f"\nEffective Sample Sizes:")
    for param, ess in bayesian_results.effective_sample_sizes.items():
        efficiency = ess / bayesian_results.n_samples
        print(f"  {param}: {ess:.1f} ({efficiency:.1%} efficiency)")

# Generate Bayesian forecasts with uncertainty
print("\n=== BAYESIAN FORECASTING ===")

forecast_steps = 10
forecast_mean, forecast_lower, forecast_upper = bayesian_arima_custom.forecast(
    steps=forecast_steps,
    alpha=0.05  # 95% credible intervals
)

print(f"Bayesian forecasts ({forecast_steps} steps ahead):")
for i in range(forecast_steps):
    ci_width = forecast_upper[i] - forecast_lower[i]
    print(f"  Step {i+1}: {forecast_mean[i]:7.4f} ± {ci_width/2:6.4f} [{forecast_lower[i]:7.4f}, {forecast_upper[i]:7.4f}]")

# Compare uncertainty over time
print(f"\nUncertainty evolution:")
print(f"  1-step CI width: {forecast_upper[0] - forecast_lower[0]:.4f}")
print(f"  5-step CI width: {forecast_upper[4] - forecast_lower[4]:.4f}")
print(f"  10-step CI width: {forecast_upper[9] - forecast_lower[9]:.4f}")

# Multi-lag ARMA model with hierarchical priors
factor_arma = ARIMAFactor(ar_order=2, diff_order=0, ma_order=1)

# Hierarchical priors for AR coefficients
ar_variance_prior = InverseGammaPrior(shape=3.0, scale=2.0, name="AR_variance")

arma_priors = {
    'ar_0': HierarchicalPrior(
        NormalPrior(0.0, 1.0, name="AR1"),
        ar_variance_prior,
        name="AR1_hierarchical"
    ),
    'ar_1': HierarchicalPrior(
        NormalPrior(0.0, 1.0, name="AR2"),
        ar_variance_prior,
        name="AR2_hierarchical"
    ),
    'ma_0': NormalPrior(0.0, 0.5, name="MA1"),
    'sigma2': InverseGammaPrior(3.0, 2.0, name="error_variance")
}

# Fit hierarchical ARMA model
bayesian_arma = BayesianARIMA(factor_arma, priors=arma_priors, config=mcmc_config)
print("Fitting hierarchical ARMA(2,0,1) model...")
# arma_results = bayesian_arma.fit(ts)  # Uncomment to run

from bayesian.model_selection import ModelComparisonCriteria, ARIMAModelSelection

# Compare multiple models using Bayesian criteria
print("\n=== MODEL COMPARISON ===")

# Fit several models for comparison
models_to_compare = [
    ARIMAFactor(1, 0, 0),  # AR(1)
    ARIMAFactor(2, 0, 0),  # AR(2)
    ARIMAFactor(1, 0, 1),  # ARMA(1,1)
]

comparison_results = []

for factor in models_to_compare:
    print(f"Fitting {factor.get_model_string()}...")

    # Use shorter MCMC for comparison
    quick_config = MCMCConfig(
        mcmc_burnin=500,
        mcmc_sample_length=1000,
        random_seed=42
    )

    bayes_model = BayesianARIMA(factor, config=quick_config)
    results = bayes_model.fit(ts)

    # Store results for comparison
    comparison_results.append({
        'model': factor.get_model_string(),
        'dic': results.dic if results.dic else float('inf'),
        'log_marginal_likelihood': results.log_marginal_likelihood if results.log_marginal_likelihood else -float(
            'inf'),
        'n_params': len(factor.get_polynomial_orders()[0]) + len(factor.get_polynomial_orders()[1]) + 1,
        'results': results
    })

# Display comparison
print(f"\nModel Comparison Results:")
print(f"{'Model':>12s} {'DIC':>8s} {'Log ML':>8s} {'Params':>7s}")
print("-" * 40)

for result in comparison_results:
    dic_str = f"{result['dic']:.2f}" if result['dic'] != float('inf') else "N/A"
    ml_str = f"{result['log_marginal_likelihood']:.2f}" if result['log_marginal_likelihood'] != -float('inf') else "N/A"
    print(f"{result['model']:>12s} {dic_str:>8s} {ml_str:>8s} {result['n_params']:>7d}")

# Find best model by DIC (lower is better)
valid_results = [r for r in comparison_results if r['dic'] != float('inf')]
if valid_results:
    best_model = min(valid_results, key=lambda x: x['dic'])
    print(f"\nBest model by DIC: {best_model['model']} (DIC = {best_model['dic']:.2f})")

# Automatic ARIMA model selection
print("\n=== AUTOMATIC MODEL SELECTION ===")

# Configure automatic selection
auto_selector = ARIMAModelSelection(
    max_p=3,  # Maximum AR order
    max_d=2,  # Maximum differencing
    max_q=2,  # Maximum MA order
    seasonal=False,  # No seasonal components
)

# For demonstration, we'll manually test a few models
# (Full automatic selection would be computationally intensive)

candidate_models = [
    ARIMAFactor(1, 0, 0),
    ARIMAFactor(1, 1, 0),
    ARIMAFactor(1, 0, 1),
]

print("Testing candidate models...")
best_dic = float('inf')
best_model_result = None

for factor in candidate_models:
    try:
        quick_config = MCMCConfig(
            mcmc_burnin=300,
            mcmc_sample_length=500,
            random_seed=123
        )

        bayes_model = BayesianARIMA(factor, config=quick_config)
        results = bayes_model.fit(ts)

        if results.dic and results.dic < best_dic:
            best_dic = results.dic
            best_model_result = (factor, results)

        print(f"  {factor.get_model_string()}: DIC = {results.dic:.2f if results.dic else 'N/A'}")

    except Exception as e:
        print(f"  {factor.get_model_string()}: Failed ({str(e)[:50]}...)")

if best_model_result:
    best_factor, best_results = best_model_result
    print(f"\nSelected model: {best_factor.get_model_string()}")
    print(f"DIC: {best_dic:.2f}")

# Compute Bayes factors for model comparison
from bayesian.model_selection import BayesFactorComparison

# Example: Compare AR(1) vs AR(2)
# (Using mock marginal likelihoods for demonstration)

# In practice, these would come from actual model fits
ml_ar1 = -150.5  # Log marginal likelihood for AR(1)
ml_ar2 = -152.1  # Log marginal likelihood for AR(2)

bf_result = BayesFactorComparison.compute_bayes_factor(ml_ar1, ml_ar2)

print(f"\nBayes Factor Analysis:")
print(f"Log Bayes Factor (AR1 vs AR2): {bf_result['log_bayes_factor']:.3f}")
print(f"Bayes Factor: {bf_result['bayes_factor']:.3f}")
print(f"Evidence: {bf_result['evidence_strength']}")
print(f"Interpretation: {bf_result['interpretation']}")

