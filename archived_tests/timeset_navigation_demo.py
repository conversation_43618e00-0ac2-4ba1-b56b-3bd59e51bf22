"""
Simple demo of TimeSet successor and predecessor methods
Shows how to navigate through dates using TimeSets
"""

from core import Date, TimeSet

def main():
    print("🧭 TimeSet Navigation Demo")
    print("=" * 40)
    
    # Basic navigation
    print("📅 Basic TimeSet Navigation")
    print("-" * 30)
    
    daily = TimeSet("daily")
    start_date = Date("y2023m06d15")
    
    print(f"Starting date: {start_date}")
    print(f"Next day: {daily.successor(start_date)}")
    print(f"Previous day: {daily.predecessor(start_date)}")
    
    # Monthly navigation with day overflow handling
    print(f"\n📆 Monthly Navigation (with day overflow)")
    print("-" * 45)
    
    monthly = TimeSet("monthly")
    jan_31 = Date("y2023m01d31")
    
    print(f"January 31st: {jan_31}")
    print(f"Next month: {monthly.successor(jan_31)} (Feb 28 - handles overflow)")
    print(f"Previous month: {monthly.predecessor(jan_31)} (Dec 31)")
    
    # Weekday navigation
    print(f"\n🗓️  Weekday Navigation")
    print("-" * 25)
    
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])  # Monday-Friday
    friday = Date("y2023m06d16")  # Friday
    
    print(f"Current date: {friday} ({friday._value.strftime('%A')})")
    print(f"Next weekday: {weekdays.successor(friday)} ({weekdays.successor(friday)._value.strftime('%A')})")
    print(f"Previous weekday: {weekdays.predecessor(friday)} ({weekdays.predecessor(friday)._value.strftime('%A')})")
    
    # Static methods
    print(f"\n⚡ Static Navigation Methods")
    print("-" * 30)
    
    date = Date("y2023m06d15")
    weekly = TimeSet("weekly")
    
    print(f"Current date: {date}")
    print(f"Next week (static): {TimeSet.next_date(weekly, date)}")
    print(f"Previous week (static): {TimeSet.prev_date(weekly, date)}")
    
    # Business scenario
    print(f"\n💼 Business Scenario: Finding Next Business Day")
    print("-" * 50)
    
    # Friday before weekend
    friday_before_weekend = Date("y2023m06d16")
    business_days = TimeSet.wd([0, 1, 2, 3, 4])
    
    print(f"It's Friday: {friday_before_weekend}")
    next_business = business_days.successor(friday_before_weekend)
    print(f"Next business day: {next_business} ({next_business._value.strftime('%A')})")
    
    # Month-end processing
    print(f"\n📋 Month-end Processing Dates")
    print("-" * 35)
    
    month_end = TimeSet.day([28, 29, 30, 31])
    current = Date("y2023m06d01")
    
    print("Next 4 month-end dates:")
    for i in range(4):
        next_eom = month_end.successor(current)
        print(f"  {i+1}. {next_eom} ({next_eom._value.strftime('%B %d')})")
        current = next_eom
    
    print(f"\n✨ Key Features:")
    print("  ✅ Instance methods: timeset.successor(date), timeset.predecessor(date)")
    print("  ✅ Static methods: TimeSet.next_date(timeset, date), TimeSet.prev_date(timeset, date)")
    print("  ✅ Handles day overflow in months (Jan 31 → Feb 28)")
    print("  ✅ Works with all TimeSet types (daily, weekly, monthly, weekdays, etc.)")
    print("  ✅ Perfect for business logic and date calculations")

if __name__ == "__main__":
    main()