#!/usr/bin/env python3
"""
Simple test to verify that imports work correctly
This focuses on testing imports and basic functionality
"""

import sys
import os

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

print("🧪 Testing TOL Python imports...")
print("=" * 50)

def test_imports():
    """Test all major imports"""
    try:
        print("1. Testing core imports...")
        from core.dates import Date, TimeSet
        from core.data import Data
        print("   ✓ Core modules imported")
        
        print("2. Testing Serie import...")
        from series.serie import Serie
        print("   ✓ Serie imported")
        
        print("3. Testing statistical imports...")
        from stats.statistics import mean, variance, std_dev
        print("   ✓ Statistics imported")
        
        print("4. Testing ARIMA imports...")
        from arima.arima_factor import ARIMAFactor
        from arima.arima_model import ARIMA
        print("   ✓ ARIMA imported")
        
        print("5. Testing Bayesian imports...")
        from bayesian.core import BayesianModel, MCMCConfig
        from bayesian.priors import NormalPrior, InverseGammaPrior
        print("   ✓ Bayesian imported")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality"""
    try:
        print("6. Testing basic functionality...")
        
        # Import what we need
        from series.serie import Serie
        from core.dates import Date, TimeSet
        from stats.statistics import mean, variance
        
        # Test Serie creation
        test_data = [1.0, 2.0, 3.0, 4.0, 5.0]
        serie = Serie(data=test_data)
        print(f"   ✓ Serie created with {len(serie)} observations")
        
        # Test statistics on Serie
        mean_val = mean(serie)
        var_val = variance(serie)
        print(f"   ✓ Statistics: mean={mean_val:.2f}, variance={var_val:.2f}")
        
        # Test Date creation
        date1 = Date("y2023m06d15")
        print(f"   ✓ Date created: {date1}")
        
        # Test TimeSet
        timeset = TimeSet.monthly(Date("y2023m01d01"), 12)
        print(f"   ✓ TimeSet created with {len(timeset)} periods")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Functionality test failed: {e}")
        return False

def test_arima_functionality():
    """Test ARIMA functionality"""
    try:
        print("7. Testing ARIMA functionality...")
        
        from arima.arima_factor import ARIMAFactor
        from arima.arima_model import ARIMA
        from series.serie import Serie
        
        # Create ARIMA factor
        factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1)
        print(f"   ✓ ARIMAFactor created: {factor.get_model_string()}")
        
        # Create ARIMA model
        model = ARIMA(factor)
        print("   ✓ ARIMA model created")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ARIMA test failed: {e}")
        return False

def test_bayesian_functionality():
    """Test Bayesian functionality"""
    try:
        print("8. Testing Bayesian functionality...")
        
        from bayesian.core import MCMCConfig
        from bayesian.priors import NormalPrior, InverseGammaPrior
        from bayesian.arima import BayesianARIMA
        from arima.arima_factor import ARIMAFactor
        
        # Create MCMC config
        config = MCMCConfig(mcmc_burnin=100, mcmc_sample_length=500)
        print("   ✓ MCMCConfig created")
        
        # Create priors
        ar_prior = NormalPrior(0.0, 1.0, name="AR1")
        variance_prior = InverseGammaPrior(3.0, 2.0, name="variance")
        print("   ✓ Priors created")
        
        # Create Bayesian ARIMA
        factor = ARIMAFactor(1, 0, 0)
        bayesian_arima = BayesianARIMA(factor, config=config)
        print("   ✓ BayesianARIMA created")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Bayesian test failed: {e}")
        return False

def main():
    """Run all tests"""
    print(f"Current working directory: {os.getcwd()}")
    print(f"Script location: {current_dir}")
    print()
    
    tests_passed = 0
    total_tests = 4
    
    if test_imports():
        tests_passed += 1
    
    if test_basic_functionality():
        tests_passed += 1
    
    if test_arima_functionality():
        tests_passed += 1
        
    if test_bayesian_functionality():
        tests_passed += 1
    
    print()
    print("=" * 50)
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print()
        print("✅ Your TOL Python installation is working correctly!")
        print()
        print("📖 How to use in your own scripts:")
        print("-" * 30)
        print("import sys")
        print(f"sys.path.append('{current_dir}')")
        print()
        print("# Basic imports")
        print("from series.serie import Serie")
        print("from core.dates import Date, TimeSet")
        print("from stats.statistics import mean, variance, std_dev")
        print()
        print("# ARIMA imports")
        print("from arima.arima_factor import ARIMAFactor")
        print("from arima.arima_model import ARIMA")
        print()
        print("# Bayesian imports")
        print("from bayesian.arima import BayesianARIMA")
        print("from bayesian.core import MCMCConfig")
        print("from bayesian.priors import NormalPrior, InverseGammaPrior")
        print()
        print("# Example usage:")
        print("data = [1, 2, 3, 4, 5]")
        print("serie = Serie(data=data)")
        print("print(f'Mean: {mean(serie)}')")
        
    else:
        print(f"❌ {tests_passed}/{total_tests} tests passed")
        print()
        print("🔧 To fix import issues:")
        print("1. Make sure you're running from the tol_python directory")
        print("2. Add the directory to your Python path")
        print("3. Check that all required files exist")

if __name__ == "__main__":
    main()