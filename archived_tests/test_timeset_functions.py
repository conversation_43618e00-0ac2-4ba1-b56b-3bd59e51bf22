"""
Test all TOL-compatible TimeSet functions
Verifies implementation of Day, Month, Year, WeekDay, Hour, Minute, Second,
Periodic, Range, SerTms, and other TimeSet operations
"""

import numpy as np
from datetime import datetime, timedelta
from core import (Date, TimeSet, DayTimeSet, MonthTimeSet, YearTimeSet,
                  WeekDayTimeSet, HourTimeSet, MinuteTimeSet, SecondTimeSet,
                  PeriodicTimeSet, RangeTimeSet, SerieTimeSet, SpecificDatesTimeSet)
from series import Serie


def test_dates_of_set():
    """Test DatesOfSet function"""
    print("=== Testing DatesOfSet ===")
    
    # Create a daily TimeSet
    daily = TimeSet("daily")
    start = Date("y2023m06d01")
    
    # Get 5 dates starting from June 1 - now returns a TimeSet
    dates_ts = daily.dates_of_set(start, 5)
    
    # Verify it's a TimeSet
    assert isinstance(dates_ts, SpecificDatesTimeSet)
    assert len(dates_ts) == 5
    
    # Get the actual dates list
    dates_list = dates_ts.get_dates_list()
    print(f"5 dates from {start}: {[str(d) for d in dates_list]}")
    assert dates_list[0]._value == datetime(2023, 6, 1)
    assert dates_list[4]._value == datetime(2023, 6, 5)
    
    # Test that we can use it as a TimeSet
    test_date = Date("y2023m06d03")
    assert dates_ts.includes(test_date) == True
    assert dates_ts.includes(Date("y2023m06d07")) == False
    
    # Test TimeSet operations on the result
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])
    business_subset = dates_ts * weekdays  # Intersection with weekdays
    assert isinstance(business_subset, TimeSet)
    
    print("✓ DatesOfSet test passed\n")


def test_day_timeset():
    """Test Day TimeSet function"""
    print("=== Testing Day TimeSet ===")
    
    # Create TimeSet for 15th of each month
    fifteenth = TimeSet.day(15)
    
    # Test includes
    assert fifteenth.includes(Date("y2023m06d15")) == True
    assert fifteenth.includes(Date("y2023m06d16")) == False
    
    # Test successor
    start = Date("y2023m06d10")
    next_15th = fifteenth.successor(start)
    print(f"Next 15th after {start}: {next_15th}")
    assert next_15th._value.day == 15
    
    # Test multiple days
    end_of_month = TimeSet.day([28, 29, 30, 31])
    assert end_of_month.includes(Date("y2023m06d30")) == True
    
    print("✓ Day TimeSet tests passed\n")


def test_month_timeset():
    """Test Month TimeSet function"""
    print("=== Testing Month TimeSet ===")
    
    # Create TimeSet for summer months
    summer = TimeSet.m([6, 7, 8])
    
    assert summer.includes(Date("y2023m06d15")) == True
    assert summer.includes(Date("y2023m05d15")) == False
    assert summer.includes(Date("y2023m07d01")) == True
    
    # Test successor
    spring_date = Date("y2023m05d15")
    first_summer = summer.successor(spring_date)
    print(f"First summer date after {spring_date}: {first_summer}")
    assert first_summer._value.month == 6
    
    print("✓ Month TimeSet tests passed\n")


def test_year_timeset():
    """Test Year TimeSet function"""
    print("=== Testing Year TimeSet ===")
    
    # Create TimeSet for specific years
    leap_years = TimeSet.y([2020, 2024, 2028])
    
    assert leap_years.includes(Date("y2024m06d15")) == True
    assert leap_years.includes(Date("y2023m06d15")) == False
    
    # Test successor
    date_2023 = Date("y2023m06d15")
    next_leap = leap_years.successor(date_2023)
    print(f"Next leap year date after {date_2023}: {next_leap}")
    assert next_leap._value.year == 2024
    
    print("✓ Year TimeSet tests passed\n")


def test_weekday_timeset():
    """Test WeekDay TimeSet function"""
    print("=== Testing WeekDay TimeSet ===")
    
    # Create TimeSet for weekdays (Monday-Friday)
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])  # 0=Monday, 4=Friday
    
    # June 15, 2023 is a Thursday (3)
    thursday = Date("y2023m06d15")
    assert weekdays.includes(thursday) == True
    
    # June 17, 2023 is a Saturday (5)
    saturday = Date("y2023m06d17")
    assert weekdays.includes(saturday) == False
    
    # Find next weekday after Saturday
    next_weekday = weekdays.successor(saturday)
    print(f"Next weekday after Saturday {saturday}: {next_weekday}")
    assert next_weekday._value.weekday() == 0  # Monday
    
    print("✓ WeekDay TimeSet tests passed\n")


def test_hour_minute_second_timesets():
    """Test Hour, Minute, Second TimeSets"""
    print("=== Testing Hour/Minute/Second TimeSets ===")
    
    # Create datetime with specific time
    dt = datetime(2023, 6, 15, 14, 30, 45)  # 2:30:45 PM
    date_time = Date.__new__(Date)
    date_time._value = dt
    
    # Test Hour
    business_hours = TimeSet.h(list(range(9, 18)))  # 9 AM - 5 PM
    assert business_hours.includes(date_time) == True  # 2 PM is business hours
    
    # Test Minute
    quarter_hours = TimeSet.mi([0, 15, 30, 45])
    assert quarter_hours.includes(date_time) == True  # 30 minutes
    
    # Test Second
    even_seconds = TimeSet.s(list(range(0, 60, 2)))
    assert even_seconds.includes(date_time) == False  # 45 is odd
    
    print("✓ Hour/Minute/Second TimeSet tests passed\n")


def test_periodic_timeset():
    """Test Periodic TimeSet function"""
    print("=== Testing Periodic TimeSet ===")
    
    # Every 3 days starting June 1
    start = Date("y2023m06d01")
    every_3_days = TimeSet.periodic(start, 3, "days")
    
    # June 1 + 0*3 = June 1 ✓
    assert every_3_days.includes(Date("y2023m06d01")) == True
    # June 1 + 1*3 = June 4 ✓
    assert every_3_days.includes(Date("y2023m06d04")) == True
    # June 1 + 2*3 = June 7 ✓
    assert every_3_days.includes(Date("y2023m06d07")) == True
    # June 5 is not on schedule
    assert every_3_days.includes(Date("y2023m06d05")) == False
    
    # Test successor
    june2 = Date("y2023m06d02")
    next_periodic = every_3_days.successor(june2)
    print(f"Next periodic date after {june2}: {next_periodic}")
    assert next_periodic._value == datetime(2023, 6, 4)
    
    # Test weekly periodic
    weekly = TimeSet.periodic(start, 1, "weeks")
    assert weekly.includes(Date("y2023m06d08")) == True  # June 1 + 7 days
    
    print("✓ Periodic TimeSet tests passed\n")


def test_range_timeset():
    """Test Range TimeSet function"""
    print("=== Testing Range TimeSet ===")
    
    # Date range from June 1 to June 10
    start = Date("y2023m06d01")
    end = Date("y2023m06d10")
    
    # Daily range
    daily_range = TimeSet.range(start, end)
    assert daily_range.includes(Date("y2023m06d05")) == True
    assert daily_range.includes(Date("y2023m06d15")) == False
    
    # Range with weekly step
    weekly_step = TimeSet("weekly")
    weekly_range = TimeSet.range(start, end, weekly_step)
    
    # Should include June 1 and June 8
    assert weekly_range.includes(start) == True
    assert weekly_range.includes(Date("y2023m06d08")) == True
    assert weekly_range.includes(Date("y2023m06d05")) == False  # Not on weekly pattern
    
    print("✓ Range TimeSet tests passed\n")


def test_serie_timeset():
    """Test SerTms TimeSet function"""
    print("=== Testing SerTms TimeSet ===")
    
    # Create a Serie with specific dates
    data = [10.5, 11.2, 10.8, 12.1, 11.9]
    start_date = Date("y2023m06d01")
    serie = Serie(data=data, first_date=start_date, dating=TimeSet("daily"))
    
    # Create TimeSet from Serie dates
    serie_ts = TimeSet.ser_tms(serie)
    
    # Should include all Serie dates
    assert serie_ts.includes(Date("y2023m06d01")) == True
    assert serie_ts.includes(Date("y2023m06d05")) == True
    assert serie_ts.includes(Date("y2023m06d06")) == False  # Beyond serie end
    
    # Test successor
    june2 = Date("y2023m06d02")
    next_serie_date = serie_ts.successor(june2)
    print(f"Next Serie date after {june2}: {next_serie_date}")
    assert next_serie_date._value == datetime(2023, 6, 3)
    
    print("✓ SerTms TimeSet tests passed\n")


def test_complex_timeset_combinations():
    """Test complex combinations of TimeSets"""
    print("=== Testing Complex TimeSet Combinations ===")
    
    # Business days: weekdays in the month
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])
    june = TimeSet.m(6)
    business_days_june = weekdays * june
    
    # June 15, 2023 is Thursday - should be included
    assert business_days_june.includes(Date("y2023m06d15")) == True
    # June 17, 2023 is Saturday - should not be included
    assert business_days_june.includes(Date("y2023m06d17")) == False
    # May 15, 2023 is not in June - should not be included
    assert business_days_june.includes(Date("y2023m05d15")) == False
    
    # End of month business days
    end_of_month = TimeSet.day([28, 29, 30, 31])
    eom_business = end_of_month * weekdays
    
    # June 30, 2023 is Friday - should be included
    assert eom_business.includes(Date("y2023m06d30")) == True
    
    # Quarterly first business day
    quarters = TimeSet.m([1, 4, 7, 10])
    first_day = TimeSet.day(1)
    q1_days = quarters * first_day  # First day of quarter months
    
    assert q1_days.includes(Date("y2023m01d01")) == True
    assert q1_days.includes(Date("y2023m04d01")) == True
    assert q1_days.includes(Date("y2023m02d01")) == False
    
    print("✓ Complex TimeSet combination tests passed\n")


if __name__ == "__main__":
    print("Testing TOL Python TimeSet Functions")
    print("=" * 50)
    
    test_dates_of_set()
    test_day_timeset()
    test_month_timeset() 
    test_year_timeset()
    test_weekday_timeset()
    test_hour_minute_second_timesets()
    test_periodic_timeset()
    test_range_timeset()
    test_serie_timeset()
    test_complex_timeset_combinations()
    
    print("=" * 50)
    print("✅ All TimeSet function tests passed!")
    print("\nImplemented TOL functions:")
    print("- DatesOfSet: Get n dates from a TimeSet")
    print("- Day: TimeSet for specific days of month")
    print("- M: TimeSet for specific months")
    print("- Y: TimeSet for specific years")
    print("- WD: TimeSet for specific weekdays")
    print("- H/Mi/S: TimeSet for hours/minutes/seconds")
    print("- Periodic: Every n time units")
    print("- Range: Date range with step")
    print("- SerTms: TimeSet from Serie dates")