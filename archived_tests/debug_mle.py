"""
Debug MLE implementation to find the format error
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from tol_python.series import Serie
from tol_python.arima import ARIMAFactor, ARIMA


def debug_mle():
    """Debug MLE step by step"""
    print("Debugging MLE Implementation")
    print("=" * 40)
    
    # Generate simple AR(1) data
    np.random.seed(42)
    n = 50  # Smaller dataset
    phi = 0.7
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(1, n):
        y[t] = phi * y[t-1] + errors[t]
    
    serie = Serie(data=y)
    
    # Simple AR(1) model
    factor = ARIMAFactor(ar_order=1, include_mean=False)
    print(f"Factor: {factor}")
    
    # Try MLE step by step
    try:
        model = ARIMA(factor)
        print("✓ ARIMA model created")
        
        # Try fitting
        print("Attempting MLE fit...")
        results = model.fit(serie, method="mle")
        print(f"✓ MLE fit completed")
        print(f"  AR parameter: {results.ar_params[0]:.4f}")
        print(f"  Converged: {results.converged}")
        
        # Test basic properties
        print(f"  Log-likelihood: {results.loglikelihood}")
        print(f"  n_obs: {results.n_obs}")
        print(f"  n_params: {results.n_params}")
        print(f"  degrees_freedom: {results.degrees_freedom}")
        print(f"  loglikelihood is nan: {np.isnan(results.loglikelihood)}")
        print(f"  AIC: {results.aic}")
        
        return True
        
    except Exception as e:
        import traceback
        print(f"✗ MLE failed: {e}")
        print("Traceback:")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_mle()