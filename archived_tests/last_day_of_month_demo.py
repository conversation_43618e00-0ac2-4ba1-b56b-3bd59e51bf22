"""
Demo: Creating TimeSet for last day of month
Shows different approaches to get month-end dates
"""

from core import Date, TimeSet
from series import Serie

def demo_last_day_approaches():
    print("📅 Last Day of Month TimeSet Creation")
    print("=" * 50)
    
    # Approach 1: Direct method (what currently works)
    print("Approach 1: Direct month-end TimeSet")
    month_end = TimeSet.day([28, 29, 30, 31])
    print(f"month_end = TimeSet.day([28, 29, 30, 31])")
    
    # Test some dates
    test_dates = [
        Date("y2023m01d31"),  # January 31
        Date("y2023m02d28"),  # February 28
        Date("y2023m06d30"),  # June 30
        Date("y2023m06d29"),  # June 29 (not last day)
    ]
    
    print("\nTesting month-end dates:")
    for date in test_dates:
        is_eom = month_end.includes(date)
        status = "✅" if is_eom else "❌"
        print(f"  {date}: {status}")
    
    # Approach 2: What you want to do (but doesn't work as expected)
    print(f"\n\nApproach 2: Your intended approach")
    print("month_begin = TimeSet.day(1)")
    print("# This won't work: month_begin.predecessor()")
    print("# Because predecessor() expects a Date, not a TimeSet")
    
    month_begin = TimeSet.day(1)
    
    # Show what happens when we try to use predecessor with specific dates
    print(f"\nBut we can use predecessor with specific dates:")
    sample_firsts = [
        Date("y2023m02d01"),  # Feb 1 -> Jan 31
        Date("y2023m03d01"),  # Mar 1 -> Feb 28
        Date("y2023m07d01"),  # Jul 1 -> Jun 30
    ]
    
    daily = TimeSet("daily")
    for first_date in sample_firsts:
        last_day = daily.predecessor(first_date)
        print(f"  {first_date} predecessor: {last_day}")
    
    # Approach 3: Creating a custom last-day TimeSet
    print(f"\n\nApproach 3: Custom LastDayOfMonth TimeSet")
    print("We could create a specialized TimeSet class...")
    
    # For now, let's simulate what the last day logic would look like
    def is_last_day_of_month(date: Date) -> bool:
        """Check if a date is the last day of its month"""
        if not date.is_normal():
            return False
        
        # Get the next day
        next_day = date.add_days(1)
        # If next day is the 1st, current day is last of month
        return next_day._value.day == 1
    
    print("\nTesting custom last-day logic:")
    test_dates_custom = [
        Date("y2023m01d31"),  # Last day of January
        Date("y2023m01d30"),  # Not last day of January  
        Date("y2023m02d28"),  # Last day of February (non-leap)
        Date("y2023m06d30"),  # Last day of June
        Date("y2023m12d31"),  # Last day of December
    ]
    
    for date in test_dates_custom:
        is_last = is_last_day_of_month(date)
        status = "✅" if is_last else "❌"
        print(f"  {date}: {status}")

def demo_month_navigation():
    """Show how to navigate between month boundaries"""
    print(f"\n\n🧭 Month Boundary Navigation")
    print("=" * 40)
    
    # Start with first of month, find last of previous month
    print("Finding last day of previous month:")
    
    firsts = [
        Date("y2023m02d01"),  # Feb 1
        Date("y2023m03d01"),  # Mar 1  
        Date("y2023m07d01"),  # Jul 1
        Date("y2024m03d01"),  # Mar 1 (leap year)
    ]
    
    daily = TimeSet("daily")
    
    for first in firsts:
        prev_day = daily.predecessor(first)
        month_name = first._value.strftime('%B')
        prev_month = prev_day._value.strftime('%B')
        print(f"  {first} ({month_name} 1st) -> {prev_day} (last day of {prev_month})")

def demo_business_scenario():
    """Show practical usage for month-end processing"""
    print(f"\n\n💼 Business Scenario: Month-end Reports")
    print("=" * 45)
    
    # Create month-end TimeSet
    month_end = TimeSet.day([28, 29, 30, 31])
    business_days = TimeSet.wd([0, 1, 2, 3, 4])
    
    # Month-end business days
    eom_business = month_end * business_days
    
    print("Next 5 month-end business days:")
    current = Date("y2023m06d01")
    
    for i in range(5):
        next_eom = eom_business.successor(current)
        if next_eom.is_normal():
            day_name = next_eom._value.strftime('%A, %B %d')
            print(f"  {i+1}. {next_eom} ({day_name})")
            current = next_eom
        else:
            break
    
    # Create indicator series
    print(f"\nMonth-end business day indicator:")
    start = Date("y2023m06d01")
    end = Date("y2023m06d30")
    daily = TimeSet("daily")
    
    eom_indicator = Serie.cal_ind(eom_business, daily, start, end)
    
    # Show just the month-end dates
    current = start
    while current <= end:
        if eom_indicator[current] == 1.0:
            day_name = current._value.strftime('%A, %B %d')
            print(f"  📋 {current} ({day_name}) - Month-end business day")
        current = daily.successor(current)

if __name__ == "__main__":
    print("Creating TimeSet for Last Day of Month")
    print("=" * 60)
    
    demo_last_day_approaches()
    demo_month_navigation()
    demo_business_scenario()
    
    print("\n" + "=" * 60)
    print("💡 Summary:")
    print("• Current approach: TimeSet.day([28, 29, 30, 31]) works well")
    print("• Navigation: Use daily.predecessor(first_of_month) for specific dates")
    print("• Business logic: Combine with business days for practical scenarios")
    print("• Custom TimeSet: Could implement LastDayOfMonth class if needed")