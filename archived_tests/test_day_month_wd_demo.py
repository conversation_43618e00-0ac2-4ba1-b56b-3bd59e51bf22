"""
Interactive Demo: Day, Month, and WeekDay TimeSet Functions
Run this to see how TimeSet date filtering works in practice
"""

from core import Date, TimeSet
from datetime import datetime

def demo_day_function():
    """Demonstrate TimeSet.day() function"""
    print("🗓️  DAY FUNCTION DEMO")
    print("=" * 50)
    
    # Create TimeSet for 15th of every month
    fifteenth = TimeSet.day(Date("y2023m01d01"), 15)
    print(f"TimeSet for 15th of month: {fifteenth}")
    
    # Test specific dates
    test_dates = [
        Date("y2023m06d15"),  # June 15 - should match
        Date("y2023m06d16"),  # June 16 - should NOT match
        Date("y2023m07d15"),  # July 15 - should match
        Date("y2023m12d15"),  # December 15 - should match
    ]
    
    print("\nTesting dates:")
    for date in test_dates:
        includes = fifteenth.includes(date)
        status = "✅ INCLUDED" if includes else "❌ excluded"
        print(f"  {date} ({date._value.strftime('%B %d, %Y')}): {status}")
    
    # Find next 15th after June 10
    june_10 = Date("y2023m06d10")
    next_15th = fifteenth.successor(june_10)
    print(f"\nNext 15th after {june_10}: {next_15th}")
    
    # Multiple days - end of month
    month_end = TimeSet.day(Date("y2023m01d01"), [28, 29, 30, 31])
    print(f"\nMonth-end TimeSet: {month_end}")
    
    end_dates = [
        Date("y2023m02d28"),  # Feb 28 - should match
        Date("y2023m02d27"),  # Feb 27 - should NOT match
        Date("y2023m06d30"),  # June 30 - should match
        Date("y2023m07d31"),  # July 31 - should match
    ]
    
    print("Testing month-end dates:")
    for date in end_dates:
        includes = month_end.includes(date)
        status = "✅ INCLUDED" if includes else "❌ excluded"
        print(f"  {date}: {status}")
    
    print()


def demo_month_function():
    """Demonstrate TimeSet.m() function"""
    print("📅 MONTH FUNCTION DEMO")
    print("=" * 50)
    
    # Summer months (June, July, August)
    summer = TimeSet.m(Date("y2023m01d01"), [6, 7, 8])
    print(f"Summer months TimeSet: {summer}")
    
    # Test dates across the year
    test_dates = [
        Date("y2023m05d15"),  # May - should NOT match
        Date("y2023m06d01"),  # June 1 - should match
        Date("y2023m06d30"),  # June 30 - should match
        Date("y2023m07d15"),  # July 15 - should match
        Date("y2023m08d31"),  # August 31 - should match
        Date("y2023m09d01"),  # September 1 - should NOT match
    ]
    
    print("\nTesting dates throughout the year:")
    for date in test_dates:
        includes = summer.includes(date)
        status = "☀️ SUMMER" if includes else "❄️ not summer"
        month_name = date._value.strftime('%B')
        print(f"  {date} ({month_name}): {status}")
    
    # Find first summer date after spring
    spring_date = Date("y2023m05d15")
    first_summer = summer.successor(spring_date)
    print(f"\nFirst summer date after {spring_date}: {first_summer}")
    
    # Quarter months (March, June, September, December)
    quarters = TimeSet.m(Date("y2023m01d01"), [3, 6, 9, 12])
    print(f"\nQuarter-end months: {quarters}")
    
    print("Testing quarter-end months:")
    for month in range(1, 13):
        test_date = Date(f"y2023m{month:02d}d15")
        includes = quarters.includes(test_date)
        status = "📊 QUARTER END" if includes else "📈 regular month"
        month_name = test_date._value.strftime('%B')
        print(f"  {month_name}: {status}")
    
    print()


def demo_weekday_function():
    """Demonstrate TimeSet.wd() function"""
    print("📆 WEEKDAY FUNCTION DEMO")
    print("=" * 50)
    
    # Weekdays only (Monday-Friday)
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])  # 0=Monday, 4=Friday
    print(f"Business days TimeSet: {weekdays}")
    
    # Test a full week in June 2023
    print("\nTesting a full week (June 12-18, 2023):")
    for day in range(12, 19):
        test_date = Date(f"y2023m06d{day}")
        includes = weekdays.includes(test_date)
        day_name = test_date._value.strftime('%A')
        status = "💼 BUSINESS DAY" if includes else "🏖️ WEEKEND"
        print(f"  {test_date} ({day_name}): {status}")
    
    # Find next business day after Saturday
    saturday = Date("y2023m06d17")  # Saturday
    next_business = weekdays.successor(saturday)
    print(f"\nNext business day after {saturday} (Saturday): {next_business}")
    
    # Weekends only
    weekends = TimeSet.wd([5, 6])  # 5=Saturday, 6=Sunday
    print(f"\nWeekend TimeSet: {weekends}")
    
    print("Testing weekend detection:")
    weekend_test_dates = [
        Date("y2023m06d16"),  # Friday
        Date("y2023m06d17"),  # Saturday
        Date("y2023m06d18"),  # Sunday
        Date("y2023m06d19"),  # Monday
    ]
    
    for date in weekend_test_dates:
        includes = weekends.includes(date)
        day_name = date._value.strftime('%A')
        status = "🎉 WEEKEND" if includes else "😴 weekday"
        print(f"  {date} ({day_name}): {status}")
    
    # Just Fridays (for "Summer Fridays")
    fridays = TimeSet.wd(4)  # Friday = 4
    print(f"\nFridays only: {fridays}")
    
    print()


def demo_combined_operations():
    """Demonstrate combining Day, Month, and WeekDay operations"""
    print("🔗 COMBINED OPERATIONS DEMO")
    print("=" * 50)
    
    # Business rules using combinations
    print("Creating business rules with TimeSet combinations...")
    
    # Month-end business days
    month_end = TimeSet.day(Date("y2023m01d01"), [28, 29, 30, 31])
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])
    month_end_business = month_end * weekdays
    
    print(f"Month-end business days: {month_end_business}")
    
    # Test some month-end dates
    test_dates = [
        Date("y2023m06d30"),  # Friday, June 30
        Date("y2023m07d31"),  # Monday, July 31
        Date("y2023m09d30"),  # Saturday, September 30 (should NOT match - weekend)
        Date("y2023m12d29"),  # Friday, December 29
    ]
    
    print("\nTesting month-end business days:")
    for date in test_dates:
        includes = month_end_business.includes(date)
        day_name = date._value.strftime('%A, %B %d')
        status = "📋 MONTH-END BUSINESS DAY" if includes else "❌ not month-end business day"
        print(f"  {date} ({day_name}): {status}")
    
    # Summer Fridays
    summer = TimeSet.m(Date("y2023m01d01"), [6, 7, 8])
    fridays = TimeSet.wd(4)
    summer_fridays = summer * fridays
    
    print(f"\nSummer Fridays: {summer_fridays}")
    
    summer_test = [
        Date("y2023m06d02"),  # Friday in June
        Date("y2023m06d03"),  # Saturday in June (not Friday)
        Date("y2023m07d14"),  # Friday in July
        Date("y2023m08d25"),  # Friday in August
        Date("y2023m09d01"),  # Friday in September (not summer)
    ]
    
    print("Testing Summer Fridays:")
    for date in summer_test:
        includes = summer_fridays.includes(date)
        day_name = date._value.strftime('%A, %B %d')
        status = "🌞 SUMMER FRIDAY!" if includes else "❌ not summer friday"
        print(f"  {date} ({day_name}): {status}")
    
    # Quarter-end reporting dates (last Friday of quarter months)
    quarters = TimeSet.m(Date("y2023m01d01"), [3, 6, 9, 12])
    last_week = TimeSet.day(Date("y2023m01d01"), [25, 26, 27, 28, 29, 30, 31])
    quarter_end_fridays = quarters * fridays * last_week
    
    print(f"\nQuarter-end reporting Fridays: {quarter_end_fridays}")
    
    # Find the next few quarter-end Fridays
    start_search = Date("y2023m01d01")
    print("Next quarter-end Fridays:")
    for i in range(4):
        if i == 0:
            next_friday = quarter_end_fridays.successor(start_search)
        else:
            next_friday = quarter_end_fridays.successor(next_friday)
        
        if next_friday.is_normal():
            day_name = next_friday._value.strftime('%A, %B %d, %Y')
            print(f"  {next_friday} ({day_name}) - Perfect for quarterly reports!")
        else:
            break
    
    print()


def demo_practical_scenarios():
    """Show practical business scenarios"""
    print("💼 PRACTICAL BUSINESS SCENARIOS")
    print("=" * 50)
    
    # Scenario 1: Find all payroll days (15th and last day of month, but only business days)
    print("Scenario 1: Payroll Days (15th and month-end, business days only)")
    
    payroll_days = TimeSet.day(Date("y2023m01d01"), [15, 28, 29, 30, 31])
    business_days = TimeSet.wd([0, 1, 2, 3, 4])
    payroll_business = payroll_days * business_days
    
    # Get next 6 payroll days
    payroll_dates = payroll_business.dates_of_set(Date("y2023m06d01"), 6)
    payroll_list = payroll_dates.get_dates_list()
    
    print("Next 6 payroll days:")
    for date in payroll_list:
        day_name = date._value.strftime('%A, %B %d, %Y')
        day_type = "15th" if date._value.day == 15 else "month-end"
        print(f"  💰 {date} ({day_name}) - {day_type} payroll")
    
    print()
    
    # Scenario 2: Board meeting schedule (first Tuesday of each quarter month)
    print("Scenario 2: Board Meetings (First Tuesday of quarter months)")
    
    quarters = TimeSet.m(Date("y2023m01d01"), [3, 6, 9, 12])
    first_week = TimeSet.day(Date("y2023m01d01"), [1, 2, 3, 4, 5, 6, 7])
    tuesdays = TimeSet.wd(1)  # Tuesday = 1
    board_meetings = quarters * first_week * tuesdays
    
    # Find board meeting dates for 2023
    meeting_dates = board_meetings.dates_of_set(Date("y2023m01d01"), 4)
    meeting_list = meeting_dates.get_dates_list()
    
    print("2023 Board meeting schedule:")
    for date in meeting_list:
        day_name = date._value.strftime('%A, %B %d, %Y')
        print(f"  📋 {date} ({day_name}) - Quarterly board meeting")
    
    print()
    
    # Scenario 3: Holiday party planning (last Friday before month-end in December)
    print("Scenario 3: Holiday Party (Last Friday before month-end in December)")
    
    december = TimeSet.m(Date("y2023m01d01"), 12)
    late_month = TimeSet.day(Date("y2023m01d01"), [20, 21, 22, 23, 24, 25, 26, 27])
    fridays = TimeSet.wd(4)
    holiday_party = december * late_month * fridays
    
    party_dates = holiday_party.dates_of_set(Date("y2023m12d01"), 2)
    party_list = party_dates.get_dates_list()
    
    print("Holiday party options:")
    for date in party_list:
        day_name = date._value.strftime('%A, %B %d, %Y')
        print(f"  🎉 {date} ({day_name}) - Perfect for holiday party!")


if __name__ == "__main__":
    print("🎯 TOL PYTHON TIMESET INTERACTIVE DEMO")
    print("Testing Day, Month, and WeekDay Functions")
    print("=" * 60)
    print()
    
    # Run all demos
    demo_day_function()
    demo_month_function()
    demo_weekday_function()
    demo_combined_operations()
    demo_practical_scenarios()
    
    print("=" * 60)
    print("✅ Demo Complete!")
    print()
    print("🔧 Try modifying the dates and parameters above to see how it works!")
    print("🚀 Key takeaways:")
    print("   • TimeSet.day(date, days) - Filter by day of month")
    print("   • TimeSet.m(date, months) - Filter by month")
    print("   • TimeSet.wd(weekdays) - Filter by weekday (0=Monday)")
    print("   • Use * to combine filters (intersection)")
    print("   • Use + to add filters (union)")
    print("   • Use - to exclude filters (difference)")
    print("   • All functions return TimeSets for maximum composability!")