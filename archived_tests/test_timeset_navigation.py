"""
Test TimeSet successor and predecessor navigation methods
Demonstrates correct usage of TimeSet navigation functions
"""

from core import Date, TimeSet
from series import Serie

def test_basic_timeset_navigation():
    """Test basic successor/predecessor methods"""
    print("🧭 Testing TimeSet Navigation Methods")
    print("=" * 50)
    
    # Test with daily TimeSet
    daily = TimeSet("daily")
    start_date = Date("y2023m06d15")
    
    print("Daily TimeSet navigation:")
    print(f"  Start date: {start_date}")
    
    # Test instance methods
    next_day = daily.successor(start_date)
    prev_day = daily.predecessor(start_date)
    print(f"  Next day: {next_day}")
    print(f"  Previous day: {prev_day}")
    
    # Test static methods
    next_day_static = TimeSet.next_date(daily, start_date)
    prev_day_static = TimeSet.prev_date(daily, start_date)
    print(f"  Next day (static): {next_day_static}")
    print(f"  Previous day (static): {prev_day_static}")
    
    print()

def test_weekly_navigation():
    """Test navigation with weekly TimeSet"""
    print("📅 Testing Weekly Navigation")
    print("=" * 30)
    
    weekly = TimeSet("weekly")
    thursday = Date("y2023m06d15")  # June 15, 2023 is a Thursday
    
    print(f"Current date: {thursday} ({thursday._value.strftime('%A')})")
    
    next_week = weekly.successor(thursday)
    prev_week = weekly.predecessor(thursday)
    
    print(f"Next week: {next_week} ({next_week._value.strftime('%A')})")
    print(f"Previous week: {prev_week} ({prev_week._value.strftime('%A')})")
    
    print()

def test_monthly_navigation():
    """Test navigation with monthly TimeSet"""
    print("📆 Testing Monthly Navigation")
    print("=" * 30)
    
    monthly = TimeSet("monthly")
    june_15 = Date("y2023m06d15")
    
    print(f"Current date: {june_15}")
    
    next_month = monthly.successor(june_15)
    prev_month = monthly.predecessor(june_15)
    
    print(f"Next month: {next_month}")
    print(f"Previous month: {prev_month}")
    
    # Test edge case - January 31st + 1 month
    jan_31 = Date("y2023m01d31")
    feb_date = monthly.successor(jan_31)
    print(f"Jan 31 + 1 month: {feb_date} (handles day overflow)")
    
    print()

def test_custom_timeset_navigation():
    """Test navigation with custom TimeSets"""
    print("🎯 Testing Custom TimeSet Navigation")
    print("=" * 40)
    
    # Test with weekdays TimeSet
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])  # Monday-Friday
    friday = Date("y2023m06d16")  # June 16, 2023 is a Friday
    
    print(f"Current date: {friday} ({friday._value.strftime('%A')})")
    
    next_weekday = weekdays.successor(friday)
    prev_weekday = weekdays.predecessor(friday)
    
    print(f"Next weekday: {next_weekday} ({next_weekday._value.strftime('%A')})")
    print(f"Previous weekday: {prev_weekday} ({prev_weekday._value.strftime('%A')})")
    
    # Test with month TimeSet
    june_july = TimeSet.m([6, 7])  # June and July
    june_date = Date("y2023m06d15")
    
    print(f"\nCurrent date: {june_date}")
    next_in_set = june_july.successor(june_date)
    print(f"Next date in June/July set: {next_in_set}")
    
    print()

def test_timeset_sequence_navigation():
    """Test navigating through a sequence of dates in a TimeSet"""
    print("🔄 Testing TimeSet Sequence Navigation")
    print("=" * 45)
    
    # Create month-end TimeSet
    month_end = TimeSet.day([28, 29, 30, 31])
    
    print("Month-end dates starting from June 1st:")
    current = Date("y2023m06d01")
    
    for i in range(5):
        next_eom = month_end.successor(current)
        if next_eom.is_normal():
            print(f"  {i+1}. {next_eom} ({next_eom._value.strftime('%B %d, %Y')})")
            current = next_eom
        else:
            break
    
    print()

def test_business_scenario():
    """Test real business scenario using TimeSet navigation"""
    print("💼 Business Scenario: Payroll Dates")
    print("=" * 40)
    
    # Payroll runs on 15th and last day of month, but only on business days
    payroll_days = TimeSet.day([15, 28, 29, 30, 31])
    business_days = TimeSet.wd([0, 1, 2, 3, 4])
    
    # Combined: payroll days that are also business days
    payroll_business = payroll_days * business_days
    
    print("Next 6 payroll dates (business days only):")
    current = Date("y2023m06d01")
    
    for i in range(6):
        next_payroll = payroll_business.successor(current)
        if next_payroll.is_normal():
            day_name = next_payroll._value.strftime('%A')
            day_type = "15th" if next_payroll._value.day == 15 else "End of month"
            print(f"  {i+1}. {next_payroll} ({day_name}) - {day_type}")
            current = next_payroll
        else:
            break
    
    print()

def demo_navigation_with_series():
    """Demonstrate navigation in context of Series operations"""
    print("📊 Navigation with Series Data")
    print("=" * 35)
    
    # Create a daily time series for 2 weeks
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Generate some sample data
    data = [100, 102, 98, 103, 99, 105, 101, 97, 104, 100, 106, 102, 98, 107]
    daily_series = Serie(data=data, first_date=start, dating=daily)
    
    print("Sample daily series (first 7 days):")
    print(daily_series.table(max_rows=7))
    
    # Find the date with maximum value
    max_value = max(data)
    max_date = None
    current = daily_series.first_date
    
    while current <= daily_series.last_date:
        if daily_series[current] == max_value:
            max_date = current
            break
        current = daily.successor(current)
    
    if max_date:
        print(f"\nMaximum value {max_value} occurs on: {max_date}")
        
        # Find previous and next dates
        prev_date = daily.predecessor(max_date)
        next_date = daily.successor(max_date)
        
        if prev_date >= daily_series.first_date:
            print(f"Previous day value: {daily_series[prev_date]}")
        if next_date <= daily_series.last_date:
            print(f"Next day value: {daily_series[next_date]}")
    
    print()

if __name__ == "__main__":
    print("TimeSet Navigation Methods Test Suite")
    print("=" * 60)
    print()
    
    test_basic_timeset_navigation()
    test_weekly_navigation()
    test_monthly_navigation()
    test_custom_timeset_navigation()
    test_timeset_sequence_navigation()
    test_business_scenario()
    demo_navigation_with_series()
    
    print("=" * 60)
    print("✅ All TimeSet navigation tests completed!")
    print()
    print("📋 Available Navigation Methods:")
    print("  Instance methods:")
    print("    • timeset.successor(date) - Get next date in TimeSet")
    print("    • timeset.predecessor(date) - Get previous date in TimeSet")
    print("  Static methods:")
    print("    • TimeSet.next_date(timeset, date) - Static successor")
    print("    • TimeSet.prev_date(timeset, date) - Static predecessor")
    print()
    print("💡 Navigation works with:")
    print("  • Basic TimeSets (daily, weekly, monthly, yearly)")
    print("  • Custom TimeSets (weekdays, month-end, specific days)")
    print("  • Combined TimeSets (intersections, unions)")
    print("  • Series data analysis and processing")