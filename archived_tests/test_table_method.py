#!/usr/bin/env python3
"""
Test the restored table method in corrected Serie implementation
"""

import numpy as np
from core.dates import Date, TimeSet
from series.corrected_serie import Serie, SerieTable

def test_single_serie_table():
    """Test table method on a single serie"""
    print("=== Testing Single Serie Table ===")
    
    # Create test data
    data = [1.0, 2.5, 3.7, 4.2, 5.8]
    first_date = Date("y2023m01d01")
    dating = TimeSet("daily")
    
    serie = Serie(data=data, first_date=first_date, dating=dating)
    
    # Generate table
    table_output = serie.table()
    print("Table output:")
    print(table_output)
    print()

def test_multiple_series_table():
    """Test SerieTable with multiple series"""
    print("=== Testing Multiple Series Table ===")
    
    # Create test series
    data1 = [1.0, 2.0, 3.0, 4.0, 5.0]
    data2 = [10.5, 20.7, 30.2, 40.8, 50.1]
    data3 = [100.0, 200.0, np.nan, 400.0, 500.0]  # With missing value
    
    first_date = Date("y2023m01d01")
    dating = TimeSet("daily")
    
    serie1 = Serie(data=data1, first_date=first_date, dating=dating)
    serie2 = Serie(data=data2, first_date=first_date, dating=dating)
    serie3 = Serie(data=data3, first_date=first_date, dating=dating)
    
    # Create table with multiple series
    table = SerieTable()
    table.add_serie(serie1, "Growth")
    table.add_serie(serie2, "Revenue")
    table.add_serie(serie3, "Profit")
    
    # Generate table
    table_output = table.to_bdt_format()
    print("Multiple series table:")
    print(table_output)
    print()

def test_table_with_different_date_ranges():
    """Test table with series having different date ranges"""
    print("=== Testing Series with Different Date Ranges ===")
    
    # Series 1: Jan 1-5
    data1 = [1.0, 2.0, 3.0, 4.0, 5.0]
    serie1 = Serie(data=data1, first_date=Date("y2023m01d01"), dating=TimeSet("daily"))
    
    # Series 2: Jan 3-7 (overlaps partially)
    data2 = [30.0, 40.0, 50.0, 60.0, 70.0]
    serie2 = Serie(data=data2, first_date=Date("y2023m01d03"), dating=TimeSet("daily"))
    
    # Create table
    table = SerieTable()
    table.add_serie(serie1, "SerieA")
    table.add_serie(serie2, "SerieB")
    
    # Generate table (should handle missing values automatically)
    table_output = table.to_bdt_format()
    print("Different date ranges table:")
    print(table_output)
    print()

def test_bdt_format_compliance():
    """Test BDT format compliance with C++ output"""
    print("=== Testing BDT Format Compliance ===")
    
    data = [1.234567, 2.789012, np.nan, 4.567890]
    first_date = Date("y2023m01d15")
    dating = TimeSet("daily")
    
    serie = Serie(data=data, first_date=first_date, dating=dating)
    table_output = serie.table()
    
    print("BDT format output:")
    print(table_output)
    
    # Verify format elements
    lines = table_output.strip().split('\n')
    print(f"\nFormat verification:")
    print(f"  Header line: {lines[0]}")
    print(f"  Data lines: {len(lines)-1}")
    tab_check = '✓' if '\t' in lines[0] else '✗'
    unknown_check = '✓' if '?' in table_output else '✗'
    print(f"  Tab-separated: {tab_check}")
    print(f"  Unknown marker: {unknown_check}")
    print()

def main():
    """Run all table method tests"""
    print("Testing Corrected Serie Table Method")
    print("=" * 50)
    
    try:
        test_single_serie_table()
        test_multiple_series_table()
        test_table_with_different_date_ranges()
        test_bdt_format_compliance()
        
        print("✅ All table method tests completed successfully!")
        print("The table method now matches C++ BSerieTable functionality.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())