#!/usr/bin/env python3
"""
Comprehensive example demonstrating TOL to Python Serie migration
Shows how to replicate common TOL patterns in Python
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tol_python import Serie, Date, TimeSet
import numpy as np


def example_1_basic_creation():
    """Example 1: Basic Serie Creation and Access"""
    print("=== Example 1: Basic Serie Creation ===\n")
    
    print("TOL Code:")
    print("  Serie A = SubSer(Rand(1,2,C), y2000, y2001);")
    print("  Real a = SumS(A);")
    print("  WriteLn(\"Sum: \" << a);")
    
    print("\nPython Equivalent:")
    # Simulate Rand(1,2,C) - random values between 1 and 2
    np.random.seed(42)
    data = np.random.uniform(1, 2, 365)  # Daily data for one year
    
    A = Serie(data=data,
              first_date="y2000m01d01",
              last_date="y2000m12d30")
    
    a = A.sum()
    print(f"  Sum: {a:.2f}")
    print(f"  First value: {A['y2000m01d01']:.4f}")
    print(f"  Last value: {A['y2000m12d30']:.4f}")


def example_2_operations():
    """Example 2: Serie Operations"""
    print("\n\n=== Example 2: Serie Operations ===\n")
    
    print("TOL Code:")
    print("  Serie C = A + B;")
    print("  Serie D = A - B;")
    print("  Serie E = (1-B) * A;  // First difference")
    
    print("\nPython Equivalent:")
    
    # Create two series
    A = Serie(data=[10, 20, 30, 40, 50],
              first_date="y2023m01d01",
              last_date="y2023m01d05")
    
    B = Serie(data=[1, 2, 3, 4, 5],
              first_date="y2023m01d01",
              last_date="y2023m01d05")
    
    C = A + B
    D = A - B
    E = A.diff()  # First difference (1-B operator)
    
    print(f"  A: {list(A.to_dict().values())}")
    print(f"  B: {list(B.to_dict().values())}")
    print(f"  C = A + B: {list(C.to_dict().values())}")
    print(f"  D = A - B: {list(D.to_dict().values())}")
    print(f"  E = diff(A): {[v for v in E.to_dict().values() if v is not None]}")


def example_3_time_series_analysis():
    """Example 3: Time Series Analysis"""
    print("\n\n=== Example 3: Time Series Analysis ===\n")
    
    print("TOL Pattern: Moving averages and transformations")
    
    # Create a serie with trend and noise
    t = np.linspace(0, 4*np.pi, 100)
    trend = 100 + 2*t
    seasonal = 10 * np.sin(t)
    noise = np.random.normal(0, 5, 100)
    data = trend + seasonal + noise
    
    ts = Serie(data=data,
               first_date="y2023m01d01",
               last_date="y2023m04d10")
    
    # Calculate moving averages
    ma_7 = ts.moving_average(7)
    ma_30 = ts.moving_average(30)
    
    # Detrend using first differences
    detrended = ts.diff()
    
    print(f"Original series stats:")
    print(f"  Mean: {ts.mean():.2f}")
    print(f"  Min: {min(ts.to_dict().values()):.2f}")
    print(f"  Max: {max(ts.to_dict().values()):.2f}")
    
    print(f"\nMoving averages reduce variance:")
    print(f"  MA(7) mean: {ma_7.mean():.2f}")
    print(f"  MA(30) mean: {ma_30.mean():.2f}")


def example_4_missing_values():
    """Example 4: Handling Missing Values"""
    print("\n\n=== Example 4: Missing Values ===\n")
    
    print("TOL handles missing values (BMissing) automatically")
    print("Python implementation preserves this behavior:\n")
    
    # Serie with missing values
    s = Serie(data=[100, None, 102, 103, None, None, 107],
              first_date="y2023m01d01",
              last_date="y2023m01d07")
    
    print("Serie with missing values:")
    for date, value in s.to_dict().items():
        val_str = f"{value:.1f}" if value is not None else "missing"
        print(f"  {date}: {val_str}")
    
    print(f"\nSum (ignoring missing): {s.sum():.1f}")
    print(f"Mean (ignoring missing): {s.mean():.1f}")
    
    # Operations propagate missing values
    s2 = s * 2
    print("\nAfter multiplication by 2:")
    for date, value in s2.to_dict().items():
        val_str = f"{value:.1f}" if value is not None else "missing"
        print(f"  {date}: {val_str}")


def example_5_different_frequencies():
    """Example 5: Different Time Frequencies"""
    print("\n\n=== Example 5: Different Frequencies ===\n")
    
    print("TOL supports various dating patterns")
    print("Python implementation mirrors this:\n")
    
    # Monthly data
    monthly = Serie(data=[100, 105, 103, 108, 112, 115],
                   first_date="y2023m01d01",
                   last_date="y2023m06d01",
                   dating=TimeSet("monthly"))
    
    print("Monthly serie:")
    for date, value in list(monthly.to_dict().items())[:3]:
        print(f"  {date}: {value}")
    print("  ...")
    
    # Yearly data
    yearly = Serie(data=[1000, 1100, 1250, 1400, 1600],
                  first_date="y2020m01d01",
                  last_date="y2024m01d01",
                  dating=TimeSet("yearly"))
    
    print("\nYearly serie:")
    for date, value in yearly.to_dict().items():
        print(f"  {date}: {value}")
    
    # Calculate growth rates
    growth = (yearly.diff() / yearly.lag(1)) * 100
    print("\nYear-over-year growth rates:")
    for date, value in growth.to_dict().items():
        if value is not None:
            print(f"  {date}: {value:.1f}%")


def example_6_io_operations():
    """Example 6: Saving and Loading Series"""
    print("\n\n=== Example 6: I/O Operations ===\n")
    
    print("TOL can save/load series in binary format")
    print("Python implementation supports multiple formats:\n")
    
    # Create a serie
    s = Serie(data=np.random.randn(20),
              first_date="y2023m01d01",
              last_date="y2023m01d20")
    
    # Save in different formats
    import tempfile
    
    # JSON format
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json_file = f.name
        s.to_json(f)
    
    # CSV format
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        csv_file = f.name
        s.to_csv(f)
    
    # Binary format (TOL-compatible)
    with tempfile.NamedTemporaryFile(suffix='.tols', delete=False) as f:
        binary_file = f.name
        s.to_binary(f)
    
    print(f"Saved to:")
    print(f"  JSON: {os.path.basename(json_file)}")
    print(f"  CSV: {os.path.basename(csv_file)}")
    print(f"  Binary: {os.path.basename(binary_file)}")
    
    # Load and verify
    s_json = Serie.from_json(json_file)
    s_csv = Serie.from_csv(csv_file)
    s_binary = Serie.from_binary(binary_file)
    
    print(f"\nAll formats preserve data: {s.sum():.4f} = {s_json.sum():.4f} = {s_csv.sum():.4f} = {s_binary.sum():.4f}")
    
    # Clean up
    os.unlink(json_file)
    os.unlink(csv_file)
    os.unlink(binary_file)


def example_7_advanced_operations():
    """Example 7: Advanced TOL-like Operations"""
    print("\n\n=== Example 7: Advanced Operations ===\n")
    
    print("Replicating complex TOL expressions:\n")
    
    # Create base serie
    s = Serie(data=[100, 102, 98, 105, 110, 108, 115, 120],
              first_date="y2023m01d01",
              last_date="y2023m01d08")
    
    # TOL: Serie Y = 0.5*Y(-1) + 0.3*X + noise
    # This is an AR(1) model with external variable
    
    # Initialize result serie
    Y = Serie(data=[100],  # Initial value
              first_date="y2023m01d01",
              last_date="y2023m01d08")
    
    # Generate the AR(1) process
    np.random.seed(42)
    for i in range(1, 8):
        date = Y._index_to_date(i)
        prev_date = Y._index_to_date(i-1)
        
        y_prev = Y[prev_date]
        x_current = s[date]
        noise = np.random.normal(0, 2)
        
        Y[date] = 0.5 * y_prev + 0.3 * x_current + noise
    
    print("AR(1) model: Y(t) = 0.5*Y(t-1) + 0.3*X(t) + noise")
    print("\nResults:")
    for date in ["y2023m01d01", "y2023m01d02", "y2023m01d03", "y2023m01d08"]:
        print(f"  {date}: X={s[date]:.1f}, Y={Y[date]:.1f}")


if __name__ == "__main__":
    print("TOL to Python Serie Migration Examples")
    print("=" * 50)
    
    example_1_basic_creation()
    example_2_operations()
    example_3_time_series_analysis()
    example_4_missing_values()
    example_5_different_frequencies()
    example_6_io_operations()
    example_7_advanced_operations()
    
    print("\n" + "=" * 50)
    print("Migration complete! The Python implementation provides:")
    print("- TOL-compatible date handling")
    print("- All basic Serie operations")
    print("- Missing value support")
    print("- Multiple I/O formats")
    print("- Integration with NumPy/pandas")