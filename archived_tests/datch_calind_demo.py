"""
Simple demo of DatCh and CalInd functions
Shows common usage patterns for calendar indicators and frequency conversion
"""

from core import Date, TimeSet
from series import Serie, SerieOperations
import numpy as np

def demo_calind():
    """Demonstrate CalInd usage"""
    print("🗓️  CalInd (Calendar Indicator) Demo")
    print("=" * 50)
    
    # Create indicators for business planning
    start = Date("y2028m01d01")
    end = Date("y2028m03d31")
    daily = TimeSet("daily")

    # Business days indicator
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])
    business_indicator = Serie.cal_ind(weekdays, daily, start, end)
    
    # Last day of month indicator using new TimeSet
    last_day = TimeSet.last_day_of_month()
    last_day_indicator = Serie.cal_ind(last_day, daily, start, end)
    
    # Month-begin indicator  
    month_begin = TimeSet.day(1)
    
    # Example of using TOL Successor function
    month_begin_plus_1 = TimeSet.successor_tol(month_begin, 1)  # 2nd day of month
    
    month_begin_indicator = Serie.cal_ind(month_begin, daily, start, end)

    print("Business Days and Month-end Indicators for 2028:")
    test_dates = [
        Date("y2028m01d01"),  # First day
        Date("y2028m01d03"),  # Regular day
        Date("y2028m01d31"),  # Last day of January
        Date("y2028m02d29"),  # Last day of February (leap year)
        Date("y2028m03d31"),  # Last day of March
    ]
    
    for date in test_dates:
        biz = business_indicator[date]
        last_day_val = last_day_indicator[date]
        month_begin_val = month_begin_indicator[date]
        day_name = date._value.strftime('%A')
        
        biz_str = "Business Day" if biz == 1.0 else "Weekend"
        last_str = "Last Day" if last_day_val == 1.0 else "Not Last"
        begin_str = "First Day" if month_begin_val == 1.0 else "Not First"
        
        print(f"  {date} ({day_name}): {biz_str}, {last_str}, {begin_str}")
    
    print()

def demo_datch():
    """Demonstrate DatCh usage"""
    print("📊 DatCh (Dating Change) Demo")
    print("=" * 50)
    
    # Create sample daily data (e.g., stock prices)
    start = Date("y2023m06d01") 
    daily = TimeSet("daily")
    
    # Generate 90 days of mock stock price data
    np.random.seed(42)  # For reproducible results
    base_price = 100
    prices = []
    current_price = base_price
    
    for i in range(90):
        # Random walk with slight upward trend
        change = np.random.normal(0.02, 1.5)  # 2% daily trend, 1.5% volatility
        current_price *= (1 + change/100)
        prices.append(current_price)
    
    daily_prices = Serie(data=prices, first_date=start, dating=daily)
    
    print(f"Daily stock prices: {len(daily_prices)} days")
    print(f"  Start: ${daily_prices[start]:.2f}")
    print(f"  End: ${daily_prices[daily_prices.last_date]:.2f}")
    
    # Convert to weekly closing prices (last value of each week)
    weekly = TimeSet("weekly")
    weekly_close = daily_prices.dat_ch(weekly, SerieOperations.last_s)
    
    print(f"\nWeekly closing prices:")
    current = weekly_close.first_date
    count = 0
    while current <= weekly_close.last_date and count < 5:
        price = weekly_close[current]
        print(f"  Week ending {current}: ${price:.2f}")
        current = weekly.successor(current)
        count += 1
        if not current.is_normal():
            break
    
    # Convert to monthly averages
    monthly = TimeSet("monthly")
    monthly_avg = daily_prices.dat_ch(monthly, SerieOperations.avr_s)
    
    print(f"\nMonthly average prices:")
    current = monthly_avg.first_date
    while current <= monthly_avg.last_date:
        price = monthly_avg[current]
        print(f"  {current._value.strftime('%B %Y')}: ${price:.2f}")
        current = monthly.successor(current)
        if not current.is_normal():
            break
    
    print()

def demo_combined():
    """Demonstrate combining CalInd and DatCh"""
    print("🔗 Combined CalInd + DatCh Demo")
    print("=" * 50)
    
    # Create daily sales data
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Generate sales data (higher on weekdays)
    sales_data = []
    current = start
    np.random.seed(123)
    
    for i in range(60):  # 2 months
        weekday = current._value.weekday()
        if weekday < 5:  # Weekday (0=Monday, 4=Friday)
            base_sales = 5000  # Higher weekday sales
        else:  # Weekend
            base_sales = 1500  # Lower weekend sales
            
        # Add some random variation
        sales = base_sales + np.random.normal(0, 500)
        sales_data.append(max(0, sales))  # No negative sales
        current = daily.successor(current)
    
    daily_sales = Serie(data=sales_data, first_date=start, dating=daily)
    
    # Create business days indicator
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])
    biz_indicator = Serie.cal_ind(weekdays, daily, start, 
                                 daily_sales.last_date)
    
    # Calculate weekday-only sales
    weekday_sales = daily_sales * biz_indicator
    
    print("Sales Analysis:")
    print(f"  Total daily sales (60 days): ${daily_sales.sum():,.2f}")
    print(f"  Weekday-only sales: ${weekday_sales.sum():,.2f}")
    print(f"  Weekend percentage: {((daily_sales.sum() - weekday_sales.sum()) / daily_sales.sum() * 100):.1f}%")
    
    # Convert to monthly totals
    monthly = TimeSet("monthly")
    monthly_total = daily_sales.dat_ch(monthly, SerieOperations.sum_s_range)
    monthly_weekday = weekday_sales.dat_ch(monthly, SerieOperations.sum_s_range)
    
    print(f"\nMonthly sales breakdown:")
    current = monthly_total.first_date
    while current <= monthly_total.last_date:
        total = monthly_total[current]
        weekday = monthly_weekday[current]
        weekend = total - weekday
        month_name = current._value.strftime('%B')
        
        print(f"  {month_name}: Total ${total:,.0f} (Weekday: ${weekday:,.0f}, Weekend: ${weekend:,.0f})")
        
        current = monthly.successor(current)
        if not current.is_normal():
            break
    
    print()

if __name__ == "__main__":
    print("TOL Python: DatCh and CalInd Function Demo")
    print("=" * 60)
    print()
    
    demo_calind()
    demo_datch()
    demo_combined()
    
    print("=" * 60)
    print("✅ Demo completed!")
    print()
    print("Key functions implemented:")
    print("  • Serie.cal_ind(timeset, dating, start, end) - Calendar indicators")
    print("  • serie.dat_ch(new_dating, statistic) - Frequency conversion")
    print("  • Statistics: avr_s, sum_s_range, first_s, last_s")
    print()
    print("💡 These functions enable powerful time series analysis:")
    print("  - Create business day indicators")
    print("  - Convert daily to weekly/monthly data")  
    print("  - Combine indicators with data for filtering")
    print("  - Generate reports with different time aggregations")