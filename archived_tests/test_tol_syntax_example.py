"""
Test the exact TOL syntax example: M(1)*(D(15)+D(2))
This demonstrates the clean API after removing unnecessary parameters
"""

from core import Date, TimeSet

def test_tol_syntax():
    print("🎯 TOL SYNTAX TEST: M(1)*(D(15)+D(2))")
    print("=" * 50)
    
    # TOL: M(1)*(D(15)+D(2))
    # TOL Python: TimeSet.m(1) * (TimeSet.day(15) + TimeSet.day(2))
    
    january_2nd_and_15th = TimeSet.m(1) * (TimeSet.day(15) + TimeSet.day(2))
    
    print(f"Created TimeSet: {january_2nd_and_15th}")
    print()
    
    # Test dates
    test_cases = [
        ("y2023m01d02", "January 2nd", "✅ Should match"),
        ("y2023m01d15", "January 15th", "✅ Should match"), 
        ("y2023m01d10", "January 10th", "❌ Should NOT match"),
        ("y2023m02d02", "February 2nd", "❌ Should NOT match (not January)"),
        ("y2023m02d15", "February 15th", "❌ Should NOT match (not January)"),
        ("y2024m01d02", "January 2nd (2024)", "✅ Should match"),
        ("y2024m01d15", "January 15th (2024)", "✅ Should match"),
    ]
    
    print("Testing dates:")
    print("-" * 60)
    for date_str, description, expected in test_cases:
        date = Date(date_str)
        actual = january_2nd_and_15th.includes(date)
        status = "✅ MATCH" if actual else "❌ no match"
        result = "CORRECT" if (actual and "✅" in expected) or (not actual and "❌" in expected) else "ERROR"
        
        print(f"{date} ({description:20s}): {status:10s} - {result}")
    
    print()
    print("🔧 Breaking down the expression:")
    print("   M(1)           = January months")
    print("   D(15)          = 15th days") 
    print("   D(2)           = 2nd days")
    print("   D(15) + D(2)   = 15th OR 2nd days")
    print("   M(1) * (...)   = January AND (15th OR 2nd)")
    print("   Result         = January 2nd and January 15th of any year")
    
    print()
    print("✅ Perfect! The syntax matches TOL exactly:")
    print("   TOL:        M(1)*(D(15)+D(2))")
    print("   TOL Python: TimeSet.m(1) * (TimeSet.day(15) + TimeSet.day(2))")

if __name__ == "__main__":
    test_tol_syntax()