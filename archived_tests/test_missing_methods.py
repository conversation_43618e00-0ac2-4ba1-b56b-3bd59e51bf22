#!/usr/bin/env python3
"""
Test for missing methods found during user testing
"""

import sys
import os
import numpy as np

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from series.serie import Serie
from core.dates import Date, TimeSet

print("🧪 Testing missing methods from user guide")
print("=" * 60)

# Test 1: subseries_by_date method
print("\n1. Testing subseries_by_date method:")
np.random.seed(42)
data = np.random.randn(100).tolist()
start_date = Date('y2023m01d01')
daily_series = Serie(data=data, first_date=start_date, dating=TimeSet('daily'))

q1_start = Date('y2023m01d01')
q1_end = Date('y2023m03d31')
q1_data = daily_series.subseries_by_date(q1_start, q1_end)

print(f"   Original length: {len(daily_series)}")
print(f"   Q1 observations: {len(q1_data)}")
print(f"   Q1 start: {q1_data.first_date}")
print(f"   Q1 end: {q1_data.last_date}")
print("   ✓ subseries_by_date works!")

# Test 2: Also test sub_ser (original method)
print("\n2. Testing sub_ser method (original):")
q2_start = Date('y2023m04d01')
q2_end = Date('y2023m06d30')
q2_data = daily_series.sub_ser(q2_start, q2_end)

print(f"   Q2 observations: {len(q2_data)}")
print(f"   Q2 start: {q2_data.first_date}")
print(f"   Q2 end: {q2_data.last_date}")
print("   ✓ sub_ser works!")

# Test 3: Date.range method  
print("\n3. Testing Date.range method:")
date1 = Date("y2023m06d15")
date2 = Date("y2023m06d20")
date_range = Date.range(date1, date2, TimeSet("daily"))

print(f"   Range length: {len(date_range)}")
print(f"   First date: {date_range[0]}")
print(f"   Last date: {date_range[-1]}")
print("   ✓ Date.range works!")

# Test 4: Integer data handling
print("\n4. Testing integer data with to_numpy():")
int_data = [1, 2, 3, 4, 5]
int_serie = Serie(data=int_data)
ma3 = int_serie.moving_average(3)

print(f"   Original type: {int_serie._data._data.dtype}")
print(f"   MA(3) values: {ma3._data.to_numpy()}")
print("   ✓ Integer data to_numpy() works!")

print("\n" + "=" * 60)
print("🎉 ALL MISSING METHODS TESTS PASSED!")
print("\nThe following methods are now available:")
print("- Serie.subseries_by_date(start, end)")
print("- Serie.sub_ser(start, end)")  
print("- Date.range(start, end, timeset)")
print("- Fixed Data.to_numpy() for integer data")