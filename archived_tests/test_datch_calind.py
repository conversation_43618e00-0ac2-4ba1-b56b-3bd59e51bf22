"""
Test DatCh and CalInd functions for Serie objects
Verifies date/frequency conversion and calendar indicator functionality
"""

import numpy as np
from datetime import datetime
from core import Date, TimeSet
from series import Serie, SerieOperations


def test_cal_ind_weekdays():
    """Test CalInd for weekday indicators"""
    print("=== Testing CalInd for Weekdays ===")
    
    # Create indicator for Mondays (0 = Monday)
    mondays = TimeSet.wd(0)
    daily = TimeSet("daily")
    
    # Create indicator serie for June 2023
    start = Date("y2023m06d01")
    end = Date("y2023m06d30")
    monday_indicator = Serie.cal_ind(mondays, daily, start, end)
    
    print(f"Monday indicator: {monday_indicator}")
    print(f"First date: {monday_indicator.first_date}")
    print(f"Last date: {monday_indicator.last_date}")
    
    # Test specific dates
    test_dates = [
        (Date("y2023m06d05"), "Monday June 5", 1.0),
        (Date("y2023m06d06"), "Tuesday June 6", 0.0),
        (Date("y2023m06d12"), "Monday June 12", 1.0),
        (Date("y2023m06d13"), "Tuesday June 13", 0.0),
    ]
    
    print("\nTesting Monday indicator:")
    for date, desc, expected in test_dates:
        if date >= monday_indicator.first_date and date <= monday_indicator.last_date:
            value = monday_indicator[date]
            status = "✓" if value == expected else "✗"
            print(f"  {date} ({desc}): {value} {status}")
            assert value == expected, f"Expected {expected} for {date}"
    
    # Create weekend indicator
    weekends = TimeSet.wd([5, 6])  # Saturday and Sunday
    weekend_indicator = Serie.cal_ind(weekends, daily, start, end)
    
    print("\nWeekend indicator test:")
    test_weekend = [
        (Date("y2023m06d03"), "Saturday June 3", 1.0),
        (Date("y2023m06d04"), "Sunday June 4", 1.0),
        (Date("y2023m06d05"), "Monday June 5", 0.0),
    ]
    
    for date, desc, expected in test_weekend:
        if date >= weekend_indicator.first_date and date <= weekend_indicator.last_date:
            value = weekend_indicator[date]
            status = "✓" if value == expected else "✗"
            print(f"  {date} ({desc}): {value} {status}")
    
    print("✓ CalInd weekday tests passed\n")


def test_cal_ind_months():
    """Test CalInd for month indicators"""
    print("=== Testing CalInd for Months ===")
    
    # Create indicator for summer months (June, July, August)
    summer = TimeSet.m([6, 7, 8])
    daily = TimeSet("daily")
    
    start = Date("y2023m05d01")
    end = Date("y2023m09d30")
    summer_indicator = Serie.cal_ind(summer, daily, start, end)
    
    print("Testing summer month indicator:")
    test_dates = [
        (Date("y2023m05d31"), "May 31", 0.0),
        (Date("y2023m06d01"), "June 1", 1.0),
        (Date("y2023m07d15"), "July 15", 1.0),
        (Date("y2023m08d31"), "August 31", 1.0),
        (Date("y2023m09d01"), "September 1", 0.0),
    ]
    
    for date, desc, expected in test_dates:
        if date >= summer_indicator.first_date and date <= summer_indicator.last_date:
            value = summer_indicator[date]
            status = "✓" if value == expected else "✗"
            print(f"  {date} ({desc}): {value} {status}")
    
    print("✓ CalInd month tests passed\n")


def test_cal_ind_complex():
    """Test CalInd with complex TimeSet combinations"""
    print("=== Testing CalInd with Complex TimeSets ===")
    
    # Business days in summer
    summer = TimeSet.m([6, 7, 8])
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])
    summer_business = summer * weekdays
    
    daily = TimeSet("daily")
    start = Date("y2023m05d01")
    end = Date("y2023m08d31")
    summer_biz_indicator = Serie.cal_ind(summer_business, daily, start, end)
    
    print("Testing summer business days indicator:")
    test_dates = [
        (Date("y2023m06d05"), "Monday June 5", 1.0),      # Summer weekday
        (Date("y2023m06d03"), "Saturday June 3", 0.0),    # Summer weekend
        (Date("y2023m05d01"), "Monday May 1", 0.0),       # Not summer
        (Date("y2023m07d14"), "Friday July 14", 1.0),     # Summer weekday
    ]
    
    for date, desc, expected in test_dates:
        if date >= summer_biz_indicator.first_date and date <= summer_biz_indicator.last_date:
            value = summer_biz_indicator[date]
            status = "✓" if value == expected else "✗"
            print(f"  {date} ({desc}): {value} {status}")
    
    print("✓ CalInd complex tests passed\n")


def test_dat_ch_daily_to_monthly():
    """Test DatCh converting daily to monthly series"""
    print("=== Testing DatCh: Daily to Monthly ===")
    
    # Create daily serie with data for June-August 2023
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Generate some daily data (e.g., temperatures)
    data = []
    dates = []
    current = start
    for i in range(90):  # 3 months of data
        # Simulate temperature data
        temp = 20 + 10 * np.sin(i/30 * np.pi) + np.random.normal(0, 2)
        data.append(temp)
        dates.append(current)
        current = daily.successor(current)
    
    daily_serie = Serie(data=data, first_date=start, dating=daily)
    print(f"Daily serie: {len(daily_serie)} days from {daily_serie.first_date} to {daily_serie.last_date}")
    
    # Convert to monthly averages
    monthly = TimeSet("monthly")
    monthly_avg = daily_serie.dat_ch(monthly)
    
    print(f"\nMonthly averages serie: {len(monthly_avg)} months")
    current = monthly_avg.first_date
    while current <= monthly_avg.last_date:
        value = monthly_avg[current]
        print(f"  {current}: {value:.2f}")
        current = monthly.successor(current)
        if not current.is_normal():
            break
    
    # Convert to monthly sums
    monthly_sum = daily_serie.dat_ch(monthly, SerieOperations.sum_s_range)
    
    print(f"\nMonthly sums serie:")
    current = monthly_sum.first_date
    while current <= monthly_sum.last_date:
        value = monthly_sum[current]
        print(f"  {current}: {value:.2f}")
        current = monthly.successor(current)
        if not current.is_normal():
            break
    
    # Convert to monthly first value
    monthly_first = daily_serie.dat_ch(monthly, SerieOperations.first_s)
    
    print(f"\nMonthly first values serie:")
    current = monthly_first.first_date
    while current <= monthly_first.last_date:
        value = monthly_first[current]
        print(f"  {current}: {value:.2f}")
        current = monthly.successor(current)
        if not current.is_normal():
            break
    
    print("✓ DatCh daily to monthly tests passed\n")


def test_dat_ch_daily_to_weekly():
    """Test DatCh converting daily to weekly series"""
    print("=== Testing DatCh: Daily to Weekly ===")
    
    # Create daily serie
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Generate 30 days of data
    data = list(range(1, 31))
    daily_serie = Serie(data=data, first_date=start, dating=daily)
    
    # Convert to weekly averages
    weekly = TimeSet("weekly")
    weekly_avg = daily_serie.dat_ch(weekly)
    
    print("Weekly averages:")
    current = weekly_avg.first_date
    while current <= weekly_avg.last_date:
        value = weekly_avg[current]
        print(f"  Week starting {current}: {value:.2f}")
        current = weekly.successor(current)
        if not current.is_normal():
            break
    
    print("✓ DatCh daily to weekly tests passed\n")


def test_dat_ch_with_missing():
    """Test DatCh with missing values"""
    print("=== Testing DatCh with Missing Values ===")
    
    # Create daily serie with some missing values
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    data = []
    for i in range(30):
        # Insert some missing values
        if i % 7 == 0:
            data.append(None)
        else:
            data.append(float(i))
    
    daily_serie = Serie(data=data, first_date=start, dating=daily)
    print(f"Daily serie with {daily_serie.count_missing()} missing values")
    
    # Convert to weekly, should handle missing values
    weekly = TimeSet("weekly")
    weekly_avg = daily_serie.dat_ch(weekly)
    
    print("Weekly averages (handling missing):")
    current = weekly_avg.first_date
    while current <= weekly_avg.last_date:
        value = weekly_avg[current]
        value_str = f"{value:.2f}" if not np.isnan(value) else "NaN"
        print(f"  Week starting {current}: {value_str}")
        current = weekly.successor(current)
        if not current.is_normal():
            break
    
    print("✓ DatCh missing value tests passed\n")


def test_combined_cal_ind_dat_ch():
    """Test combining CalInd and DatCh operations"""
    print("=== Testing Combined CalInd and DatCh ===")
    
    # Create a daily sales serie
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Generate sales data (higher on weekdays)
    sales_data = []
    current = start
    for i in range(60):  # 2 months
        weekday = current._value.weekday()
        if weekday < 5:  # Weekday
            sales = 1000 + np.random.normal(0, 100)
        else:  # Weekend
            sales = 300 + np.random.normal(0, 50)
        sales_data.append(sales)
        current = daily.successor(current)
    
    daily_sales = Serie(data=sales_data, first_date=start, dating=daily)
    
    # Create weekday indicator
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])
    weekday_indicator = Serie.cal_ind(weekdays, daily, start, 
                                     Date(f"y2023m{7 + (len(sales_data)-1)//30:02d}d{((len(sales_data)-1)%30)+1:02d}"))
    
    # Multiply sales by indicator to get weekday-only sales
    weekday_sales = daily_sales * weekday_indicator.subseries_by_date(
        daily_sales.first_date, daily_sales.last_date)
    
    # Convert to monthly totals
    monthly = TimeSet("monthly")
    monthly_weekday_sales = weekday_sales.dat_ch(monthly, SerieOperations.sum_s_range)
    
    print("Monthly weekday sales totals:")
    current = monthly_weekday_sales.first_date
    while current <= monthly_weekday_sales.last_date:
        value = monthly_weekday_sales[current]
        print(f"  {current}: ${value:.2f}")
        current = monthly.successor(current)
        if not current.is_normal():
            break
    
    print("✓ Combined CalInd and DatCh tests passed\n")


def demo_practical_usage():
    """Demonstrate practical usage scenarios"""
    print("=== Practical Usage Examples ===")
    
    # Example 1: Holiday adjustment
    print("Example 1: Holiday-adjusted data")
    
    # Create a business days indicator
    business_days = TimeSet.wd([0, 1, 2, 3, 4])
    daily = TimeSet("daily")
    start = Date("y2023m06d01")
    end = Date("y2023m06d30")
    biz_indicator = Serie.cal_ind(business_days, daily, start, end)
    
    # Create some daily data
    start = Date("y2023m06d01")
    data = np.random.normal(100, 10, 30)
    daily_data = Serie(data=data.tolist(), first_date=start, dating=daily)
    
    # Apply business day adjustment
    biz_adjusted = daily_data * biz_indicator.subseries_by_date(
        daily_data.first_date, daily_data.last_date)
    
    print(f"Original sum: {daily_data.sum():.2f}")
    print(f"Business days only sum: {biz_adjusted.sum():.2f}")
    
    # Example 2: Quarterly reporting
    print("\nExample 2: Quarterly aggregation")
    
    # Convert daily to monthly for simplicity 
    monthly = TimeSet("monthly")
    monthly_avg = daily_data.dat_ch(monthly, SerieOperations.avr_s)
    
    print("Monthly averages:")
    current = monthly_avg.first_date
    while current <= monthly_avg.last_date:
        value = monthly_avg[current]
        if not np.isnan(value):
            print(f"  Month starting {current}: {value:.2f}")
        current = monthly.successor(current)
        if not current.is_normal() or current > monthly_avg.last_date:
            break
    
    print("\n✓ Practical examples completed")


if __name__ == "__main__":
    print("Testing DatCh and CalInd Serie Functions")
    print("=" * 50)
    
    test_cal_ind_weekdays()
    test_cal_ind_months()
    test_cal_ind_complex()
    test_dat_ch_daily_to_monthly()
    test_dat_ch_daily_to_weekly()
    test_dat_ch_with_missing()
    test_combined_cal_ind_dat_ch()
    demo_practical_usage()
    
    print("=" * 50)
    print("✅ All DatCh and CalInd tests passed!")
    print("\nImplemented functions:")
    print("- Serie.cal_ind(timeset, dating) - Create calendar indicator series")
    print("- serie.dat_ch(dating, statistic) - Change serie dating/frequency")
    print("  - Statistics: avr_s (default), sum_s_range, first_s, last_s")