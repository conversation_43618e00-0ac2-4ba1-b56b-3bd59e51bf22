#!/usr/bin/env python3
"""
Test script to verify imports work correctly
Run this from the parent directory of tol_python
"""

import sys
import os

# Add the parent directory to Python path so we can import tol_python as a package
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

print("Testing TOL Python imports...")
print(f"Current directory: {current_dir}")
print(f"Parent directory: {parent_dir}")
print(f"Python path: {sys.path[:3]}...")

try:
    # Test importing from the tol_python package
    print("\n1. Testing core imports...")
    from tol_python.core.dates import Date, TimeSet
    print("✓ Core dates imported successfully")
    
    from tol_python.core.data import Data
    print("✓ Core data imported successfully")
    
    print("\n2. Testing Serie import...")
    from tol_python.series.serie import Serie
    print("✓ Serie imported successfully")
    
    print("\n3. Testing Serie creation...")
    # Test basic Serie creation
    test_data = [1.0, 2.0, 3.0, 4.0, 5.0]
    serie = Serie(data=test_data)
    print(f"✓ Serie created with {len(serie)} observations")
    
    print("\n4. Testing Date creation...")
    date1 = Date("y2023m06d15")
    print(f"✓ Date created: {date1}")
    
    print("\n5. Testing basic operations...")
    # Test basic operations
    serie2 = Serie(data=[10.0, 20.0, 30.0, 40.0, 50.0])
    result = serie + serie2
    print(f"✓ Basic arithmetic works: {result._data.to_numpy()[:3]}")
    
    print("\n6. Testing statistical imports...")
    from tol_python.stats.statistics import mean, variance
    mean_val = mean(serie)
    var_val = variance(serie)
    print(f"✓ Statistics work: mean={mean_val:.2f}, variance={var_val:.2f}")
    
    print("\n🎉 ALL IMPORTS WORKING CORRECTLY!")
    print("\nHow to use in your code:")
    print("=" * 50)
    print("# Add this to the top of your script:")
    print("import sys")
    print("sys.path.append('/Users/<USER>/Documents/Tol')  # Path to TOL directory")
    print("")
    print("# Then import normally:")
    print("from tol_python.series.serie import Serie")
    print("from tol_python.core.dates import Date, TimeSet")
    print("from tol_python.stats.statistics import mean, variance")
    print("from tol_python.arima import ARIMA, ARIMAFactor")
    print("from tol_python.bayesian import BayesianARIMA, MCMCConfig")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\nTrying alternative import method...")
    
    try:
        # Alternative method - direct imports with sys.path modification
        tol_python_dir = current_dir
        if tol_python_dir not in sys.path:
            sys.path.insert(0, tol_python_dir)
        
        print(f"Added {tol_python_dir} to Python path")
        
        # Try importing with absolute imports
        from core.dates import Date, TimeSet
        from core.data import Data
        from series.serie import Serie
        
        print("✓ Alternative import method works!")
        
        # Test basic functionality
        test_data = [1.0, 2.0, 3.0, 4.0, 5.0]
        serie = Serie(data=test_data)
        print(f"✓ Serie created with {len(serie)} observations")
        
        print("\n🎉 ALTERNATIVE IMPORTS WORKING!")
        print("\nTo use this method, add this to your script:")
        print("=" * 50)
        print("import sys")
        print(f"sys.path.append('{tol_python_dir}')")
        print("")
        print("# Then import directly:")
        print("from series.serie import Serie")
        print("from core.dates import Date, TimeSet")
        print("from stats.statistics import mean, variance")
        
    except Exception as e2:
        print(f"❌ Alternative method also failed: {e2}")
        print("\nLet's create a simple fix...")
        
        # Create a simple solution
        solution = """
SOLUTION: The relative imports are causing issues. Here are 3 ways to fix this:

METHOD 1 - Use as a package (Recommended):
1. Navigate to the parent directory of tol_python:
   cd /Users/<USER>/Documents/Tol

2. Start Python from there:
   python3

3. Import using the package name:
   from tol_python.series.serie import Serie
   from tol_python.core.dates import Date

METHOD 2 - Add to Python path:
import sys
sys.path.append('/Users/<USER>/Documents/Tol/tol_python')
from series.serie import Serie
from core.dates import Date

METHOD 3 - Install as package:
1. Navigate to tol_python directory
2. Create setup.py (I'll generate this)
3. Run: pip install -e .

Would you like me to implement any of these solutions?
"""
        print(solution)

if __name__ == "__main__":
    print("\nTest completed!")