"""
Test enhanced ARIMA features: diagnostics and auto-selection
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from tol_python.series import Serie
from tol_python.arima import ARIMAFactor, ARIMA, AutoARIMA


def test_residual_diagnostics():
    """Test comprehensive residual diagnostics"""
    print("Testing Residual Diagnostics")
    print("=" * 50)
    
    # Generate ARMA(1,1) data with some issues
    np.random.seed(42)
    n = 200
    phi = 0.7
    theta = 0.4
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    # Add some heteroscedasticity and outliers
    for t in range(1, n):
        # Increasing variance over time
        sigma_t = 1 + 0.01 * t
        errors[t] = np.random.normal(0, sigma_t)
        
        # Add outliers
        if np.random.random() < 0.02:
            errors[t] *= 5
        
        # ARMA process
        y[t] = phi * y[t-1] + errors[t] + theta * errors[t-1]
    
    serie = Serie(data=y)
    
    # Fit ARMA(1,1) model
    factor = ARIMAFactor(ar_order=1, ma_order=1, include_mean=False)
    model = ARIMA(factor)
    results = model.fit(serie, method="css")
    
    print(f"Model fitted: AR={results.ar_params[0]:.3f}, MA={results.ma_params[0]:.3f}")
    
    # Get diagnostics
    try:
        diagnostics = results.diagnostics()
        
        print("\nDiagnostic Results:")
        print("-" * 30)
        
        # Ljung-Box test
        ljung = diagnostics['ljung_box_10']
        print(f"Ljung-Box (10 lags):")
        print(f"  Statistic: {ljung['statistic']:.2f}")
        print(f"  p-value: {ljung['p_value']:.4f}")
        print(f"  No autocorrelation: {'✓' if ljung['p_value'] > 0.05 else '✗'}")
        
        # Normality
        norm = diagnostics['normality']
        print(f"\nNormality (Jarque-Bera):")
        print(f"  Statistic: {norm['jarque_bera']['statistic']:.2f}")
        print(f"  p-value: {norm['jarque_bera']['p_value']:.4f}")
        print(f"  Skewness: {norm['skewness']:.3f}")
        print(f"  Excess kurtosis: {norm['excess_kurtosis']:.3f}")
        
        # ARCH test
        arch = diagnostics['arch_lm']
        print(f"\nARCH-LM (5 lags):")
        print(f"  Statistic: {arch['statistic']:.2f}")
        print(f"  p-value: {arch['p_value']:.4f}")
        print(f"  Homoscedastic: {'✓' if not arch['reject_homoscedasticity'] else '✗'}")
        
        # Outliers
        out = diagnostics['outliers_zscore']
        print(f"\nOutliers (|z| > 3):")
        print(f"  Count: {out['n_outliers']}")
        print(f"  Percentage: {out['pct_outliers']:.1f}%")
        
        # Overall assessment
        adequacy = diagnostics['model_adequate']
        print(f"\nModel Adequacy:")
        print(f"  Overall: {'✓ Adequate' if adequacy['overall'] else '✗ Needs improvement'}")
        
        # Print summary
        print("\n" + results.diagnostic_summary())
        
        return True
        
    except Exception as e:
        print(f"Diagnostics failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_auto_arima():
    """Test automatic ARIMA model selection"""
    print("\n\nTesting AutoARIMA")
    print("=" * 50)
    
    # Generate ARIMA(2,1,1) data
    np.random.seed(123)
    n = 150
    
    # Parameters
    phi1, phi2 = 0.6, -0.3
    theta = 0.4
    drift = 0.02
    
    # Generate differences (stationary)
    w = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(2, n):
        w[t] = drift + phi1 * w[t-1] + phi2 * w[t-2] + errors[t] + theta * errors[t-1]
    
    # Integrate to get I(1) series
    y = np.cumsum(w)
    serie = Serie(data=y)
    
    print("Generated ARIMA(2,1,1) series with drift")
    
    # Run auto.arima
    try:
        auto = AutoARIMA(
            max_p=3, max_d=2, max_q=3,
            seasonal=False,
            ic='aic',
            stepwise=True,
            method='css',
            trace=True
        )
        
        print("\nSearching for optimal model...")
        best_model = auto.fit(serie, trace=True)
        
        print("\n" + auto.summary())
        
        # Check if it found reasonable model
        results = best_model.results
        if results is not None:
            print(f"\nFinal model parameters:")
            print(f"  AR: {results.ar_params}")
            print(f"  MA: {results.ma_params}")
            if results.intercept is not None:
                print(f"  Intercept/Drift: {results.intercept:.4f}")
        
        return True
        
    except Exception as e:
        print(f"AutoARIMA failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_seasonal_auto_arima():
    """Test auto.arima with seasonal data"""
    print("\n\nTesting Seasonal AutoARIMA")
    print("=" * 50)
    
    # Generate seasonal ARIMA(1,0,0)(1,0,0)[12] data
    np.random.seed(456)
    n = 120  # 10 years monthly
    
    # Parameters
    phi = 0.5      # AR(1)
    Phi = 0.6      # Seasonal AR(1)
    season = 12
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    # Add seasonal pattern
    seasonal_component = 5 * np.sin(2 * np.pi * np.arange(n) / season)
    
    for t in range(season, n):
        y[t] = (phi * y[t-1] + 
                Phi * y[t-season] + 
                errors[t] + 
                seasonal_component[t])
    
    serie = Serie(data=y)
    print("Generated seasonal ARIMA(1,0,0)(1,0,0)[12] series")
    
    # Run seasonal auto.arima
    try:
        auto = AutoARIMA(
            max_p=2, max_d=1, max_q=2,
            max_P=1, max_D=1, max_Q=1,
            seasonal=True,
            season_length=12,
            ic='bic',  # BIC penalizes complexity more
            stepwise=True,
            method='css',
            trace=False  # Less verbose
        )
        
        print("\nSearching for optimal seasonal model...")
        best_model = auto.fit(serie)
        
        print("\n" + auto.summary())
        
        return True
        
    except Exception as e:
        print(f"Seasonal AutoARIMA failed: {e}")
        return False


def test_mle_with_diagnostics():
    """Test MLE estimation with full diagnostics"""
    print("\n\nTesting MLE with Diagnostics")
    print("=" * 50)
    
    # Generate clean AR(2) data
    np.random.seed(789)
    n = 200
    phi1, phi2 = 0.5, 0.3
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(2, n):
        y[t] = phi1 * y[t-1] + phi2 * y[t-2] + errors[t]
    
    serie = Serie(data=y)
    
    # Fit with MLE
    factor = ARIMAFactor(ar_order=2, include_mean=False)
    model = ARIMA(factor)
    
    try:
        results = model.fit(serie, method="mle")
        
        print(f"MLE Results:")
        print(f"  AR(1): {results.ar_params[0]:.3f} ± {results.standard_errors['ar_se'][0]:.3f}")
        print(f"  AR(2): {results.ar_params[1]:.3f} ± {results.standard_errors['ar_se'][1]:.3f}")
        print(f"  Log-likelihood: {results.loglikelihood:.2f}")
        print(f"  AIC: {results.aic:.2f}")
        
        # Get and display diagnostics
        diagnostics = results.diagnostics()
        
        # Check if residuals are well-behaved
        ljung = diagnostics['ljung_box_10']
        norm = diagnostics['normality']['jarque_bera']
        
        print(f"\nResidual Diagnostics:")
        print(f"  Ljung-Box p-value: {ljung['p_value']:.3f} {'(good)' if ljung['p_value'] > 0.05 else '(bad)'}")
        print(f"  Jarque-Bera p-value: {norm['p_value']:.3f} {'(good)' if norm['p_value'] > 0.05 else '(bad)'}")
        
        # Overall assessment
        adequacy = diagnostics['model_adequate']['overall']
        print(f"  Model adequate: {'Yes ✓' if adequacy else 'No ✗'}")
        
        return True
        
    except Exception as e:
        print(f"MLE with diagnostics failed: {e}")
        return False


if __name__ == "__main__":
    print("Enhanced ARIMA Test Suite")
    print("=" * 60)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_residual_diagnostics()
    all_passed &= test_auto_arima()
    all_passed &= test_seasonal_auto_arima()
    all_passed &= test_mle_with_diagnostics()
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All enhanced ARIMA tests passed!")
        print("\nPhase 4 Complete - Enhanced ARIMA Features:")
        print("✓ Kalman filter MLE with standard errors")
        print("✓ Comprehensive residual diagnostics")
        print("✓ Automatic model selection (auto.arima)")
        print("✓ Seasonal model support")
        print("✓ Model adequacy assessment")
        print("\nTOL's advanced ARIMA capabilities fully migrated to Python!")
    else:
        print("❌ Some enhanced tests failed.")
        print("Please check the implementation.")
    
    print("\nPhase 4 implementation complete! ✨")