"""
Demonstration of why dates_of_set should return a TimeSet
Shows the consistency and composability benefits
"""

from core import Date, TimeSet

def demonstrate_timeset_consistency():
    print("=== TimeSet Function Consistency Demonstration ===\n")
    
    # All TimeSet functions return TimeSets - now including dates_of_set
    weekdays = TimeSet.wd([0, 1, 2, 3, 4])  # Returns TimeSet ✓
    summer = TimeSet.m(Date("y2023m01d01"), [6, 7, 8])  # Returns TimeSet ✓
    business_dates = weekdays.dates_of_set(Date("y2023m06d01"), 10)  # Now returns TimeSet ✓
    
    print(f"Weekdays type: {type(weekdays)}")
    print(f"Summer type: {type(summer)}")
    print(f"Business dates type: {type(business_dates)}")
    print()
    
    # Now we can compose them using set operations
    print("=== Composability Benefits ===")
    
    # Combine the first 10 business days with summer months
    summer_business_subset = business_dates * summer
    print(f"Summer business days in first 10: {summer_business_subset}")
    
    # Find end-of-month dates within our business days
    month_end = TimeSet.day(Date("y2023m01d01"), [28, 29, 30, 31])
    eom_business = business_dates * month_end
    print(f"End-of-month business days: {eom_business}")
    
    # Chain operations
    complex_schedule = (
        business_dates *  # First 10 business days
        summer *          # Only summer months
        TimeSet.day(Date("y2023m01d01"), 15)  # Only 15th of month
    )
    print(f"Complex schedule: {complex_schedule}")
    
    print()
    
    # Show that we can still get the list when needed
    print("=== Getting Lists When Needed ===")
    dates_list = business_dates.get_dates_list()
    print(f"As Python list: {[str(d) for d in dates_list[:3]]}...")
    
    # Test includes() method
    test_date = Date("y2023m06d05")  # Monday, June 5, 2023
    print(f"Does business_dates include {test_date}? {business_dates.includes(test_date)}")
    
    # Navigation still works
    next_date = business_dates.successor(test_date)
    print(f"Next business date after {test_date}: {next_date}")
    
    print()
    
    # Show practical business scenario
    print("=== Practical Business Scenario ===")
    
    # Get next 20 business days
    all_business = weekdays.dates_of_set(Date("y2023m06d01"), 20)
    
    # Filter to only month-end business days within those 20
    month_end_business = all_business * month_end
    month_end_list = month_end_business.to_dates_list(Date("y2023m06d01"), 10)
    
    print(f"Month-end business days in next 20 business days:")
    for date in month_end_list:
        print(f"  - {date} ({date._value.strftime('%A')})")
    
    # Further filter to only Fridays
    fridays = TimeSet.wd(4)  # Friday = 4
    friday_month_end = month_end_business * fridays
    friday_list = friday_month_end.to_dates_list(Date("y2023m06d01"), 5)
    
    print(f"\nMonth-end Fridays:")
    for date in friday_list:
        print(f"  - {date} (Perfect for month-end reports!)")
    
    print()
    print("✅ This demonstrates why ALL TimeSet functions should return TimeSets:")
    print("   - Consistency: All functions have the same return type")
    print("   - Composability: Results can be combined with set operations")
    print("   - Flexibility: Can still get Python lists with get_dates_list()")
    print("   - TOL Compatibility: Matches TOL's behavior exactly")


if __name__ == "__main__":
    demonstrate_timeset_consistency()