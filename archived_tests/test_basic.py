#!/usr/bin/env python3
"""
Basic test demonstrating Serie functionality
Similar to tol_tests/tol/CreationAndCopy/test_serie/test.tol
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tol_python import Serie, Date, TimeSet
import numpy as np


def test_basic_serie():
    """Test basic Serie creation and access"""
    print("=== Basic Serie Test ===\n")
    
    # Create a simple series with daily data
    data = [1.5, 2.3, 3.1, None, 5.2]  # Including a missing value
    s = Serie(data=data, 
              first_date="y2000m01d01",
              last_date="y2000m01d05",
              dating=TimeSet("daily"))
    
    print(f"Serie created: {s}")
    print(f"\nFirst date: {s.first_date}")
    print(f"Last date: {s.last_date}")
    print(f"Length: {len(s)}")
    
    # Access by date
    print("\n--- Access by date ---")
    print(f"Value at y2000m01d01: {s['y2000m01d01']}")
    print(f"Value at y2000m01d04: {s['y2000m01d04']} (missing)")
    
    # Access by index
    print("\n--- Access by index ---")
    print(f"Value at index 0: {s[0]}")
    print(f"Value at index -1: {s[-1]}")
    
    # Modify values
    print("\n--- Modify values ---")
    s["y2000m01d04"] = 4.0
    print(f"After setting y2000m01d04 = 4.0: {s['y2000m01d04']}")
    
    # SubSeries
    print("\n--- SubSeries extraction ---")
    sub = s.sub_ser("y2000m01d02", "y2000m01d04")
    print(f"SubSeries: {sub}")
    
    # Convert to dict
    print("\n--- Serie as dictionary ---")
    print(s.to_dict())


def test_copy_behavior():
    """Test copy behavior similar to TOL test"""
    print("\n\n=== Copy Behavior Test ===\n")
    
    # Create series A and B
    np.random.seed(42)
    data_a = np.random.uniform(1, 2, 10)
    data_b = np.random.uniform(-2, 1, 10)
    
    A = Serie(data=data_a, 
              first_date="y2000m01d01",
              last_date="y2000m01d10")
    
    B = Serie(data=data_b,
              first_date="y2000m01d01", 
              last_date="y2000m01d10")
    
    # Sum of A
    sum_a = sum(A[Date(f"y2000m01d{i:02d}")] for i in range(1, 11))
    print(f"Sum of A: {sum_a:.4f}")
    
    # Sum of B  
    sum_b = sum(B[Date(f"y2000m01d{i:02d}")] for i in range(1, 11))
    print(f"Sum of B: {sum_b:.4f}")
    
    # Copy semantics
    C = A  # Reference copy (Python default)
    C_copy = A.copy()  # Deep copy
    
    # Modify A
    A["y2000m01d01"] = 999.0
    
    print(f"\nAfter modifying A[y2000m01d01] = 999:")
    print(f"A[y2000m01d01] = {A['y2000m01d01']}")
    print(f"C[y2000m01d01] = {C['y2000m01d01']} (reference copy)")
    print(f"C_copy[y2000m01d01] = {C_copy['y2000m01d01']} (deep copy)")


def test_different_frequencies():
    """Test series with different dating frequencies"""
    print("\n\n=== Different Frequencies Test ===\n")
    
    # Monthly series
    monthly_data = [100, 102, 105, 103, 107, 110]
    monthly = Serie(data=monthly_data,
                   first_date="y2023m01d01",
                   last_date="y2023m06d01",
                   dating=TimeSet("monthly"))
    
    print("Monthly Serie:")
    print(monthly)
    
    # Yearly series
    yearly_data = [1000, 1050, 1100, 1200, 1300]
    yearly = Serie(data=yearly_data,
                  first_date="y2020m01d01",
                  last_date="y2024m01d01", 
                  dating=TimeSet("yearly"))
    
    print("\nYearly Serie:")
    print(yearly)


if __name__ == "__main__":
    test_basic_serie()
    test_copy_behavior()
    test_different_frequencies()
    
    # Test pandas integration if available
    try:
        print("\n\n=== Pandas Integration ===")
        s = Serie(data=[1, 2, 3, 4, 5],
                 first_date="y2023m01d01",
                 last_date="y2023m01d05")
        df = s.to_pandas()
        print(f"Converted to pandas Series:\n{df}")
    except ImportError:
        print("\n(pandas not available - skipping pandas integration test)")