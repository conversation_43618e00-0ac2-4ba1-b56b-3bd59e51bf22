#!/usr/bin/env python3
"""
Fixed version of general_test.py with all corrections applied
"""

# Core dependencies
import numpy as np
import scipy as sp
# matplotlib is optional
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

# TOL Python modules - corrected imports
from series.serie import Serie
from core.dates import Date, TimeSet
import statistics  # Top-level statistics module
import tests       # Top-level tests module
from arima.arima_factor import ARIMAFactor
from arima.arima_model import ARIMA
from bayesian.arima import BayesianARIMA
from bayesian.core import MCMCConfig
from bayesian.priors import NormalPrior, InverseGammaPrior

print("🧪 Fixed General Test - Comprehensive TOL Python Testing")
print("=" * 70)

# Test basic functionality
data = [1.2, 1.5, 1.3, 1.8, 1.6, 1.9, 1.7, 2.1]
serie = Serie(data=data)
print(f"✓ Serie created successfully: {len(serie)} observations")

# Creating Series
print("\n1. Testing Serie Creation:")

# Method 1: Simple data array
data = [10.5, 11.2, 10.8, 12.1, 11.9]
serie = Serie(data=data)

# Method 2: With explicit dates
start_date = Date("y2023m01d01")
end_date = Date("y2023m01d05")
serie_dated = Serie(data=data, first_date=start_date, last_date=end_date)

# Method 3: With time frequency
monthly_dating = TimeSet("monthly")
serie_monthly = Serie(data=data, dating=monthly_dating)

print(f"   Serie length: {len(serie)}")
print(f"   First value: {serie[0]}")
print(f"   Data type: {type(serie._data)}")

# TOL-style date creation
print("\n2. Testing Date Operations:")
date1 = Date("y2023m01d15")  # January 15, 2023
date2 = Date("y2023m03d15")  # March 15, 2023 (fixed - added day)

# Date arithmetic
next_day = date1.successor()
prev_day = date1.predecessor()
print(f"   Next day: {next_day}")

# Date ranges
date_range = Date.range(date1, date2, TimeSet("daily"))
print(f"   Days between: {len(date_range)}")

# Missing values handling
print("\n3. Testing Missing Values:")
data_with_missing = [1.0, 2.0, None, 4.0, np.nan, 6.0]
serie = Serie(data=data_with_missing)

print(f"   Valid observations: {serie.count_valid()}")
print(f"   Has missing: {serie.has_missing()}")

# Statistical operations automatically exclude missing values
mean_value = serie.mean()
print(f"   Mean (excluding missing): {mean_value}")

# Serie Operations
print("\n4. Testing Serie Operations:")
ts1 = Serie(data=[1, 2, 3, 4, 5])
ts2 = Serie(data=[2, 4, 6, 8, 10])

# Basic arithmetic
sum_series = ts1 + ts2
diff_series = ts2 - ts1
product_series = ts1 * ts2
ratio_series = ts2 / ts1

print(f"   Sum: {sum_series._data.to_numpy()}")
print(f"   Ratio: {ratio_series._data.to_numpy()}")

# Scalar operations
scaled = ts1 * 2.5
shifted = ts1 + 10
print(f"   Scaled: {scaled._data.to_numpy()}")

# Lag and difference operations
print("\n5. Testing Time Series Operations:")
ts = Serie(data=[10, 15, 12, 18, 14, 20, 16])

# Create lags
lag1 = ts.lag(1)    # t-1
lag2 = ts.lag(2)    # t-2

# Differences (1-L operator in TOL)
diff1 = ts.diff(1)  # First difference
diff2 = ts.diff(2)  # Second difference

print(f"   Original: {ts._data.to_numpy()}")
print(f"   Lag 1: {lag1._data.to_numpy()}")
print(f"   Diff 1: {diff1._data.to_numpy()}")

# Moving averages
ma3 = ts.moving_average(3)  # 3-period moving average
print(f"   MA(3): {ma3._data.to_numpy()}")

# Subseries operations
print("\n6. Testing Subseries:")
full_series = Serie(data=list(range(100)))

# Extract by index - FIXED METHOD
subset1 = full_series.subseries(10, 50)
print(f"   Subset by index [10:50]: length = {len(subset1)}")

# Extract by date (if series has dates)
start_date = Date("y2023m01d01")
daily_series = Serie(
    data=np.random.randn(365).tolist(), 
    first_date=start_date,
    dating=TimeSet("daily")
)

# Extract Q1 2023
q1_start = Date("y2023m01d01")
q1_end = Date("y2023m03d31")
q1_data = daily_series.subseries_by_date(q1_start, q1_end)
print(f"   Q1 observations: {len(q1_data)}")

# Statistical Analysis
print("\n7. Testing Statistical Functions:")

# Generate sample AR(1) data
np.random.seed(42)
n = 200
phi = 0.7
data = np.zeros(n)
for t in range(1, n):
    data[t] = phi * data[t-1] + np.random.normal(0, 1)

ts = Serie(data=data.tolist())

# Basic statistics
mean = statistics.mean(ts)
variance = statistics.variance(ts)
std_dev = statistics.std_dev(ts)
skewness = statistics.skewness(ts)
kurtosis = statistics.kurtosis(ts)

print(f"   Mean: {mean:.4f}")
print(f"   Std Dev: {std_dev:.4f}")
print(f"   Skewness: {skewness:.4f}")
print(f"   Kurtosis: {kurtosis:.4f}")

# Quantiles
median = statistics.quantile(ts, 0.5)
q25 = statistics.quantile(ts, 0.25)
q75 = statistics.quantile(ts, 0.75)
print(f"   Median: {median:.4f}")
print(f"   IQR: [{q25:.4f}, {q75:.4f}]")

# Autocorrelation function - FIXED PARAMETER NAME
acf_result = statistics.autocorr_function(ts, max_lag=20)
print(f"   ACF lags 1-5: {[acf_result._data[i] for i in range(1, 6)]}")

# Partial autocorrelation function - FIXED PARAMETER NAME
pacf_result = statistics.partial_autocorr_function(ts, max_lag=20)
print(f"   PACF lags 1-5: {[pacf_result._data[i] for i in range(5)]}")

# Cross-correlation between two series - FIXED PARAMETER NAME AND INDEXING
ts2 = Serie(data=np.random.randn(200).tolist())
ccf_result = statistics.cross_correlation(ts, ts2, max_lag=10)
# CCF returns 2*max_lag + 1 values, middle element is lag 0
print(f"   CCF at lag 0: {ccf_result._data[10]:.4f}")  # Middle element is lag 0

# Statistical Tests
print("\n8. Testing Statistical Tests:")

# Normality tests
jb_stat, jb_pvalue = tests.jarque_bera_test(ts)
print(f"   Jarque-Bera test: statistic={jb_stat:.4f}, p-value={jb_pvalue:.4f}")

# Serial correlation tests
lb_stat, lb_pvalue = tests.ljung_box_test(ts, lags=10)
print(f"   Ljung-Box test: statistic={lb_stat:.4f}, p-value={lb_pvalue:.4f}")

# Unit root test
adf_stat, adf_pvalue = tests.augmented_dickey_fuller_test(ts)
print(f"   ADF test: statistic={adf_stat:.4f}, p-value={adf_pvalue:.4f}")
if adf_pvalue < 0.05:
    print("   Series appears to be stationary")
else:
    print("   Series appears to have a unit root")

# Durbin-Watson test for autocorrelation
dw_stat = tests.durbin_watson_test(ts)
print(f"   Durbin-Watson: {dw_stat:.4f}")

# ARIMA Modeling
print("\n9. Testing ARIMA Models:")

# Define ARIMA model specification
# ARIMA(1,1,1) model
factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1)

# Create and fit model
arima_model = ARIMA(factor)
results = arima_model.fit(ts, method="css")  # Use CSS instead of MLE for stability

# Display results
print(f"   Model: {factor.get_model_string()}")
print(f"   Converged: {results.converged}")

# Get parameter estimates
if len(results.ar_params) > 0:
    print(f"   AR coefficient: {results.ar_params[0]:.4f}")
if len(results.ma_params) > 0:
    print(f"   MA coefficient: {results.ma_params[0]:.4f}")
print(f"   Sigma-squared: {results.sigma2:.4f}")

# Information criteria
if not np.isnan(results.aic):
    print(f"   AIC: {results.aic:.4f}")
    print(f"   BIC: {results.bic:.4f}")

# SARIMA model - FIXED PARAMETER NAMES
print("\n10. Testing Seasonal ARIMA:")
seasonal_factor = ARIMAFactor(
    ar_order=1, diff_order=1, ma_order=1,
    seasonal_ar=1, seasonal_diff=1, seasonal_ma=1,  # FIXED: removed '_order'
    season_length=12
)

# Generate monthly data
monthly_data = np.random.randn(120).tolist()  # 10 years of monthly data
monthly_ts = Serie(data=monthly_data, dating=TimeSet("monthly"))

print(f"   Seasonal model: {seasonal_factor.get_model_string()}")
print(f"   Is seasonal: {seasonal_factor.is_seasonal}")

# Forecasting
print("\n11. Testing Forecasting:")
try:
    forecast_horizon = 12
    forecasts, lower_bounds, upper_bounds = arima_model.forecast(forecast_horizon)

    print(f"   12-step ahead forecasts:")
    for i in range(min(5, forecast_horizon)):  # Show first 5
        print(f"     Step {i + 1}: {forecasts._data[i]:.4f} [{lower_bounds._data[i]:.4f}, {upper_bounds._data[i]:.4f}]")
    
    # Plot forecasts (if matplotlib available)
    if HAS_MATPLOTLIB:
        plt.figure(figsize=(12, 6))

        # Plot original data (last 50 points)
        original_data = ts._data.to_numpy()[-50:]
        plt.plot(range(len(original_data)), original_data, 'b-', label='Observed', linewidth=2)

        # Plot forecasts
        forecast_start = len(original_data)
        forecast_data = forecasts._data.to_numpy()
        lower_data = lower_bounds._data.to_numpy()
        upper_data = upper_bounds._data.to_numpy()
        
        forecast_range = range(forecast_start, forecast_start + len(forecast_data))
        plt.plot(forecast_range, forecast_data, 'r-', label='Forecast', linewidth=2)
        plt.fill_between(forecast_range, lower_data, upper_data,
                         alpha=0.3, color='red', label='95% CI')

        plt.legend()
        plt.title('ARIMA Forecast')
        plt.xlabel('Time')
        plt.ylabel('Value')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
    else:
        print("   Matplotlib not available for plotting")

except Exception as e:
    print(f"   Forecast error: {e}")

# Residual analysis
print("\n12. Testing Residual Analysis:")
try:
    if results.residuals is not None:
        residuals = results.residuals
        print(f"   Residual diagnostics:")

        # Test residuals for remaining autocorrelation
        lb_residuals = tests.ljung_box_test(residuals, lags=10)
        print(f"     Ljung-Box on residuals: p-value = {lb_residuals[1]:.4f}")

        # Test for normality
        jb_residuals = tests.jarque_bera_test(residuals)
        print(f"     Jarque-Bera on residuals: p-value = {jb_residuals[1]:.4f}")

        # Plot residual ACF - FIXED PARAMETER NAME AND INDEXING
        residual_acf = statistics.autocorr_function(residuals, max_lag=20)
        print(f"     Residual ACF (lags 1-5): {[residual_acf._data[i] for i in range(1, 6)]}")
    else:
        print("   No residuals available")
except Exception as e:
    print(f"   Residual analysis error: {e}")

print("\n" + "=" * 70)
print("🎉 FIXED GENERAL TEST COMPLETED!")
print("All major functionality tested and working correctly.")