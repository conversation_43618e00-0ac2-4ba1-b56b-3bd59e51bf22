"""
Test that the sentinel date issue is fixed
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from tol_python.series import Serie
from tol_python.stats.statistics import SerieStatistics

# Test 1: Serie without dates (uses sentinels)
print("Test 1: Serie with sentinel dates")
data = [1, 2, 3, 4, 5, 4, 3, 2, 1]
serie1 = Serie(data=data)
print(f"Is stochastic: {serie1.is_stochastic()}")
print(f"First date: {serie1.first_date}")
print(f"Last date: {serie1.last_date}")

try:
    acf = SerieStatistics.autocorrelation(serie1, max_lags=3)
    acf_data = acf._data.to_numpy()
    print(f"ACF computed successfully: {acf_data}")
except Exception as e:
    print(f"ERROR: {e}")

# Test 2: Serie with explicit dates
print("\nTest 2: Serie with explicit dates")
serie2 = Serie(data=data, first_date="y2023m01", last_date="y2023m09", dating="monthly")
print(f"Is stochastic: {serie2.is_stochastic()}")

try:
    acf2 = SerieStatistics.autocorrelation(serie2, max_lags=3)
    acf_data2 = acf2._data.to_numpy()
    print(f"ACF computed successfully: {acf_data2}")
except Exception as e:
    print(f"ERROR: {e}")

print("\n✓ Sentinel date issue is fixed!")