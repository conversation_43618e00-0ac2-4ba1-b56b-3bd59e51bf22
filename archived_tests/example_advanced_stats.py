#!/usr/bin/env python3
"""
Example demonstrating advanced statistical functionality
Shows how to use TOL-compatible statistical operations in Python
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tol_python import Serie, SerieStatistics, StatisticalTests
import numpy as np


def demonstrate_tol_style_analysis():
    """Demonstrate TOL-style time series analysis workflow"""
    print("=== TOL-Style Time Series Analysis ===\n")
    
    print("Simulating quarterly GDP data with trend and cycle...")
    
    # Create quarterly GDP-like data (trend + cycle + noise)
    np.random.seed(42)
    quarters = 40  # 10 years of quarterly data
    
    # Trend component (2% annual growth = 0.5% quarterly)
    trend = 1000 * (1.005 ** np.arange(quarters))
    
    # Business cycle component (5-year cycle)
    cycle = 50 * np.sin(2 * np.pi * np.arange(quarters) / 20)
    
    # Random shocks
    shocks = np.random.normal(0, 20, quarters)
    
    # Combine components
    gdp = trend + cycle + shocks
    
    # Create serie
    gdp_serie = Serie(data=gdp,
                     first_date="y2010m01d01",
                     last_date="y2019m10d01",
                     dating=TimeSet("quarterly"))
    
    print(f"GDP Series: {quarters} quarterly observations")
    print(f"Mean GDP: ${gdp_serie.mean():.0f}")
    print(f"GDP Growth (std dev): {gdp_serie.std():.1f}")
    
    return gdp_serie


def analyze_time_series_properties(serie):
    """Analyze key time series properties"""
    print("\n=== Time Series Properties Analysis ===\n")
    
    # Basic descriptive statistics
    print("Descriptive Statistics:")
    print(f"  Mean: {serie.mean():.2f}")
    print(f"  Median: {serie.median():.2f}")
    print(f"  Std Dev: {serie.std():.2f}")
    print(f"  Skewness: {serie.skewness():.3f}")
    print(f"  Kurtosis: {serie.kurtosis():.3f}")
    print(f"  IQR: {serie.iqr():.2f}")
    
    # Test for normality
    jb_test = serie.jarque_bera_test()
    print(f"\nNormality Test (Jarque-Bera):")
    print(f"  Statistic: {jb_test['statistic']:.3f}")
    print(f"  p-value: {jb_test['p_value']:.3f}")
    print(f"  Conclusion: {'Normal' if jb_test['p_value'] > 0.05 else 'Not Normal'}")
    
    # Autocorrelation analysis
    print(f"\nAutocorrelation Analysis:")
    acf = serie.autocorr(max_lags=8)
    print("  Lag  ACF")
    for i in range(1, 5):
        print(f"   {i}   {acf._data[i]:6.3f}")
    
    # Test for serial correlation
    ljb_test = serie.ljung_box_test(lags=8)
    print(f"\nSerial Correlation Test (Ljung-Box):")
    print(f"  Statistic: {ljb_test['statistic']:.3f}")
    print(f"  p-value: {ljb_test['p_value']:.3f}")
    print(f"  Conclusion: {'No serial correlation' if ljb_test['p_value'] > 0.05 else 'Serial correlation present'}")


def test_stationarity(serie):
    """Test for unit roots and stationarity"""
    print("\n=== Stationarity Testing ===\n")
    
    # ADF test on levels
    adf_levels = serie.adf_test(regression="ct")  # Include constant and trend
    print("ADF Test (Levels - with constant and trend):")
    print(f"  ADF Statistic: {adf_levels['statistic']:.3f}")
    print(f"  p-value: {adf_levels['p_value']:.3f}")
    print(f"  Critical Values: {adf_levels['critical_values']}")
    
    stationary_levels = adf_levels['statistic'] < adf_levels['critical_values']['5%']
    print(f"  Conclusion: {'Stationary' if stationary_levels else 'Non-stationary (has unit root)'}")
    
    # If non-stationary, test first differences
    if not stationary_levels:
        print("\nTesting first differences...")
        diff_serie = serie.diff()
        adf_diff = diff_serie.adf_test(regression="c")  # Only constant for differences
        
        print("ADF Test (First Differences):")
        print(f"  ADF Statistic: {adf_diff['statistic']:.3f}")
        print(f"  p-value: {adf_diff['p_value']:.3f}")
        print(f"  Critical Values: {adf_diff['critical_values']}")
        
        stationary_diff = adf_diff['statistic'] < adf_diff['critical_values']['5%']
        print(f"  Conclusion: {'Stationary after differencing' if stationary_diff else 'Still non-stationary'}")
        
        if stationary_diff:
            print(f"  → Series is integrated of order 1, I(1)")


def analyze_growth_rates(serie):
    """Analyze growth rates and cycles"""
    print("\n=== Growth Rate Analysis ===\n")
    
    # Calculate growth rates (quarterly % change)
    growth = (serie / serie.lag(1) - 1) * 100
    
    print("Growth Rate Statistics:")
    print(f"  Mean growth rate: {growth.mean():.2f}% per quarter")
    print(f"  Annualized growth: {growth.mean() * 4:.2f}% per year")
    print(f"  Growth volatility: {growth.std():.2f}%")
    print(f"  Min growth: {min(growth.to_dict().values()):.2f}%")
    print(f"  Max growth: {max(growth.to_dict().values()):.2f}%")
    
    # Test growth rates for serial correlation
    growth_ljb = StatisticalTests.box_pierce_ljung_test(growth, lags=4)
    print(f"\nGrowth Rate Serial Correlation:")
    print(f"  Ljung-Box p-value: {growth_ljb['p_value']:.3f}")
    print(f"  Conclusion: {'Independent' if growth_ljb['p_value'] > 0.05 else 'Serially correlated'}")
    
    return growth


def demonstrate_cross_correlation():
    """Demonstrate cross-correlation analysis between related series"""
    print("\n=== Cross-Correlation Analysis ===\n")
    
    print("Creating related economic indicators...")
    
    # Create related series (e.g., GDP and Employment)
    np.random.seed(42)
    quarters = 30
    
    # GDP (leading indicator)
    gdp_growth = np.random.normal(0.5, 2, quarters)  # Quarterly growth rates
    
    # Employment (lagging indicator - follows GDP with 1-2 quarter lag)
    employment_growth = np.zeros(quarters)
    for t in range(quarters):
        if t == 0:
            employment_growth[t] = np.random.normal(0.2, 1.5)
        elif t == 1:
            employment_growth[t] = 0.6 * gdp_growth[t-1] + np.random.normal(0.2, 1.5)
        else:
            employment_growth[t] = (0.6 * gdp_growth[t-1] + 0.3 * gdp_growth[t-2] + 
                                  np.random.normal(0.2, 1.5))
    
    gdp_serie = Serie(data=gdp_growth, first_date="y2015m01d01", last_date="y2022m04d01")
    emp_serie = Serie(data=employment_growth, first_date="y2015m01d01", last_date="y2022m04d01")
    
    # Cross-correlation analysis
    ccf = SerieStatistics.cross_correlation(gdp_serie, emp_serie, max_lags=6)
    
    print("Cross-Correlation: GDP Growth vs Employment Growth")
    print("Lag  CCF    Interpretation")
    for i, lag in enumerate(range(-4, 5)):
        ccf_val = ccf._data[i+2] if i+2 < len(ccf._data.to_numpy()) else 0
        if lag < 0:
            interp = "Employment leads GDP"
        elif lag == 0:
            interp = "Contemporaneous"  
        else:
            interp = "GDP leads Employment"
        print(f"{lag:2d}   {ccf_val:6.3f}  {interp}")
    
    # Find maximum correlation and its lag
    ccf_values = ccf._data.to_numpy()
    max_idx = np.argmax(np.abs(ccf_values))
    max_lag = max_idx - 6  # Adjust for lag range
    print(f"\nMaximum correlation at lag {max_lag}: {ccf_values[max_idx]:.3f}")
    
    # Contemporary correlation
    contemp_corr = SerieStatistics.correlation(gdp_serie, emp_serie)
    print(f"Contemporary correlation: {contemp_corr:.3f}")


def demonstrate_residual_analysis():
    """Demonstrate residual analysis after fitting a simple model"""
    print("\n=== Residual Analysis ===\n")
    
    # Create series with trend
    np.random.seed(42)
    t = np.arange(50)
    y = 100 + 2*t + np.random.normal(0, 5, 50)  # Linear trend + noise
    
    serie = Serie(data=y, first_date="y2020m01d01", last_date="y2024m02d19")
    
    # Fit simple linear trend (detrend)
    trend = 100 + 2*t
    residuals = y - trend
    
    resid_serie = Serie(data=residuals, first_date="y2020m01d01", last_date="y2024m02d19")
    
    print("Residual Diagnostics:")
    print(f"  Mean: {resid_serie.mean():.3f} (should be ~0)")
    print(f"  Std Dev: {resid_serie.std():.3f}")
    
    # Test residuals for serial correlation
    ljb_resid = resid_serie.ljung_box_test(lags=10)
    print(f"\nSerial Correlation Test:")
    print(f"  Ljung-Box p-value: {ljb_resid['p_value']:.3f}")
    print(f"  Conclusion: {'Good' if ljb_resid['p_value'] > 0.05 else 'Serial correlation in residuals'}")
    
    # Durbin-Watson test
    dw_stat = resid_serie.durbin_watson_test()
    print(f"\nDurbin-Watson Test:")
    print(f"  DW Statistic: {dw_stat:.3f}")
    print(f"  Conclusion: {'Good' if 1.5 < dw_stat < 2.5 else 'Possible autocorrelation'}")
    
    # Test for normality
    jb_resid = resid_serie.jarque_bera_test()
    print(f"\nNormality Test:")
    print(f"  Jarque-Bera p-value: {jb_resid['p_value']:.3f}")
    print(f"  Conclusion: {'Normal residuals' if jb_resid['p_value'] > 0.05 else 'Non-normal residuals'}")


if __name__ == "__main__":
    print("TOL Python - Advanced Statistics Example")
    print("=" * 50)
    
    # Load required modules
    from tol_python.core import TimeSet
    
    # Main analysis workflow
    gdp_data = demonstrate_tol_style_analysis()
    analyze_time_series_properties(gdp_data)
    test_stationarity(gdp_data)
    growth_data = analyze_growth_rates(gdp_data)
    demonstrate_cross_correlation()
    demonstrate_residual_analysis()
    
    print("\n" + "=" * 50)
    print("Advanced Statistics Example Complete!")
    print("\nThis example demonstrated:")
    print("- Descriptive statistics with missing value handling")
    print("- Autocorrelation and partial autocorrelation analysis")
    print("- Statistical tests (Ljung-Box, Jarque-Bera, ADF)")
    print("- Stationarity testing and unit root analysis")
    print("- Growth rate calculations and analysis")
    print("- Cross-correlation between related series")
    print("- Comprehensive residual diagnostics")
    print("\nAll functions match TOL's statistical capabilities!")