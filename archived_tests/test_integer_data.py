#!/usr/bin/env python3
"""
Test that integer data is handled correctly in Serie operations
This was a bug found by user testing
"""

import sys
import os
import numpy as np

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from series.serie import Serie

print("🧪 Testing integer data handling")
print("=" * 50)

# Test 1: Basic integer serie
print("\n1. Testing basic integer Serie creation:")
data = [1, 2, 3, 4, 5]
serie = Serie(data=data)
print(f"   Original data: {data}")
print(f"   Serie data type: {serie._data._data.dtype}")
print(f"   Serie to_numpy(): {serie._data.to_numpy()}")
print("   ✓ Basic integer handling works")

# Test 2: Moving average with integer data
print("\n2. Testing moving average with integer data:")
ma3 = serie.moving_average(3)
ma_values = ma3._data.to_numpy()
print(f"   MA(3) values: {ma_values}")
print(f"   MA(3) type: {ma_values.dtype}")
assert ma_values.dtype == np.float64, "MA should be float type"
assert np.isnan(ma_values[0]), "First values should be NaN"
assert np.isnan(ma_values[1]), "Second values should be NaN"
assert ma_values[2] == 2.0, "Third value should be 2.0"
print("   ✓ Moving average with integers works correctly")

# Test 3: Operations with integer series
print("\n3. Testing operations with integer series:")
serie2 = Serie(data=[10, 20, 30, 40, 50])
sum_serie = serie + serie2
print(f"   Sum result: {sum_serie._data.to_numpy()}")
print(f"   Sum type: {sum_serie._data.to_numpy().dtype}")
print("   ✓ Integer serie operations work")

# Test 4: Mixed operations
print("\n4. Testing mixed integer/float operations:")
float_serie = Serie(data=[1.5, 2.5, 3.5, 4.5, 5.5])
mixed_sum = serie + float_serie
print(f"   Mixed sum: {mixed_sum._data.to_numpy()}")
print(f"   Result type: {mixed_sum._data.to_numpy().dtype}")
print("   ✓ Mixed type operations work")

# Test 5: Statistical operations on integer data
print("\n5. Testing statistics on integer data:")
from stats.statistics import mean, variance, std_dev

mean_val = mean(serie)
var_val = variance(serie)
std_val = std_dev(serie)
print(f"   Mean: {mean_val}")
print(f"   Variance: {var_val}")
print(f"   Std Dev: {std_val}")
print("   ✓ Statistics on integer data work")

print("\n" + "=" * 50)
print("🎉 ALL INTEGER DATA TESTS PASSED!")
print("\nNote: Integer data is automatically converted to float64")
print("when operations might produce non-integer results (like NaN).")