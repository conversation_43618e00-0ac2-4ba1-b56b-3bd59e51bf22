"""
Demo: New LastDayOfMonth TimeSet
Shows the exact solution you wanted - a proper last day of month TimeSet
"""

from core import Date, TimeSet, LastDayOfMonthTimeSet
from series import Serie

def main():
    print("🎯 LastDayOfMonth TimeSet Demo")
    print("=" * 50)
    
    # Method 1: Direct instantiation
    print("Method 1: Direct instantiation")
    last_day = LastDayOfMonthTimeSet()
    print(f"last_day = LastDayOfMonthTimeSet()")
    print(f"TimeSet: {last_day}")
    
    # Method 2: Static method (cleaner)
    print(f"\nMethod 2: Static method (recommended)")
    month_end = TimeSet.last_day_of_month()
    print(f"month_end = TimeSet.last_day_of_month()")
    print(f"TimeSet: {month_end}")
    
    # Test precision vs the old approach
    print(f"\n📊 Precision Test: Last Day vs Day([28,29,30,31])")
    print("-" * 55)
    
    old_approach = TimeSet.day([28, 29, 30, 31])
    new_approach = TimeSet.last_day_of_month()
    
    test_dates = [
        (Date("y2023m01d31"), "Jan 31 (actual last day)"),
        (Date("y2023m01d30"), "Jan 30 (NOT last day)"),
        (Date("y2023m02d28"), "Feb 28 (actual last day)"),
        (Date("y2023m02d27"), "Feb 27 (NOT last day)"),
        (Date("y2023m06d30"), "Jun 30 (actual last day)"),
        (Date("y2023m06d29"), "Jun 29 (NOT last day)"),
        (Date("y2024m02d29"), "Feb 29, 2024 (leap year last day)"),
        (Date("y2024m02d28"), "Feb 28, 2024 (NOT last day, leap year)"),
    ]
    
    print(f"{'Date':<15} {'Description':<25} {'Old':<5} {'New':<5} {'Correct'}")
    print("-" * 65)
    
    for date, desc in test_dates:
        old_result = old_approach.includes(date)
        new_result = new_approach.includes(date)
        
        # Determine if it's actually the last day
        next_day = date.add_days(1)
        is_actually_last = next_day._value.day == 1
        
        old_icon = "✅" if old_result else "❌"
        new_icon = "✅" if new_result else "❌" 
        correct_icon = "✅" if new_result == is_actually_last else "❌"
        
        print(f"{str(date):<15} {desc:<25} {old_icon:<5} {new_icon:<5} {correct_icon}")
    
    # Navigation demo
    print(f"\n🧭 Navigation Demo")
    print("-" * 25)
    
    last_day_ts = TimeSet.last_day_of_month()
    current = Date("y2023m06d15")  # Mid-June
    
    print(f"Starting from: {current}")
    
    # Find next last days
    print(f"\nNext 5 last days of month:")
    for i in range(5):
        next_last = last_day_ts.successor(current)
        month_name = next_last._value.strftime('%B %Y')
        print(f"  {i+1}. {next_last} (last day of {month_name})")
        current = next_last
    
    # Find previous last day
    current = Date("y2023m07d15")  # Mid-July
    prev_last = last_day_ts.predecessor(current)
    prev_month = prev_last._value.strftime('%B %Y')
    print(f"\nPrevious last day from {current}: {prev_last} (last day of {prev_month})")
    
    # Business scenario
    print(f"\n💼 Business Scenario: Month-end Reports")
    print("-" * 45)
    
    # Month-end business days only
    business_days = TimeSet.wd([0, 1, 2, 3, 4])  # Monday-Friday
    month_end_business = last_day_ts * business_days
    
    print("Month-end business days (next 6):")
    current = Date("y2023m06d01")
    
    for i in range(6):
        next_eom_biz = month_end_business.successor(current)
        if next_eom_biz.is_normal():
            day_name = next_eom_biz._value.strftime('%A, %B %d, %Y')
            print(f"  {i+1}. {next_eom_biz} ({day_name})")
            current = next_eom_biz
        else:
            break
    
    # Create indicator series
    print(f"\n📈 Month-end Indicator Series")
    print("-" * 35)
    
    start = Date("y2023m05d25")
    end = Date("y2023m07d05")
    daily = TimeSet("daily")
    
    eom_indicator = Serie.cal_ind(last_day_ts, daily, start, end)
    
    print("Month-end indicators around June/July:")
    current = start
    while current <= end:
        value = eom_indicator[current]
        if value == 1.0:
            month_name = current._value.strftime('%B %d, %Y')
            print(f"  📅 {current} ({month_name}) - LAST DAY OF MONTH")
        current = daily.successor(current)

if __name__ == "__main__":
    main()
    
    print(f"\n" + "=" * 50)
    print("✅ Perfect! Now you have exactly what you wanted:")
    print("   month_end = TimeSet.last_day_of_month()")
    print("   - Precise: Only actual last days of month")
    print("   - Smart: Handles leap years automatically") 
    print("   - Navigable: Full successor/predecessor support")
    print("   - Composable: Works with * and + operations")