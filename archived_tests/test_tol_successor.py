"""
Test TOL-compatible Successor function
Demonstrates the TimeSet displacement functionality matching TOL's Successor
"""

from core import Date, TimeSet

def test_basic_successor():
    """Test basic Successor functionality"""
    print("Testing TOL Successor Function")
    print("=" * 40)
    
    # Test with month beginnings
    month_begin = TimeSet.day(1)  # First day of every month
    
    # Move forward 1 day - should give us 2nd of every month
    day_2 = TimeSet.successor_tol(month_begin, 1)
    
    print("Base TimeSet: First day of month")
    print("Successor(month_begin, +1): Second day of month")
    
    test_dates = [
        (Date("y2023m06d01"), "June 1st", False, True),   # 1st -> not in result, but successor should be
        (Date("y2023m06d02"), "June 2nd", True, False),  # 2nd -> in result  
        (Date("y2023m06d03"), "June 3rd", False, False), # 3rd -> not in result
        (Date("y2023m07d01"), "July 1st", False, True),
        (Date("y2023m07d02"), "July 2nd", True, False),
    ]
    
    print("\nTesting dates:")
    for date, desc, expected_in_successor, expected_in_original in test_dates:
        in_original = month_begin.includes(date)
        in_successor = day_2.includes(date)
        
        orig_status = "✓" if in_original == expected_in_original else "✗"
        succ_status = "✓" if in_successor == expected_in_successor else "✗"
        
        print(f"  {date} ({desc}): Original={in_original}{orig_status} Successor={in_successor}{succ_status}")

def test_negative_displacement():
    """Test negative displacement (moving backward)"""
    print("\nTesting Negative Displacement")
    print("-" * 30)
    
    # Start with 5th day of month, move back 2 days -> 3rd day
    day_5 = TimeSet.day(5)
    day_3 = TimeSet.successor_tol(day_5, -2)
    
    print("Base TimeSet: 5th day of month")  
    print("Successor(day_5, -2): 3rd day of month")
    
    test_dates = [
        Date("y2023m06d03"),  # Should be in result
        Date("y2023m06d05"),  # Should not be in result (original)
        Date("y2023m07d03"),  # Should be in result  
        Date("y2023m07d04"),  # Should not be in result
    ]
    
    print("\nTesting dates:")
    for date in test_dates:
        in_original = day_5.includes(date) 
        in_successor = day_3.includes(date)
        day_num = date._value.day
        expected = (day_num == 3)
        status = "✓" if in_successor == expected else "✗"
        
        print(f"  {date}: Original={in_original} Successor={in_successor}{status}")

def test_weekly_displacement():
    """Test displacement with weekly units"""
    print("\nTesting Weekly Displacement")
    print("-" * 25)
    
    # Mondays, moved forward 1 week = next Monday
    mondays = TimeSet.wd(0)  # 0 = Monday
    weekly = TimeSet("weekly")
    
    next_week_mondays = TimeSet.successor_tol(mondays, 1, weekly)
    
    print("Base TimeSet: Mondays")
    print("Successor(mondays, +1, weekly): Mondays shifted 1 week forward")
    
    # June 5, 2023 is a Monday
    original_monday = Date("y2023m06d05")
    next_monday = Date("y2023m06d12")  # One week later
    
    print(f"\nOriginal Monday: {original_monday}")
    print(f"Expected next Monday: {next_monday}")
    
    print(f"Original Monday in base set: {mondays.includes(original_monday)}")
    print(f"Original Monday in successor: {next_week_mondays.includes(original_monday)}")
    print(f"Next Monday in successor: {next_week_mondays.includes(next_monday)}")

def test_zero_displacement():
    """Test zero displacement (should return intersection with units)"""
    print("\nTesting Zero Displacement")
    print("-" * 23)
    
    # Business days (Mon-Fri) with zero displacement using daily units
    business_days = TimeSet.wd([0, 1, 2, 3, 4])
    daily = TimeSet("daily")
    
    # Zero displacement should return intersection of business_days * daily
    unchanged = TimeSet.successor_tol(business_days, 0, daily)
    
    print("Base TimeSet: Business days (Mon-Fri)")
    print("Successor(business_days, 0, daily): Should be same as business_days * daily")
    
    # Test a few dates
    test_dates = [
        Date("y2023m06d05"),  # Monday
        Date("y2023m06d10"),  # Saturday
        Date("y2023m06d12"),  # Monday
    ]
    
    print("\nTesting dates:")
    for date in test_dates:
        in_original = business_days.includes(date)
        in_successor = unchanged.includes(date)
        day_name = date._value.strftime('%A')
        
        print(f"  {date} ({day_name}): Original={in_original} Successor={in_successor}")

def test_month_end_scenario():
    """Test practical scenario with month-end processing"""
    print("\nPractical Scenario: Month-end Report Dates") 
    print("-" * 45)
    
    # Last business day of each month
    last_days = TimeSet.last_day_of_month()
    business_days = TimeSet.wd([0, 1, 2, 3, 4])
    
    # If last day is weekend, move back to find last business day
    last_business_day = TimeSet.successor_tol(last_days * business_days, 0)
    
    print("Creating TimeSet for last business day of each month")
    
    # Test some specific month-end dates
    test_dates = [
        Date("y2023m06d30"),  # Friday - last day and business day
        Date("y2023m07d31"),  # Monday - last day and business day  
        Date("y2023m09d30"),  # Saturday - last day but not business day
        Date("y2023m09d29"),  # Friday - business day before weekend month-end
    ]
    
    print("\nTesting month-end dates:")
    for date in test_dates:
        is_last_day = last_days.includes(date)
        is_business = business_days.includes(date)
        is_target = last_business_day.includes(date)
        day_name = date._value.strftime('%A, %B %d')
        
        print(f"  {date} ({day_name}): LastDay={is_last_day} Business={is_business} Target={is_target}")

def demo_tol_syntax():
    """Demonstrate TOL-like syntax"""
    print("\nTOL Syntax Demonstration")
    print("-" * 25)
    
    print("TOL equivalent usage:")
    print("  TimeSet.successor_tol(center, displacement, units)")
    print("  TimeSet.succ(center, displacement, units)  # shorter alias")
    print()
    
    # Example: Move first-of-month forward by 1 day
    first_of_month = TimeSet.day(1)
    second_of_month = TimeSet.succ(first_of_month, 1)
    
    print("Examples:")
    print(f"  first_of_month = TimeSet.day(1)")
    print(f"  second_of_month = TimeSet.succ(first_of_month, 1)")
    print(f"  Result: {second_of_month}")
    
    # Test it
    test_date = Date("y2023m06d02")
    print(f"\n  Test: {test_date} in second_of_month = {second_of_month.includes(test_date)}")

if __name__ == "__main__":
    print("TOL-Compatible Successor Function Test")
    print("=" * 50)
    
    test_basic_successor()
    test_negative_displacement()
    test_weekly_displacement()
    test_zero_displacement()
    test_month_end_scenario() 
    demo_tol_syntax()
    
    print("\n" + "=" * 50)
    print("TOL Successor Function Implementation Complete")
    print("\nKey features:")
    print("- TimeSet.successor_tol(center, displacement, units)")
    print("- TimeSet.succ() as shorter alias")
    print("- Positive displacement = move toward future")
    print("- Negative displacement = move toward past") 
    print("- Zero displacement = intersection with units")
    print("- Compatible with TOL's Successor function behavior")