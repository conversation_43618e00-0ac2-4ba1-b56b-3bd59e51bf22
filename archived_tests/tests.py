"""
Statistical tests module for TOL Python
This provides the 'tests' namespace used in the user guide
"""

# Import all test functions
from stats.test_functions import (
    jarque_bera_test,
    ljung_box_test,
    augmented_dickey_fuller_test,
    durbin_watson_test,
    runs_test
)

# Also expose the full test results (returning dictionaries)
from stats.tests import StatisticalTests

__all__ = [
    'jarque_bera_test',
    'ljung_box_test', 
    'augmented_dickey_fuller_test',
    'durbin_watson_test',
    'runs_test',
    'StatisticalTests'
]