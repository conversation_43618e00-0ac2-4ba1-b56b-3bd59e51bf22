"""
Test ARIMA diagnostics after sentinel fix
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from tol_python.series import Serie
from tol_python.arima import ARIMAFactor, ARIMA

# Generate ARMA(1,1) data
np.random.seed(42)
n = 100
phi = 0.7
theta = 0.3

y = np.zeros(n)
errors = np.random.normal(0, 1, n)

for t in range(1, n):
    y[t] = phi * y[t-1] + errors[t] + theta * errors[t-1]

# Create Serie without dates (uses sentinels)
serie = Serie(data=y)
print(f"Serie created with {len(serie)} observations")
print(f"Is stochastic: {serie.is_stochastic()}")

# Fit ARMA model
factor = ARIMAFactor(ar_order=1, ma_order=1, include_mean=False)
model = ARIMA(factor)
results = model.fit(serie, method="css")

print(f"Model fitted: AR={results.ar_params[0]:.3f}, MA={results.ma_params[0]:.3f}")

# Test diagnostics
try:
    diagnostics = results.diagnostics()
    
    print("\nDiagnostics computed successfully!")
    
    # Show key results
    ljung = diagnostics['ljung_box_10']
    print(f"Ljung-Box (10 lags): χ² = {ljung['statistic']:.2f}, p = {ljung['p_value']:.4f}")
    
    norm = diagnostics['normality']['jarque_bera']
    print(f"Jarque-Bera: JB = {norm['statistic']:.2f}, p = {norm['p_value']:.4f}")
    
    adequacy = diagnostics['model_adequate']['overall']
    print(f"Model adequate: {'Yes' if adequacy else 'No'}")
    
    print("\n✓ ARIMA diagnostics working!")
    
except Exception as e:
    print(f"Diagnostics failed: {e}")
    import traceback
    traceback.print_exc()