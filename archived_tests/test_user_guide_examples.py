#!/usr/bin/env python3
"""
Test ALL examples from TOL_PYTHON_USER_GUIDE.md
This ensures all documented functionality works correctly
"""

import sys
import os
import numpy as np

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

print("🧪 Testing ALL examples from TOL_PYTHON_USER_GUIDE.md")
print("=" * 70)

# Test counter
test_count = 0
passed_count = 0

def test_example(name, func):
    """Run a test and track results"""
    global test_count, passed_count
    test_count += 1
    print(f"\n{test_count}. Testing: {name}")
    try:
        func()
        passed_count += 1
        print("   ✓ PASSED")
        return True
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

# ============= 1. Core Components =============

def test_date_creation():
    """Test Date creation examples from guide"""
    from core.dates import Date
    
    # Create dates from string
    date1 = Date("y2023m06d15")
    date2 = Date("y2023m12d31")
    
    # Sentinel dates
    begin_date = Date.begin()
    end_date = Date.end()
    
    # Date arithmetic
    next_day = date1.successor()
    prev_day = date1.predecessor()
    
    assert str(date1) == "y2023m06d15"
    assert str(next_day) == "y2023m06d16"
    assert str(prev_day) == "y2023m06d14"

def test_date_range():
    """Test Date.range() from guide"""
    from core.dates import Date, TimeSet
    
    date1 = Date("y2023m06d15")
    date2 = Date("y2023m06d20")
    date_range = Date.range(date1, date2, TimeSet("daily"))
    
    assert len(date_range) == 6
    assert str(date_range[0]) == "y2023m06d15"
    assert str(date_range[-1]) == "y2023m06d20"

def test_timeset_creation():
    """Test TimeSet examples from guide"""
    from core.dates import TimeSet, Date
    
    # Different frequencies
    daily = TimeSet("daily")
    weekly = TimeSet("weekly") 
    monthly = TimeSet("monthly")
    
    # Using TimeSet with dates
    start_date = Date("y2023m01d01")
    next_month = monthly.successor(start_date)
    
    assert str(next_month) == "y2023m02d01"

# ============= 2. Time Series (Serie) =============

def test_serie_creation():
    """Test Serie creation from guide"""
    from series.serie import Serie
    from core.dates import Date, TimeSet
    
    # Create from list
    data = [100.5, 102.3, 98.7, 105.2, 103.8]
    serie = Serie(data=data)
    
    # With explicit dates
    serie_dated = Serie(
        data=data,
        first_date=Date("y2023m01d01"),
        last_date=Date("y2023m01d05"),
        dating=TimeSet("daily")
    )
    
    assert len(serie) == 5
    assert len(serie_dated) == 5
    assert serie_dated[0] == 100.5

def test_serie_operations():
    """Test Serie operations from guide"""
    from series.serie import Serie
    from series.operations import SerieOperations
    
    data1 = [100, 102, 104, 103, 105]
    data2 = [1.0, 1.5, 2.0, 1.5, 2.0]
    
    serie1 = Serie(data=data1)
    serie2 = Serie(data=data2)
    
    # Arithmetic operations
    sum_serie = serie1 + serie2
    diff_serie = serie1 - serie2
    mult_serie = serie1 * 2
    
    # Statistical operations
    mean_val = serie1.mean()
    
    assert sum_serie[0] == 101.0
    assert diff_serie[0] == 99.0
    assert mult_serie[0] == 200
    assert mean_val == 102.8

def test_serie_lag_diff():
    """Test Serie lag and diff operations from guide"""
    from series.serie import Serie
    
    data = [100, 102, 104, 103, 105]
    serie = Serie(data=data)
    
    # Lag operation
    lagged = serie.lag(1)
    
    # Difference operation
    diff = serie.diff(1)
    
    # Moving average
    ma = serie.moving_average(3)
    
    assert len(lagged) == 5
    assert len(diff) == 4  # Diff reduces length by 1
    assert len(ma) == 5

# ============= 3. Statistical Functions =============

def test_basic_statistics():
    """Test basic statistics from guide"""
    from series.serie import Serie
    from stats.statistics import mean, variance, std_dev
    
    data = [10.5, 12.3, 11.8, 13.2, 12.7, 11.5]
    serie = Serie(data=data)
    
    # Calculate statistics
    mean_val = mean(serie)
    var_val = variance(serie)
    std_val = std_dev(serie)
    
    assert abs(mean_val - 12.0) < 0.1
    assert var_val > 0
    assert std_val > 0

def test_advanced_statistics():
    """Test advanced statistics from guide"""
    from series.serie import Serie
    from stats.statistics import SerieStatistics
    
    data = np.random.randn(100).tolist()
    serie = Serie(data=data)
    
    # Autocorrelation
    acf = SerieStatistics.autocorrelation(serie, max_lags=10)
    
    # Skewness and kurtosis
    skew = SerieStatistics.skewness(serie)
    kurt = SerieStatistics.kurtosis(serie)
    
    # Quantiles
    q25 = SerieStatistics.quantile(serie, 0.25)
    median = SerieStatistics.median(serie)
    q75 = SerieStatistics.quantile(serie, 0.75)
    
    assert len(acf) == 11  # 0 to 10 lags
    assert isinstance(skew, float)
    assert isinstance(kurt, float)
    assert q25 <= median <= q75

def test_statistical_tests():
    """Test statistical tests from guide"""
    from series.serie import Serie
    from stats.tests import StatisticalTests
    
    # Generate test data
    np.random.seed(42)
    data = np.random.randn(100).tolist()
    serie = Serie(data=data)
    
    # Normality test
    jb_result = StatisticalTests.jarque_bera_test(serie)
    
    # Randomness test
    runs_result = StatisticalTests.runs_test(serie)
    
    assert 'statistic' in jb_result
    assert 'p_value' in jb_result
    assert 'statistic' in runs_result
    assert 'p_value' in runs_result

# ============= 4. ARIMA Models =============

def test_arima_basic():
    """Test basic ARIMA from guide"""
    from series.serie import Serie
    from arima.arima_factor import ARIMAFactor
    from arima.arima_model import ARIMA
    
    # Generate sample data
    np.random.seed(42)
    data = np.cumsum(np.random.randn(100)).tolist()
    serie = Serie(data=data)
    
    # Create ARIMA(1,1,1) model
    factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1)
    model = ARIMA(factor)
    
    # Fit model
    results = model.fit(serie, method="css")
    
    assert results.converged
    assert len(results.ar_params) == 1
    assert len(results.ma_params) == 1

def test_seasonal_arima():
    """Test seasonal ARIMA from guide"""
    from series.serie import Serie
    from arima.arima_factor import ARIMAFactor
    from arima.arima_model import ARIMA
    
    # Generate seasonal data
    np.random.seed(42)
    t = np.arange(120)
    seasonal = 10 * np.sin(2 * np.pi * t / 12)
    noise = np.random.randn(120)
    data = (seasonal + noise).tolist()
    serie = Serie(data=data)
    
    # Create seasonal ARIMA
    factor = ARIMAFactor.from_order(
        order=(1, 0, 1),
        seasonal_order=(1, 0, 1, 12)
    )
    model = ARIMA(factor)
    
    assert factor.is_seasonal
    assert factor.season_length == 12

# ============= 5. Bayesian Models =============

def test_bayesian_arima():
    """Test Bayesian ARIMA from guide"""
    from series.serie import Serie
    from arima.arima_factor import ARIMAFactor
    from bayesian.arima import BayesianARIMA
    from bayesian.core import MCMCConfig
    from bayesian.priors import NormalPrior, InverseGammaPrior
    
    # Generate data
    np.random.seed(42)
    data = np.cumsum(np.random.randn(100)).tolist()
    serie = Serie(data=data)
    
    # Create model
    factor = ARIMAFactor(ar_order=1, diff_order=0, ma_order=1)
    
    # Configure MCMC
    config = MCMCConfig(
        mcmc_burnin=100,
        mcmc_sample_length=500,
        mcmc_cache_length=50
    )
    
    # Set priors
    priors = {
        'ar': NormalPrior(mean=0.5, variance=0.1, name="AR1"),
        'ma': NormalPrior(mean=0.0, variance=0.1, name="MA1"),
        'variance': InverseGammaPrior(shape=3.0, scale=1.0, name="sigma2")
    }
    
    # Create Bayesian ARIMA
    bayesian_model = BayesianARIMA(factor, priors=priors, config=config)
    
    assert bayesian_model.factor == factor
    assert bayesian_model.model.config == config

# ============= Run all tests =============

test_example("Date creation", test_date_creation)
test_example("Date.range()", test_date_range)
test_example("TimeSet creation", test_timeset_creation)
test_example("Serie creation", test_serie_creation)
test_example("Serie operations", test_serie_operations)
test_example("Serie lag/diff", test_serie_lag_diff)
test_example("Basic statistics", test_basic_statistics)
test_example("Advanced statistics", test_advanced_statistics)
test_example("Statistical tests", test_statistical_tests)
test_example("Basic ARIMA", test_arima_basic)
test_example("Seasonal ARIMA", test_seasonal_arima)
test_example("Bayesian ARIMA", test_bayesian_arima)

# Summary
print("\n" + "=" * 70)
print(f"SUMMARY: {passed_count}/{test_count} tests passed")
if passed_count == test_count:
    print("🎉 ALL USER GUIDE EXAMPLES WORK CORRECTLY!")
else:
    print(f"❌ {test_count - passed_count} tests failed - please fix these examples!")