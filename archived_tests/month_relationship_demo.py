"""
Demo: Relationship between first and last days of month
Shows the conceptual connection you were thinking about
"""

from core import Date, TimeSet

def main():
    print("🔗 Month Begin ↔ Month End Relationship")
    print("=" * 50)
    
    # Your original idea
    print("💡 Your Original Concept:")
    print("month_begin = TimeSet.day(1)")
    print("month_end = month_begin.predecessor  # This was the idea")
    print()
    
    # What actually works
    print("✅ What Actually Works:")
    
    # Method 1: Direct creation
    month_begin = TimeSet.day(1)
    month_end = TimeSet.last_day_of_month()
    
    print("month_begin = TimeSet.day(1)")
    print("month_end = TimeSet.last_day_of_month()")
    print()
    
    # Show the relationship works for specific dates
    print("🧮 The Mathematical Relationship:")
    print("For any first-of-month date, predecessor gives last day of previous month")
    print()
    
    first_days = [
        Date("y2023m02d01"),  # Feb 1
        Date("y2023m03d01"),  # Mar 1
        Date("y2023m07d01"),  # Jul 1
        Date("y2024m03d01"),  # Mar 1 (leap year)
    ]
    
    daily = TimeSet("daily")
    
    print(f"{'First of Month':<20} {'→ Predecessor':<20} {'Is Last Day?'}")
    print("-" * 60)
    
    for first_day in first_days:
        prev_day = daily.predecessor(first_day)
        is_last_day = month_end.includes(prev_day)
        status = "✅ YES" if is_last_day else "❌ NO"
        
        print(f"{str(first_day):<20} → {str(prev_day):<20} {status}")
    
    # Show TimeSet equivalence
    print(f"\n📊 TimeSet Behavior Comparison")
    print("-" * 40)
    
    test_dates = [
        Date("y2023m01d31"),  # Last day of January
        Date("y2023m02d01"),  # First day of February
        Date("y2023m02d28"),  # Last day of February
        Date("y2023m03d01"),  # First day of March
        Date("y2023m06d30"),  # Last day of June
        Date("y2023m07d01"),  # First day of July
    ]
    
    print(f"{'Date':<15} {'First of Month':<15} {'Last of Month':<15} {'Relationship'}")
    print("-" * 70)
    
    for date in test_dates:
        is_first = month_begin.includes(date)
        is_last = month_end.includes(date)
        
        first_icon = "🌅" if is_first else "  "
        last_icon = "🌇" if is_last else "  "
        
        if is_first:
            # Check if predecessor is last day
            prev = daily.predecessor(date)
            prev_is_last = month_end.includes(prev)
            relationship = f"prev → {prev} ({'✅ last day' if prev_is_last else '❌ not last'})"
        elif is_last:
            # Check if successor is first day  
            next_day = daily.successor(date)
            next_is_first = month_begin.includes(next_day)
            relationship = f"next → {next_day} ({'✅ first day' if next_is_first else '❌ not first'})"
        else:
            relationship = "neither first nor last"
        
        print(f"{str(date):<15} {first_icon:<15} {last_icon:<15} {relationship}")
    
    # Practical application
    print(f"\n💼 Practical Usage")
    print("-" * 20)
    
    print("Finding month boundaries:")
    current_date = Date("y2023m06d15")  # Mid-June
    
    # Find first day of current month
    current_first = month_begin.successor(daily.predecessor(current_date))
    # Find last day of current month  
    current_last = month_end.successor(daily.predecessor(current_date))
    
    print(f"Current date: {current_date}")
    print(f"First of this month: {current_first}")
    print(f"Last of this month: {current_last}")
    
    # Month navigation
    print(f"\nMonth navigation:")
    next_month_first = month_begin.successor(current_date)
    prev_month_last = month_end.predecessor(current_date)
    
    print(f"Next month starts: {next_month_first}")
    print(f"Previous month ended: {prev_month_last}")
    
    # Verify the relationship
    print(f"\n🔍 Verify Relationship:")
    relationship_check = daily.predecessor(next_month_first)
    print(f"day before {next_month_first} = {relationship_check}")
    print(f"Is {relationship_check} the last day of a month? {month_end.includes(relationship_check)}")

if __name__ == "__main__":
    main()
    
    print(f"\n" + "=" * 50)
    print("🎯 Summary:")
    print("• Your intuition was correct - first and last days are related!")
    print("• For specific dates: daily.predecessor(first_of_month) = last_of_previous_month")
    print("• For TimeSets: Use TimeSet.last_day_of_month() for precise last days")
    print("• The relationship: month_end ≡ {d | daily.successor(d) ∈ month_begin}")
    print()
    print("💡 Perfect usage:")
    print("   month_begin = TimeSet.day(1)")
    print("   month_end = TimeSet.last_day_of_month()")
    print("   # Now you have both with full precision!")