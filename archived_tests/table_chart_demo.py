"""
Simple demo of Serie table() and chart() methods
Perfect for showcasing visualization capabilities
"""

from core import Date, TimeSet
from series import Serie
import numpy as np

def main():
    print("📊 Serie Table and Chart Demo")
    print("=" * 50)
    
    # Create sample daily data
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Generate 14 days of mock temperature data
    np.random.seed(42)
    temperatures = []
    base_temp = 22  # 22°C base temperature
    
    for i in range(14):
        # Simulate daily temperature with some pattern
        seasonal = 3 * np.sin(i * 2 * np.pi / 7)  # Weekly pattern
        noise = np.random.normal(0, 2)  # Random variation
        temp = base_temp + seasonal + noise
        temperatures.append(round(temp, 1))
    
    # Add a couple missing values
    temperatures[3] = None  # Missing data on day 4
    temperatures[9] = None  # Missing data on day 10
    
    temperature_serie = Serie(data=temperatures, first_date=start, dating=daily)
    
    print("🌡️  14-Day Temperature Data")
    print("-" * 30)
    print(temperature_serie.table())
    
    print("\n📈 Statistics:")
    print(f"  Average Temperature: {temperature_serie.mean():.1f}°C")
    print(f"  Missing Values: {temperature_serie.count_missing()}")
    print(f"  Valid Readings: {temperature_serie.count_valid()}")
    
    # Show first 7 days in GitHub table format
    print(f"\n📋 First Week (GitHub format):")
    try:
        print(temperature_serie.table(max_rows=7, format="github"))
    except:
        print(temperature_serie.table(max_rows=7))
    
    # Try to create a chart
    print(f"\n📊 Temperature Chart:")
    try:
        temperature_serie.chart(
            title="14-Day Temperature Trend", 
            style='line',
            figsize=(12, 6),
            show=False,  # Set to True to display chart
            save_path="temperature_chart.png"
        )
        print("  ✅ Chart created and saved as 'temperature_chart.png'")
        print("  💡 Set show=True in the code to display the chart")
    except ImportError:
        print("  ⚠️  Chart requires matplotlib. Install with:")
        print("     pip install matplotlib")
    except Exception as e:
        print(f"  ❌ Chart error: {e}")
    
    # Weekly aggregation example
    print(f"\n🗓️  Weekly Analysis:")
    try:
        weekly = TimeSet("weekly")
        weekly_avg = temperature_serie.dat_ch(weekly)
        
        print("Weekly average temperatures:")
        print(weekly_avg.table(format="simple"))
        
        print(f"\nWeekly comparison:")
        dates = weekly_avg._get_all_dates()
        if len(dates) >= 2:
            week1_avg = weekly_avg[dates[0]]
            week2_avg = weekly_avg[dates[1]]
            change = week2_avg - week1_avg
            print(f"  Week 1: {week1_avg:.1f}°C")
            print(f"  Week 2: {week2_avg:.1f}°C")
            print(f"  Change: {change:+.1f}°C")
        
    except Exception as e:
        print(f"  Could not calculate weekly averages: {e}")
    
    print(f"\n✨ Demo Features Showcased:")
    print("  ✅ Table display with missing values")
    print("  ✅ Different table formats")
    print("  ✅ Basic statistics")
    print("  ✅ Chart generation (if matplotlib available)")
    print("  ✅ Weekly aggregation with DatCh")
    
    print(f"\n💡 Next Steps:")
    print("  • Install matplotlib for charts: pip install matplotlib")
    print("  • Install tabulate for fancy tables: pip install tabulate")
    print("  • Try different chart styles: 'line', 'bar', 'area', 'scatter'")
    print("  • Combine with CalInd for business day analysis")

if __name__ == "__main__":
    main()