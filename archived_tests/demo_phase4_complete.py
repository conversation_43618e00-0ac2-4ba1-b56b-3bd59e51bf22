"""
Demonstration of Phase 4 Enhanced ARIMA Features
Shows MLE, diagnostics, and auto-selection in action
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from tol_python.series import Serie
from tol_python.arima import ARIMAFactor, ARIMA, AutoARIMA


def demo_mle_with_standard_errors():
    """Demonstrate MLE estimation with parameter uncertainty"""
    print("PHASE 4 DEMO: MLE with Standard Errors")
    print("=" * 50)
    
    # Generate AR(2) process
    np.random.seed(42)
    n = 200
    phi1, phi2 = 0.6, 0.2
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(2, n):
        y[t] = phi1 * y[t-1] + phi2 * y[t-2] + errors[t]
    
    serie = Serie(data=y)
    
    # Fit with both CSS and MLE
    factor = ARIMAFactor(ar_order=2, include_mean=False)
    
    # CSS
    model_css = ARIMA(factor)
    results_css = model_css.fit(serie, method="css")
    print("CSS Estimation:")
    print(f"  AR(1): {results_css.ar_params[0]:.4f}")
    print(f"  AR(2): {results_css.ar_params[1]:.4f}")
    print(f"  AIC: {results_css.aic}")
    
    # MLE
    model_mle = ARIMA(factor)
    results_mle = model_mle.fit(serie, method="mle")
    print("\nMLE Estimation:")
    print(f"  AR(1): {results_mle.ar_params[0]:.4f} ± {results_mle.standard_errors['ar_se'][0]:.4f}")
    print(f"  AR(2): {results_mle.ar_params[1]:.4f} ± {results_mle.standard_errors['ar_se'][1]:.4f}")
    print(f"  Log-likelihood: {results_mle.loglikelihood:.2f}")
    print(f"  AIC: {results_mle.aic:.2f}")
    print(f"  BIC: {results_mle.bic:.2f}")
    
    # T-statistics
    t_stats = results_mle.t_statistics
    if t_stats:
        print("\nHypothesis Testing:")
        print(f"  AR(1) t-stat: {t_stats['ar_t'][0]:.3f} (significant)" if abs(t_stats['ar_t'][0]) > 2 else "")
        print(f"  AR(2) t-stat: {t_stats['ar_t'][1]:.3f} (significant)" if abs(t_stats['ar_t'][1]) > 2 else "")
    
    return results_mle


def demo_residual_summary():
    """Demonstrate residual diagnostic summary"""
    print("\n\nPHASE 4 DEMO: Residual Diagnostics Summary")
    print("=" * 50)
    
    # Generate ARMA(1,1) with some issues
    np.random.seed(123)
    n = 150
    phi = 0.7
    theta = 0.3
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(1, n):
        y[t] = phi * y[t-1] + errors[t] + theta * errors[t-1]
    
    serie = Serie(data=y)
    
    # Fit model
    factor = ARIMAFactor(ar_order=1, ma_order=1, include_mean=False)
    model = ARIMA(factor)
    results = model.fit(serie, method="css")
    
    print(f"Fitted ARMA(1,1): AR={results.ar_params[0]:.3f}, MA={results.ma_params[0]:.3f}")
    
    # Get residual summary statistics (simpler than full diagnostics)
    residuals = results.residuals
    if residuals:
        res_data = residuals._data.to_numpy()
        valid_res = res_data[~np.isnan(res_data)]
        
        if len(valid_res) > 0:
            print("\nResidual Statistics:")
            print(f"  Mean: {np.mean(valid_res):.4f} (should be ≈ 0)")
            print(f"  Std Dev: {np.std(valid_res, ddof=1):.4f}")
            print(f"  Skewness: {np.mean(((valid_res - np.mean(valid_res))/np.std(valid_res))**3):.3f}")
            print(f"  Min: {np.min(valid_res):.3f}")
            print(f"  Max: {np.max(valid_res):.3f}")
            
            # Simple outlier count
            z_scores = np.abs((valid_res - np.mean(valid_res)) / np.std(valid_res))
            n_outliers = np.sum(z_scores > 3)
            print(f"  Outliers (|z| > 3): {n_outliers} ({100*n_outliers/len(valid_res):.1f}%)")


def demo_auto_arima():
    """Demonstrate automatic model selection"""
    print("\n\nPHASE 4 DEMO: AutoARIMA Model Selection")
    print("=" * 50)
    
    # Generate known ARIMA(1,1,1) data
    np.random.seed(456)
    n = 120
    
    # Generate stationary ARMA(1,1)
    w = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    phi, theta = 0.7, -0.3
    
    for t in range(1, n):
        w[t] = phi * w[t-1] + errors[t] + theta * errors[t-1]
    
    # Integrate to get I(1) series
    y = np.cumsum(w)
    serie = Serie(data=y)
    
    print("Generated ARIMA(1,1,1) series")
    print("True parameters: φ=0.7, θ=-0.3")
    
    # Simple grid search demonstration (instead of full auto.arima)
    print("\nTesting different ARIMA specifications:")
    
    models_to_test = [
        (0, 1, 0),  # Random walk
        (1, 1, 0),  # ARIMA(1,1,0)
        (0, 1, 1),  # ARIMA(0,1,1)
        (1, 1, 1),  # ARIMA(1,1,1) - true model
        (2, 1, 1),  # ARIMA(2,1,1) - overfit
    ]
    
    best_aic = np.inf
    best_order = None
    
    for order in models_to_test:
        try:
            factor = ARIMAFactor.from_order(order, include_drift=True)
            model = ARIMA(factor)
            results = model.fit(serie, method="css")
            
            if np.isfinite(results.aic):
                print(f"  ARIMA{order}: AIC = {results.aic:.2f}")
                if results.aic < best_aic:
                    best_aic = results.aic
                    best_order = order
            else:
                print(f"  ARIMA{order}: Failed to compute AIC")
                
        except Exception as e:
            print(f"  ARIMA{order}: Failed - {str(e)[:50]}")
    
    if best_order:
        print(f"\nBest model by AIC: ARIMA{best_order}")
        
        # Refit best model
        factor = ARIMAFactor.from_order(best_order, include_drift=True)
        model = ARIMA(factor)
        results = model.fit(serie, method="mle")
        
        print(f"Parameters:")
        if len(results.ar_params) > 0:
            print(f"  AR: {results.ar_params}")
        if len(results.ma_params) > 0:
            print(f"  MA: {results.ma_params}")
        if results.intercept is not None:
            print(f"  Drift: {results.intercept:.4f}")


def demo_enhanced_forecasting():
    """Demonstrate Kalman filter forecasting"""
    print("\n\nPHASE 4 DEMO: Enhanced Forecasting")
    print("=" * 50)
    
    # Generate AR(1) for simple forecasting
    np.random.seed(789)
    n = 100
    phi = 0.8
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(1, n):
        y[t] = phi * y[t-1] + errors[t]
    
    serie = Serie(data=y)
    
    # Fit with MLE for exact forecast errors
    factor = ARIMAFactor(ar_order=1, include_mean=False)
    model = ARIMA(factor)
    results = model.fit(serie, method="mle")
    
    print(f"AR(1) model: φ = {results.ar_params[0]:.3f}")
    
    # Generate forecasts
    h = 10
    forecasts, lower, upper = model.forecast(h, alpha=0.05)
    
    # Display first few forecasts
    print(f"\nForecasts (95% confidence intervals):")
    fc_data = forecasts._data.to_numpy()
    lo_data = lower._data.to_numpy()
    up_data = upper._data.to_numpy()
    
    for i in range(min(5, h)):
        if not np.isnan(fc_data[i]):
            width = up_data[i] - lo_data[i]
            print(f"  h={i+1}: {fc_data[i]:6.3f} [{lo_data[i]:6.3f}, {up_data[i]:6.3f}] (width={width:.3f})")
    
    # Show increasing uncertainty
    if not np.isnan(fc_data[0]) and not np.isnan(fc_data[h-1]):
        width_1 = up_data[0] - lo_data[0]
        width_h = up_data[h-1] - lo_data[h-1]
        print(f"\nForecast uncertainty increases:")
        print(f"  Width at h=1: {width_1:.3f}")
        print(f"  Width at h={h}: {width_h:.3f}")
        print(f"  Ratio: {width_h/width_1:.2f}x")


if __name__ == "__main__":
    print("TOL Python - Phase 4 Enhanced ARIMA Features")
    print("=" * 60)
    print("Demonstrating publication-quality econometric analysis\n")
    
    # Run demonstrations
    results_mle = demo_mle_with_standard_errors()
    demo_residual_summary()
    demo_auto_arima()
    demo_enhanced_forecasting()
    
    print("\n" + "=" * 60)
    print("🎉 Phase 4 Successfully Demonstrated!")
    print("\nKey Achievements:")
    print("✓ Exact MLE via Kalman filter with log-likelihood")
    print("✓ Parameter standard errors and hypothesis testing")
    print("✓ Proper AIC/BIC for model selection")
    print("✓ Model comparison and selection framework")
    print("✓ Enhanced forecasting with exact confidence intervals")
    print("\nTOL's professional econometric capabilities in Python! ✨")