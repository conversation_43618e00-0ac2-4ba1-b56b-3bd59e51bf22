"""
Test and demonstrate Serie table() and chart() methods
Shows how to visualize time series data in tabular and graphical formats
"""

import numpy as np
from core import Date, TimeSet
from series import Serie
import os

def test_table_method():
    """Test the table() method with different formats"""
    print("📋 Testing Serie.table() Method")
    print("=" * 50)
    
    # Create sample data
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Generate 10 days of sample data
    data = [10.5, 12.3, 11.8, 9.7, 13.2, 14.1, 10.9, 11.5, 12.8, 13.6]
    serie = Serie(data=data, first_date=start, dating=daily)
    
    print("Sample Serie with 10 days of data:")
    print(serie.table())
    print()
    
    print("Same data with different formats:")
    
    # Test different table formats
    formats = ["grid", "plain", "simple", "github", "pipe"]
    for fmt in formats:
        print(f"\n--- Format: {fmt} ---")
        try:
            print(serie.table(max_rows=5, format=fmt))
        except ImportError:
            print("(tabulate not available - using simple format)")
            print(serie.table(max_rows=5))
            break
    
    print()

def test_table_with_missing():
    """Test table with missing values"""
    print("🕳️  Testing table() with missing values")
    print("=" * 50)
    
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Create data with some missing values
    data = [10.5, None, 11.8, 9.7, None, 14.1, 10.9]
    serie = Serie(data=data, first_date=start, dating=daily)
    
    print("Serie with missing values:")
    print(serie.table())
    print()

def test_chart_method():
    """Test the chart() method with different styles"""
    print("📊 Testing Serie.chart() Method")
    print("=" * 50)
    
    # Create sample data for charting
    start = Date("y2023m06d01")
    daily = TimeSet("daily")
    
    # Generate 30 days of sample time series data
    np.random.seed(42)
    base_value = 100
    data = []
    
    for i in range(30):
        # Simulate a trending time series with noise
        trend = i * 0.5  # Upward trend
        noise = np.random.normal(0, 3)  # Random noise
        seasonal = 5 * np.sin(i * 2 * np.pi / 7)  # Weekly pattern
        value = base_value + trend + seasonal + noise
        data.append(value)
    
    serie = Serie(data=data, first_date=start, dating=daily)
    
    print(f"Created sample time series with {len(data)} points")
    print("Testing different chart styles...")
    
    # Test different chart styles without showing (for automated testing)
    styles = ['line', 'bar', 'scatter', 'area']
    
    for style in styles:
        try:
            print(f"  ✓ Testing {style} chart...")
            fig, ax = serie.chart(
                title=f"Sample Data - {style.title()} Chart",
                style=style,
                show=False  # Don't display during testing
            )
            
            # Save chart for inspection
            save_path = f"/tmp/test_chart_{style}.png"
            if os.path.exists("/tmp"):
                serie.chart(
                    title=f"Sample Data - {style.title()} Chart",
                    style=style,
                    save_path=save_path,
                    show=False
                )
                print(f"    Saved to: {save_path}")
            
        except ImportError as e:
            print(f"    ⚠️  Skipping chart test: {e}")
            break
        except Exception as e:
            print(f"    ❌ Error with {style} chart: {e}")
    
    print()

def demo_combined_visualization():
    """Demonstrate table and chart together for analysis"""
    print("🔍 Combined Table + Chart Analysis Demo")
    print("=" * 50)
    
    # Create weekly sales data
    start = Date("y2023m06d01")
    weekly = TimeSet("weekly")
    
    # Sample weekly sales data
    sales_data = [45000, 52000, 48000, 55000, 61000, 58000, 63000, 59000]
    serie = Serie(data=sales_data, first_date=start, dating=weekly)
    
    print("Weekly Sales Analysis")
    print("-" * 30)
    
    # Show data in table format
    print("\n📋 Sales Data Table:")
    print(serie.table(format="github"))
    
    # Calculate some basic statistics
    print(f"\n📈 Basic Statistics:")
    print(f"  Total Sales: ${serie.sum():,.2f}")
    print(f"  Average Weekly Sales: ${serie.mean():,.2f}")
    print(f"  Min Sales: ${min([serie[d] for d in serie._get_all_dates()]):,.2f}")
    print(f"  Max Sales: ${max([serie[d] for d in serie._get_all_dates()]):,.2f}")
    
    # Try to create chart
    print(f"\n📊 Sales Chart:")
    try:
        serie.chart(
            title="Weekly Sales Performance",
            style='line',
            figsize=(12, 6),
            show=False,  # Don't show during demo
            save_path="/tmp/weekly_sales.png" if os.path.exists("/tmp") else None
        )
        print("  ✓ Chart created successfully")
        if os.path.exists("/tmp/weekly_sales.png"):
            print("  ✓ Chart saved to /tmp/weekly_sales.png")
    except ImportError:
        print("  ⚠️  matplotlib not available - chart functionality requires: pip install matplotlib")
    except Exception as e:
        print(f"  ❌ Chart error: {e}")
    
    print()

def demo_business_scenario():
    """Real-world business scenario using table and chart"""
    print("💼 Business Scenario: Monthly Revenue Analysis")
    print("=" * 60)
    
    # Create monthly revenue data
    start = Date("y2023m01d01")
    monthly = TimeSet("monthly")
    
    # 12 months of revenue data
    revenue_data = [
        850000, 920000, 780000, 950000, 1100000, 1250000,  # Q1-Q2
        1350000, 1280000, 1150000, 980000, 890000, 1450000  # Q3-Q4
    ]
    
    revenue_serie = Serie(data=revenue_data, first_date=start, dating=monthly)
    
    print("📊 2023 Monthly Revenue Report")
    print("-" * 40)
    
    # Display table with formatted output
    print("\n📋 Monthly Revenue Table:")
    table_output = revenue_serie.table(format="grid")
    
    # Add currency formatting to the table output
    lines = table_output.split('\n')
    formatted_lines = []
    for line in lines:
        if 'y2023m' in line and '|' in line:
            # This is a data row - format the value as currency
            parts = line.split('|')
            if len(parts) >= 3:
                try:
                    value = float(parts[2].strip())
                    parts[2] = f" ${value:,.0f} "
                    line = '|'.join(parts)
                except:
                    pass
        formatted_lines.append(line)
    
    print('\n'.join(formatted_lines))
    
    # Calculate quarterly totals using DatCh
    try:
        from series.operations import SerieOperations
        
        # Create quarter indicators and sum up
        q1_total = sum([revenue_serie[Date("y2023m01d01")], 
                       revenue_serie[Date("y2023m02d01")], 
                       revenue_serie[Date("y2023m03d01")]])
        q2_total = sum([revenue_serie[Date("y2023m04d01")], 
                       revenue_serie[Date("y2023m05d01")], 
                       revenue_serie[Date("y2023m06d01")]])
        q3_total = sum([revenue_serie[Date("y2023m07d01")], 
                       revenue_serie[Date("y2023m08d01")], 
                       revenue_serie[Date("y2023m09d01")]])
        q4_total = sum([revenue_serie[Date("y2023m10d01")], 
                       revenue_serie[Date("y2023m11d01")], 
                       revenue_serie[Date("y2023m12d01")]])
        
        print(f"\n📈 Quarterly Summary:")
        print(f"  Q1 2023: ${q1_total:,.0f}")
        print(f"  Q2 2023: ${q2_total:,.0f}")
        print(f"  Q3 2023: ${q3_total:,.0f}")
        print(f"  Q4 2023: ${q4_total:,.0f}")
        print(f"  Annual Total: ${revenue_serie.sum():,.0f}")
        
        # Growth analysis
        q4_growth = ((q4_total - q3_total) / q3_total) * 100
        print(f"  Q4 vs Q3 Growth: {q4_growth:+.1f}%")
        
    except Exception as e:
        print(f"Could not calculate quarterly summary: {e}")
    
    # Try to create chart
    print(f"\n📊 Revenue Visualization:")
    try:
        revenue_serie.chart(
            title="2023 Monthly Revenue Performance",
            style='area',
            figsize=(14, 8),
            show=False,
            save_path="/tmp/revenue_chart.png" if os.path.exists("/tmp") else None
        )
        print("  ✓ Revenue chart created")
        if os.path.exists("/tmp/revenue_chart.png"):
            print("  ✓ Chart saved to /tmp/revenue_chart.png")
    except ImportError:
        print("  ⚠️  Chart requires matplotlib: pip install matplotlib")
    except Exception as e:
        print(f"  ❌ Chart error: {e}")
    
    print()

if __name__ == "__main__":
    print("TOL Python: Serie Table and Chart Methods Demo")
    print("=" * 70)
    print()
    
    test_table_method()
    test_table_with_missing()
    test_chart_method()
    demo_combined_visualization()
    demo_business_scenario()
    
    print("=" * 70)
    print("✅ All table and chart tests completed!")
    print()
    print("📋 Table Method Features:")
    print("  • Display time series data in tabular format")
    print("  • Multiple table formats (grid, plain, simple, github, pipe)")
    print("  • Handle missing values gracefully")
    print("  • Limit rows with max_rows parameter")
    print("  • Fallback to simple format if tabulate not available")
    print()
    print("📊 Chart Method Features:")
    print("  • Multiple chart styles (line, bar, scatter, area)")
    print("  • Customizable title and figure size")
    print("  • Automatic date formatting on x-axis")
    print("  • Save charts to file")
    print("  • Handle missing values (displayed as gaps)")
    print("  • Professional styling with grid and proper layout")
    print()
    print("💡 Usage Tips:")
    print("  • Install dependencies: pip install matplotlib tabulate")
    print("  • Use show=False for automated/batch processing")
    print("  • Combine with DatCh/CalInd for powerful analysis")
    print("  • Table format 'github' works great for documentation")