"""
Test enhanced ARIMA with MLE, standard errors, and improved diagnostics
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from tol_python.series import Serie
from tol_python.arima import ARIMAFactor, ARIMA


def test_mle_vs_css():
    """Compare MLE vs CSS estimation"""
    print("Testing MLE vs CSS Estimation")
    print("=" * 40)
    
    # Generate AR(1) data
    np.random.seed(42)
    n = 200
    phi = 0.7
    sigma = 1.0
    
    y = np.zeros(n)
    errors = np.random.normal(0, sigma, n)
    
    for t in range(1, n):
        y[t] = phi * y[t-1] + errors[t]
    
    serie = Serie(data=y)
    
    # Test both methods
    factor = ARIMAFactor(ar_order=1, include_mean=False)
    
    # CSS estimation
    print("\n1. CSS Estimation:")
    model_css = ARIMA(factor)
    results_css = model_css.fit(serie, method="css")
    
    print(f"   AR parameter: {results_css.ar_params[0]:.4f}")
    print(f"   Sigma²: {results_css.sigma2:.4f}")
    print(f"   AIC: {results_css.aic:.2f}")
    print(f"   Converged: {results_css.converged}")
    
    # MLE estimation
    print("\n2. MLE Estimation:")
    try:
        model_mle = ARIMA(factor)
        results_mle = model_mle.fit(serie, method="mle")
        
        print(f"   AR parameter: {results_mle.ar_params[0]:.4f}")
        print(f"   Sigma²: {results_mle.sigma2:.4f}")
        print(f"   Log-likelihood: {results_mle.loglikelihood:.2f}")
        print(f"   AIC: {results_mle.aic:.2f}")
        print(f"   BIC: {results_mle.bic:.2f}")
        print(f"   Converged: {results_mle.converged}")
        
        # Check if we have standard errors
        if results_mle.standard_errors is not None:
            print(f"   AR std error: {results_mle.standard_errors['ar_se'][0]:.4f}")
            
            # T-statistics and p-values
            t_stats = results_mle.t_statistics
            p_vals = results_mle.p_values
            
            if t_stats is not None and 'ar_t' in t_stats:
                print(f"   AR t-statistic: {t_stats['ar_t'][0]:.3f}")
            
            if p_vals is not None and 'ar_p' in p_vals:
                print(f"   AR p-value: {p_vals['ar_p'][0]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"   MLE failed: {e}")
        return False


def test_mle_summary():
    """Test enhanced summary with MLE results"""
    print("\n\nTesting Enhanced Summary")
    print("=" * 40)
    
    # Generate ARMA(1,1) data
    np.random.seed(123)
    n = 150
    phi = 0.6
    theta = 0.4
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(1, n):
        y[t] = phi * y[t-1] + errors[t] + theta * errors[t-1]
    
    serie = Serie(data=y)
    
    # Fit ARMA(1,1) with MLE
    factor = ARIMAFactor(ar_order=1, ma_order=1, include_mean=False)
    model = ARIMA(factor)
    
    try:
        results = model.fit(serie, method="mle")
        
        print("\nMLE Summary:")
        print(results.summary())
        
        return True
        
    except Exception as e:
        print(f"MLE summary test failed: {e}")
        return False


def test_mle_forecasting():
    """Test forecasting with exact error variances"""
    print("\n\nTesting MLE Forecasting")
    print("=" * 40)
    
    # Generate AR(1) data
    np.random.seed(456)
    n = 100
    phi = 0.8
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(1, n):
        y[t] = phi * y[t-1] + errors[t]
    
    serie = Serie(data=y)
    
    # Fit with MLE
    factor = ARIMAFactor(ar_order=1, include_mean=False)
    model = ARIMA(factor)
    
    try:
        results = model.fit(serie, method="mle")
        
        print(f"Model fitted: AR parameter = {results.ar_params[0]:.3f}")
        
        # Generate forecasts
        forecasts, lower, upper = model.forecast(10)
        
        print(f"\nForecasts (first 5 steps):")
        forecast_data = forecasts._data.to_numpy()
        lower_data = lower._data.to_numpy()
        upper_data = upper._data.to_numpy()
        
        for i in range(min(5, len(forecast_data))):
            if not np.isnan(forecast_data[i]):
                print(f"  Step {i+1}: {forecast_data[i]:.3f} [{lower_data[i]:.3f}, {upper_data[i]:.3f}]")
        
        # Check forecast properties
        forecast_width = upper_data - lower_data
        print(f"\nForecast interval width (step 1): {forecast_width[0]:.3f}")
        print(f"Forecast interval width (step 5): {forecast_width[4]:.3f}")
        print(f"Uncertainty increases: {forecast_width[4] > forecast_width[0]}")
        
        return True
        
    except Exception as e:
        print(f"MLE forecasting test failed: {e}")
        return False


def test_model_comparison():
    """Test model comparison with exact AIC/BIC"""
    print("\n\nTesting Model Comparison")
    print("=" * 40)
    
    # Generate ARMA(1,1) data
    np.random.seed(789)
    n = 200
    phi = 0.5
    theta = 0.3
    
    y = np.zeros(n)
    errors = np.random.normal(0, 1, n)
    
    for t in range(1, n):
        y[t] = phi * y[t-1] + errors[t] + theta * errors[t-1]
    
    serie = Serie(data=y)
    
    # Test different model specifications
    models = [
        ("AR(1)", ARIMAFactor(ar_order=1, include_mean=False)),
        ("MA(1)", ARIMAFactor(ma_order=1, include_mean=False)),
        ("ARMA(1,1)", ARIMAFactor(ar_order=1, ma_order=1, include_mean=False)),
        ("AR(2)", ARIMAFactor(ar_order=2, include_mean=False)),
    ]
    
    results = []
    
    for name, factor in models:
        try:
            model = ARIMA(factor)
            result = model.fit(serie, method="mle")
            
            if np.isfinite(result.aic):
                results.append((name, result.aic, result.bic, result.loglikelihood))
                print(f"{name:>10s}: AIC={result.aic:6.2f}, BIC={result.bic:6.2f}, LL={result.loglikelihood:6.2f}")
            else:
                print(f"{name:>10s}: Estimation failed")
                
        except Exception as e:
            print(f"{name:>10s}: Error - {e}")
    
    if results:
        # Find best model by AIC
        best_aic = min(results, key=lambda x: x[1])
        best_bic = min(results, key=lambda x: x[2])
        
        print(f"\nBest by AIC: {best_aic[0]} (AIC = {best_aic[1]:.2f})")
        print(f"Best by BIC: {best_bic[0]} (BIC = {best_bic[1]:.2f})")
        
        return True
    
    return False


if __name__ == "__main__":
    print("Enhanced ARIMA (MLE) Test Suite")
    print("="*50)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_mle_vs_css()
    all_passed &= test_mle_summary()
    all_passed &= test_mle_forecasting()
    all_passed &= test_model_comparison()
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 All enhanced MLE tests passed!")
        print("Phase 4 MLE implementation is working correctly.")
        print("\nKey improvements achieved:")
        print("✓ Exact likelihood computation via Kalman filter")
        print("✓ Parameter standard errors and t-statistics")
        print("✓ Proper AIC/BIC calculation")
        print("✓ Enhanced forecasting with exact error variances")
        print("✓ Publication-quality summary output")
    else:
        print("❌ Some MLE tests failed.")
        print("Please check the implementation.")
    
    print("\nPhase 4 MLE implementation ready! ✨")