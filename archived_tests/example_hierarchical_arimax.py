"""
Hierarchical Bayesian ARIMAX Example

This example demonstrates how to:
1. Create ARIMAX models with input variables (Series type)
2. Define hierarchical priors for both ARIMA and input parameters
3. Run sophisticated hierarchical Bayesian estimation
4. Interpret results and perform model comparison

Example: Modeling GDP growth with unemployment rate and oil prices as inputs
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from series import Serie
from core import Date, TimeSet
from arima import ARIMAFactor
from bayesian import BayesianARIMA, MCMCConfig
from bayesian.priors import (
    NormalPrior, InverseGammaPrior, HierarchicalPrior, 
    MultivariateNormalPrior, ShrinkagePrior
)
from bayesian.core import BayesianModel, ParameterStore
from bayesian.mcmc import GibbsSampler


@dataclass
class ARIMAXResults:
    """Results from Hierarchical Bayesian ARIMAX estimation"""
    # ARIMA parameters
    ar_posterior_mean: Optional[np.ndarray] = None
    ar_credible_intervals: Optional[Tuple[np.ndar<PERSON>, np.n<PERSON><PERSON>]] = None
    
    # Input variable coefficients  
    input_posterior_means: Optional[Dict[str, float]] = None
    input_credible_intervals: Optional[Dict[str, Tuple[float, float]]] = None
    
    # Hierarchical parameters
    ar_hierarchy_variance: Optional[float] = None
    input_hierarchy_variance: Optional[float] = None
    
    # Model comparison
    dic: Optional[float] = None
    log_marginal_likelihood: Optional[float] = None
    
    # MCMC diagnostics
    effective_sample_sizes: Optional[Dict[str, float]] = None


class HierarchicalBayesianARIMAX(BayesianModel):
    """
    Hierarchical Bayesian ARIMAX Model
    
    Extends ARIMA with input variables and hierarchical priors:
    
    Model Structure:
    y_t = c + φ₁y_{t-1} + ... + φₚy_{t-p} + β₁x₁_t + ... + βₖxₖ_t + ε_t
    
    Hierarchical Prior Structure:
    Level 1: φᵢ ~ N(0, τ²_AR), βⱼ ~ N(0, τ²_INPUT)  
    Level 2: τ²_AR ~ InverseGamma(α_AR, β_AR)
             τ²_INPUT ~ InverseGamma(α_INPUT, β_INPUT)
    Level 3: Hyperpriors on α, β parameters
    """
    
    def __init__(self, factor: ARIMAFactor, input_variables: Dict[str, Serie],
                 priors: Optional[Dict[str, any]] = None, config: Optional[MCMCConfig] = None):
        """
        Initialize Hierarchical Bayesian ARIMAX
        
        Args:
            factor: ARIMA specification (p,d,q)
            input_variables: Dictionary of input Series {name: Serie}
            priors: Hierarchical prior specifications
            config: MCMC configuration
        """
        super().__init__(config)
        
        self.factor = factor
        self.input_variables = input_variables
        self.input_names = list(input_variables.keys())
        
        # Set up default hierarchical priors if not provided
        if priors is None:
            priors = self._create_default_hierarchical_priors()
        
        # Add priors to model
        for param_name, prior in priors.items():
            self.add_prior(param_name, prior)
        
        # Set up hierarchical parameter blocks
        self._setup_hierarchical_blocks()
        
        # Data storage
        self.target_series = None
        self.input_data_matrix = None
        
    def _create_default_hierarchical_priors(self) -> Dict[str, any]:
        """Create sophisticated hierarchical prior structure"""
        priors = {}
        
        # === ARIMA PARAMETER HIERARCHY ===
        # Level 2: Hyperprior for AR coefficient variance
        ar_variance_hyperprior = InverseGammaPrior(
            shape=3.0, scale=2.0, name="AR_hierarchy_variance"
        )
        priors['tau2_ar'] = ar_variance_hyperprior
        
        # Level 1: AR coefficients with hierarchical structure
        ar_lags, _ = self.factor.get_polynomial_orders()
        for i, lag in enumerate(ar_lags):
            ar_prior = HierarchicalPrior(
                parameter_prior=NormalPrior(0.0, 1.0, name=f"AR_{lag}"),
                hyperprior=ar_variance_hyperprior,
                name=f"AR_{lag}_hierarchical"
            )
            priors[f'ar_{i}'] = ar_prior
        
        # === INPUT VARIABLE HIERARCHY ===
        # Level 2: Hyperprior for input coefficient variance
        input_variance_hyperprior = InverseGammaPrior(
            shape=3.0, scale=1.0, name="INPUT_hierarchy_variance"
        )
        priors['tau2_input'] = input_variance_hyperprior
        
        # Level 1: Input coefficients with hierarchical structure
        for input_name in self.input_names:
            input_prior = HierarchicalPrior(
                parameter_prior=NormalPrior(0.0, 1.0, name=f"BETA_{input_name}"),
                hyperprior=input_variance_hyperprior,
                name=f"BETA_{input_name}_hierarchical"
            )
            priors[f'beta_{input_name}'] = input_prior
        
        # === ALTERNATIVE: SHRINKAGE PRIORS ===
        # Uncomment for shrinkage-based hierarchy
        # for input_name in self.input_names:
        #     priors[f'beta_{input_name}'] = ShrinkagePrior(
        #         type="horseshoe", scale=1.0, name=f"BETA_{input_name}_shrinkage"
        #     )
        
        # Intercept term
        priors['intercept'] = NormalPrior(0.0, 10.0, name="intercept", auto_scale=True)
        
        # Error variance
        priors['sigma2'] = InverseGammaPrior(shape=3.0, scale=2.0, name="error_variance")
        
        return priors
    
    def _setup_hierarchical_blocks(self) -> None:
        """Set up parameter blocks for efficient hierarchical sampling"""
        
        # AR parameters block (sampled jointly)
        ar_lags, _ = self.factor.get_polynomial_orders()
        if len(ar_lags) > 0:
            ar_param_names = [f'ar_{i}' for i in range(len(ar_lags))]
            self.add_parameter_block("ar_block", ar_param_names, "ar_coefficients")
        
        # Input parameters block (sampled jointly) 
        if len(self.input_names) > 0:
            input_param_names = [f'beta_{name}' for name in self.input_names]
            self.add_parameter_block("input_block", input_param_names, "input_coefficients")
        
        # Hierarchical variance block
        variance_params = ['sigma2']
        if 'tau2_ar' in self.priors:
            variance_params.append('tau2_ar')
        if 'tau2_input' in self.priors:
            variance_params.append('tau2_input')
        
        self.add_parameter_block("variance_block", variance_params, "hierarchical_variances")
        
        # Intercept block
        if 'intercept' in self.priors:
            self.add_parameter_block("intercept_block", ["intercept"], "intercept")
    
    def fit_data(self, target_series: Serie) -> None:
        """
        Prepare data for hierarchical Bayesian estimation
        
        Args:
            target_series: Target variable (y_t)
        """
        self.target_series = target_series
        
        # Align all series to common time frame
        aligned_data = self._align_series_data()
        
        # Apply differencing to target if needed
        if self.factor.diff_order > 0:
            self.target_data = self._apply_differencing(aligned_data['target'])
            # Also difference input variables if they are I(1)
            self.input_data_matrix = self._prepare_input_matrix(
                aligned_data, apply_differencing=True
            )
        else:
            self.target_data = aligned_data['target']
            self.input_data_matrix = self._prepare_input_matrix(aligned_data)
        
        # Auto-scale priors based on data
        self._auto_scale_hierarchical_priors()
    
    def _align_series_data(self) -> Dict[str, np.ndarray]:
        """Align target and input series to common time frame"""
        
        # Find common time period
        all_series = {'target': self.target_series}
        all_series.update(self.input_variables)
        
        # Simple alignment - use intersection of valid data
        target_data = self.target_series._data.to_numpy()
        target_valid = ~np.isnan(target_data)
        
        aligned_data = {'target': target_data[target_valid]}
        
        for name, serie in self.input_variables.items():
            input_data = serie._data.to_numpy()
            if len(input_data) == len(target_data):
                aligned_data[name] = input_data[target_valid]
            else:
                # Handle length mismatch - truncate or pad as needed
                min_len = min(len(input_data), len(target_data))
                aligned_data[name] = input_data[:min_len][target_valid[:min_len]]
        
        return aligned_data
    
    def _apply_differencing(self, data: np.ndarray) -> np.ndarray:
        """Apply differencing to make series stationary"""
        differenced = data.copy()
        for _ in range(self.factor.diff_order):
            differenced = np.diff(differenced)
        return differenced
    
    def _prepare_input_matrix(self, aligned_data: Dict[str, np.ndarray], 
                            apply_differencing: bool = False) -> np.ndarray:
        """Prepare matrix of input variables"""
        
        input_matrix = []
        min_length = len(aligned_data['target'])
        
        for name in self.input_names:
            if name in aligned_data:
                input_data = aligned_data[name]
                
                if apply_differencing:
                    # Apply same differencing as target
                    for _ in range(self.factor.diff_order):
                        input_data = np.diff(input_data)
                
                # Ensure same length as (differenced) target
                if len(input_data) >= min_length:
                    input_matrix.append(input_data[:min_length])
                else:
                    # Pad with last value if too short
                    padded = np.pad(input_data, (0, min_length - len(input_data)), 
                                  mode='constant', constant_values=input_data[-1])
                    input_matrix.append(padded)
        
        return np.column_stack(input_matrix) if input_matrix else np.array([]).reshape(min_length, 0)
    
    def _auto_scale_hierarchical_priors(self) -> None:
        """Automatically scale hierarchical priors based on data characteristics"""
        
        if self.target_data is None:
            return
        
        # Data characteristics
        target_std = np.std(self.target_data)
        target_mean = np.mean(self.target_data)
        
        # Scale input variable prior variance based on correlation with target
        if self.input_data_matrix.shape[1] > 0:
            for i, input_name in enumerate(self.input_names):
                input_data = self.input_data_matrix[:, i]
                correlation = np.corrcoef(self.target_data, input_data)[0, 1]
                
                # Higher correlation suggests potentially larger effect
                expected_effect_size = abs(correlation) * target_std / np.std(input_data)
                
                # Scale prior variance
                if f'beta_{input_name}' in self.priors:
                    prior = self.priors[f'beta_{input_name}']
                    if hasattr(prior, 'auto_scale_variance'):
                        prior.auto_scale_variance(expected_effect_size)
        
        # Scale intercept prior
        if 'intercept' in self.priors:
            intercept_prior = self.priors['intercept']
            if hasattr(intercept_prior, 'auto_scale_variance'):
                intercept_prior.auto_scale_variance(abs(target_mean))
    
    def log_likelihood(self, parameters: Dict[str, np.ndarray]) -> float:
        """
        Evaluate log-likelihood for ARIMAX model
        
        Args:
            parameters: Dictionary of all model parameters
            
        Returns:
            Log-likelihood value
        """
        if self.target_data is None:
            return -np.inf
        
        try:
            # Extract parameters
            ar_params = self._extract_ar_parameters(parameters)
            input_coefficients = self._extract_input_coefficients(parameters)
            sigma2 = parameters.get('sigma2', 1.0)
            intercept = parameters.get('intercept', 0.0)
            
            # Build ARIMAX likelihood
            log_lik = self._compute_arimax_likelihood(
                self.target_data, ar_params, input_coefficients, sigma2, intercept
            )
            
            return log_lik if np.isfinite(log_lik) else -np.inf
            
        except Exception as e:
            return -np.inf
    
    def _extract_ar_parameters(self, parameters: Dict[str, np.ndarray]) -> np.ndarray:
        """Extract AR parameters in correct order"""
        ar_lags, _ = self.factor.get_polynomial_orders()
        ar_params = []
        
        for i in range(len(ar_lags)):
            param_name = f'ar_{i}'
            if param_name in parameters:
                ar_params.append(parameters[param_name])
        
        return np.array(ar_params)
    
    def _extract_input_coefficients(self, parameters: Dict[str, np.ndarray]) -> np.ndarray:
        """Extract input variable coefficients"""
        coefficients = []
        
        for input_name in self.input_names:
            param_name = f'beta_{input_name}'
            if param_name in parameters:
                coefficients.append(parameters[param_name])
        
        return np.array(coefficients)
    
    def _compute_arimax_likelihood(self, target_data: np.ndarray, ar_params: np.ndarray,
                                 input_coefficients: np.ndarray, sigma2: float, 
                                 intercept: float) -> float:
        """Compute ARIMAX log-likelihood"""
        
        n = len(target_data)
        p = len(ar_params)
        
        if n <= p:
            return -np.inf
        
        # Initialize residuals
        residuals = np.zeros(n)
        
        # Compute residuals for t = p, p+1, ..., n-1
        for t in range(p, n):
            # AR component
            ar_component = sum(ar_params[i] * target_data[t-1-i] for i in range(p))
            
            # Input variables component
            input_component = 0.0
            if len(input_coefficients) > 0 and self.input_data_matrix.shape[1] > 0:
                if t < len(self.input_data_matrix):
                    input_values = self.input_data_matrix[t, :len(input_coefficients)]
                    input_component = np.dot(input_coefficients, input_values)
            
            # Predicted value
            predicted = intercept + ar_component + input_component
            
            # Residual
            residuals[t] = target_data[t] - predicted
        
        # Log-likelihood (Gaussian)
        valid_residuals = residuals[p:]
        n_valid = len(valid_residuals)
        
        if n_valid == 0 or sigma2 <= 0:
            return -np.inf
        
        log_lik = -0.5 * n_valid * np.log(2 * np.pi * sigma2)
        log_lik -= 0.5 * np.sum(valid_residuals**2) / sigma2
        
        return log_lik


def create_realistic_economic_example():
    """
    Create realistic economic example: GDP growth with unemployment and oil prices
    """
    print("=" * 80)
    print("HIERARCHICAL BAYESIAN ARIMAX EXAMPLE")
    print("GDP Growth with Unemployment Rate and Oil Prices")
    print("=" * 80)
    
    # Simulate realistic quarterly economic data
    np.random.seed(2024)
    n_quarters = 100  # 25 years of quarterly data
    
    # Create dates
    start_date = Date("y1999m01d01")
    dates = [start_date.add_months(3*i) for i in range(n_quarters)]
    
    # === SIMULATE UNDERLYING ECONOMIC RELATIONSHIPS ===
    
    # 1. Unemployment rate (mean-reverting around 6%)
    unemployment = np.zeros(n_quarters)
    unemployment[0] = 6.0  # Start at 6%
    
    for t in range(1, n_quarters):
        # Mean reversion to 6% with business cycle
        cycle = 2.0 * np.sin(2 * np.pi * t / 32)  # 8-year cycle
        unemployment[t] = 0.3 * 6.0 + 0.7 * unemployment[t-1] + 0.2 * cycle + np.random.normal(0, 0.3)
        unemployment[t] = max(unemployment[t], 3.0)  # Floor at 3%
    
    # 2. Oil prices (with volatility clustering)
    oil_prices = np.zeros(n_quarters)
    oil_prices[0] = 50.0  # Start at $50/barrel
    
    for t in range(1, n_quarters):
        # Random walk with drift and mean reversion
        drift = 0.01
        mean_reversion = -0.05 * (oil_prices[t-1] - 60)  # Target $60
        shock = np.random.normal(0, 5)
        oil_prices[t] = oil_prices[t-1] + drift + mean_reversion + shock
        oil_prices[t] = max(oil_prices[t], 20.0)  # Floor at $20
    
    # 3. GDP growth (depends on unemployment and oil prices)
    gdp_growth = np.zeros(n_quarters)
    gdp_growth[0] = 0.006  # Start at 0.6% quarterly
    
    for t in range(1, n_quarters):
        # Okun's law: GDP growth inversely related to unemployment changes
        unemployment_effect = -0.4 * (unemployment[t] - unemployment[t-1])
        
        # Oil price effect (negative for oil price increases)
        oil_effect = -0.0002 * (oil_prices[t] - oil_prices[t-1])
        
        # AR(1) persistence + trend
        trend = 0.005  # 0.5% quarterly trend
        persistence = 0.3 * gdp_growth[t-1]
        
        # Random shock
        shock = np.random.normal(0, 0.005)
        
        gdp_growth[t] = trend + persistence + unemployment_effect + oil_effect + shock
    
    # Create Series objects
    gdp_serie = Serie(data=gdp_growth, dates=dates)
    unemployment_serie = Serie(data=unemployment, dates=dates)
    oil_serie = Serie(data=oil_prices, dates=dates)
    
    print(f"Data Summary ({n_quarters} quarters):")
    print(f"  GDP Growth: Mean={np.mean(gdp_growth)*100:.2f}%, Std={np.std(gdp_growth)*100:.2f}%")
    print(f"  Unemployment: Mean={np.mean(unemployment):.2f}%, Std={np.std(unemployment):.2f}%")
    print(f"  Oil Prices: Mean=${np.mean(oil_prices):.1f}, Std=${np.std(oil_prices):.1f}")
    
    return gdp_serie, {'unemployment': unemployment_serie, 'oil_prices': oil_serie}


def define_sophisticated_hierarchical_priors():
    """
    Define sophisticated hierarchical priors for economic interpretation
    """
    print("\n" + "=" * 60)
    print("HIERARCHICAL PRIOR SPECIFICATION")
    print("=" * 60)
    
    # === THREE-LEVEL HIERARCHICAL STRUCTURE ===
    
    # Level 3: Hyperpriors on hyperparameters
    ar_shape_hyperprior = InverseGammaPrior(shape=2.0, scale=1.0, name="AR_shape_hyper")
    input_shape_hyperprior = InverseGammaPrior(shape=2.0, scale=0.5, name="INPUT_shape_hyper")
    
    # Level 2: Hyperpriors for group variances
    ar_variance_hyperprior = InverseGammaPrior(
        shape=3.0, scale=2.0, name="AR_group_variance"
    )
    
    # Separate variance for different types of inputs
    unemployment_variance_hyperprior = InverseGammaPrior(
        shape=4.0, scale=1.0, name="UNEMPLOYMENT_group_variance"  # More informative
    )
    
    oil_variance_hyperprior = InverseGammaPrior(
        shape=3.0, scale=0.5, name="OIL_group_variance"  # Different scale
    )
    
    # Level 1: Parameter priors with economic constraints
    priors = {}
    
    # AR coefficients: expect moderate persistence
    priors['ar_0'] = HierarchicalPrior(
        parameter_prior=NormalPrior(0.3, 1.0, name="AR1_GDP"),  # Prior mean of 0.3
        hyperprior=ar_variance_hyperprior,
        name="AR1_hierarchical"
    )
    
    # Unemployment coefficient: expect negative (Okun's law)
    priors['beta_unemployment'] = HierarchicalPrior(
        parameter_prior=NormalPrior(-0.5, 1.0, name="BETA_unemployment"),  # Negative prior mean
        hyperprior=unemployment_variance_hyperprior,
        name="BETA_unemployment_hierarchical"
    )
    
    # Oil price coefficient: expect small negative effect
    priors['beta_oil_prices'] = HierarchicalPrior(
        parameter_prior=NormalPrior(-0.0001, 1.0, name="BETA_oil_prices"),
        hyperprior=oil_variance_hyperprior,
        name="BETA_oil_prices_hierarchical"
    )
    
    # Store hyperpriors for sampling
    priors['tau2_ar'] = ar_variance_hyperprior
    priors['tau2_unemployment'] = unemployment_variance_hyperprior
    priors['tau2_oil'] = oil_variance_hyperprior
    
    # GDP trend (intercept): expect positive
    priors['intercept'] = NormalPrior(0.005, 0.01, name="GDP_trend")  # 0.5% quarterly
    
    # Error variance
    priors['sigma2'] = InverseGammaPrior(shape=5.0, scale=0.00005, name="error_variance")
    
    print("Hierarchical Prior Structure:")
    print("  Level 3: Hyperpriors on group variance parameters")
    print("  Level 2: Group variances for AR and input coefficients")
    print("  Level 1: Individual parameter priors")
    print()
    print("Economic Priors:")
    print("  AR(1) coefficient: N(0.3, τ²_AR) - moderate persistence")
    print("  Unemployment effect: N(-0.5, τ²_UNEMP) - Okun's law")
    print("  Oil price effect: N(-0.0001, τ²_OIL) - small negative")
    print("  GDP trend: N(0.005, 0.01²) - 0.5% quarterly growth")
    
    return priors


def run_hierarchical_estimation(gdp_serie, input_variables, priors):
    """
    Run hierarchical Bayesian ARIMAX estimation
    """
    print("\n" + "=" * 60)
    print("HIERARCHICAL BAYESIAN ESTIMATION")
    print("=" * 60)
    
    # ARIMAX specification: AR(1) with input variables
    factor = ARIMAFactor(ar_order=1, diff_order=0, ma_order=0)
    
    # MCMC configuration
    config = MCMCConfig(
        mcmc_burnin=2000,           # Longer burn-in for hierarchical model
        mcmc_sample_length=8000,    # More samples for good convergence
        mcmc_cache_length=1000,
        convergence_tolerance=1e-5,
        adaptation_period=1000,     # Longer adaptation
        compute_diagnostics=True,
        random_seed=2024
    )
    
    # Create hierarchical ARIMAX model
    print("Initializing Hierarchical Bayesian ARIMAX...")
    hierarchical_arimax = HierarchicalBayesianARIMAX(
        factor=factor,
        input_variables=input_variables,
        priors=priors,
        config=config
    )
    
    # Fit the model
    print("Fitting hierarchical model (this may take a few minutes)...")
    hierarchical_arimax.fit_data(gdp_serie)
    
    # Run MCMC sampling
    sampler = GibbsSampler(hierarchical_arimax, config)
    mcmc_results = sampler.run_chain()
    
    print("✓ Hierarchical Bayesian estimation completed!")
    
    return hierarchical_arimax, mcmc_results


def analyze_hierarchical_results(hierarchical_arimax, mcmc_results):
    """
    Analyze and interpret hierarchical Bayesian results
    """
    print("\n" + "=" * 60)
    print("HIERARCHICAL RESULTS ANALYSIS")
    print("=" * 60)
    
    parameter_store = mcmc_results['parameter_store']
    
    # === LEVEL 1: PARAMETER ESTIMATES ===
    print("\nLevel 1 - Parameter Estimates:")
    print("-" * 40)
    
    # AR coefficient
    ar_samples = parameter_store.get_history('ar_0')
    if len(ar_samples) > 0:
        ar_mean = np.mean(ar_samples)
        ar_std = np.std(ar_samples)
        ar_ci = np.quantile(ar_samples, [0.025, 0.975])
        print(f"AR(1) coefficient: {ar_mean:.4f} ± {ar_std:.4f}")
        print(f"  95% CI: [{ar_ci[0]:.4f}, {ar_ci[1]:.4f}]")
        print(f"  Economic interpretation: {'Strong' if ar_mean > 0.5 else 'Moderate' if ar_mean > 0.2 else 'Weak'} persistence")
    
    # Unemployment coefficient
    unemployment_samples = parameter_store.get_history('beta_unemployment')
    if len(unemployment_samples) > 0:
        unemp_mean = np.mean(unemployment_samples)
        unemp_std = np.std(unemployment_samples)
        unemp_ci = np.quantile(unemployment_samples, [0.025, 0.975])
        print(f"\nUnemployment effect: {unemp_mean:.4f} ± {unemp_std:.4f}")
        print(f"  95% CI: [{unemp_ci[0]:.4f}, {unemp_ci[1]:.4f}]")
        
        # Economic interpretation
        if unemp_ci[1] < 0:
            print(f"  Economic interpretation: Strong evidence for Okun's law (negative effect)")
        elif unemp_mean < 0:
            print(f"  Economic interpretation: Suggests negative effect (consistent with Okun's law)")
        else:
            print(f"  Economic interpretation: Positive effect (contrary to Okun's law)")
    
    # Oil price coefficient  
    oil_samples = parameter_store.get_history('beta_oil_prices')
    if len(oil_samples) > 0:
        oil_mean = np.mean(oil_samples)
        oil_std = np.std(oil_samples)
        oil_ci = np.quantile(oil_samples, [0.025, 0.975])
        print(f"\nOil price effect: {oil_mean:.6f} ± {oil_std:.6f}")
        print(f"  95% CI: [{oil_ci[0]:.6f}, {oil_ci[1]:.6f}]")
        
        # Economic magnitude
        oil_shock_effect = oil_mean * 10  # $10 oil price increase
        print(f"  Economic interpretation: $10 oil increase → {oil_shock_effect*100:.3f}% GDP growth change")
    
    # GDP trend
    intercept_samples = parameter_store.get_history('intercept')
    if len(intercept_samples) > 0:
        trend_mean = np.mean(intercept_samples)
        trend_std = np.std(intercept_samples)
        trend_ci = np.quantile(intercept_samples, [0.025, 0.975])
        print(f"\nGDP trend: {trend_mean:.6f} ± {trend_std:.6f}")
        print(f"  95% CI: [{trend_ci[0]:.6f}, {trend_ci[1]:.6f}]")
        print(f"  Annualized: {trend_mean*400:.2f}% ± {trend_std*400:.2f}%")
    
    # === LEVEL 2: HIERARCHICAL VARIANCES ===
    print("\n" + "-" * 40)
    print("Level 2 - Hierarchical Group Variances:")
    print("-" * 40)
    
    # AR group variance
    ar_var_samples = parameter_store.get_history('tau2_ar')
    if len(ar_var_samples) > 0:
        ar_var_mean = np.mean(ar_var_samples)
        ar_var_ci = np.quantile(ar_var_samples, [0.025, 0.975])
        print(f"AR group variance (τ²_AR): {ar_var_mean:.4f}")
        print(f"  95% CI: [{ar_var_ci[0]:.4f}, {ar_var_ci[1]:.4f}]")
        print(f"  Interpretation: {'High' if ar_var_mean > 1 else 'Moderate' if ar_var_mean > 0.1 else 'Low'} heterogeneity in AR effects")
    
    # Input group variances
    for input_name in ['unemployment', 'oil']:
        var_param = f'tau2_{input_name}'
        if var_param in parameter_store.parameter_names:
            var_samples = parameter_store.get_history(var_param)
            if len(var_samples) > 0:
                var_mean = np.mean(var_samples)
                var_ci = np.quantile(var_samples, [0.025, 0.975])
                print(f"{input_name.title()} group variance: {var_mean:.4f}")
                print(f"  95% CI: [{var_ci[0]:.4f}, {var_ci[1]:.4f}]")
    
    # === MCMC DIAGNOSTICS ===
    print("\n" + "-" * 40)
    print("MCMC Convergence Diagnostics:")
    print("-" * 40)
    
    from bayesian.mcmc.diagnostics import MCMCDiagnostics
    
    # Check key parameters
    key_params = ['ar_0', 'beta_unemployment', 'beta_oil_prices', 'intercept']
    
    for param in key_params:
        if param in parameter_store.parameter_names:
            samples = parameter_store.get_history(param)
            if len(samples) > 100:
                # Effective sample size
                ess = MCMCDiagnostics.effective_sample_size(samples)
                efficiency = ess / len(samples)
                
                # Geweke diagnostic
                geweke_result = MCMCDiagnostics.geweke_diagnostic(samples)
                
                print(f"{param}:")
                print(f"  ESS: {ess:.1f} ({efficiency:.1%} efficiency)")
                print(f"  Geweke Z: {geweke_result['z_score']:.3f} (p={geweke_result['p_value']:.3f})")
                print(f"  Convergence: {'✓' if geweke_result['p_value'] > 0.05 else '⚠'}")


def compare_models(gdp_serie, input_variables):
    """
    Compare different model specifications
    """
    print("\n" + "=" * 60)
    print("HIERARCHICAL MODEL COMPARISON")
    print("=" * 60)
    
    models_to_compare = [
        {
            'name': 'AR(1) only',
            'inputs': {},
            'description': 'Baseline autoregressive model'
        },
        {
            'name': 'AR(1) + Unemployment',
            'inputs': {'unemployment': input_variables['unemployment']},
            'description': 'AR model with unemployment (Okun\'s law)'
        },
        {
            'name': 'AR(1) + Oil',
            'inputs': {'oil_prices': input_variables['oil_prices']},
            'description': 'AR model with oil price effects'
        },
        {
            'name': 'Full ARIMAX',
            'inputs': input_variables,
            'description': 'AR model with both unemployment and oil'
        }
    ]
    
    comparison_results = []
    
    # Quick MCMC config for comparison
    quick_config = MCMCConfig(
        mcmc_burnin=500,
        mcmc_sample_length=1500,
        random_seed=2024
    )
    
    for model_spec in models_to_compare:
        print(f"\nFitting: {model_spec['name']}")
        
        try:
            # Create model
            factor = ARIMAFactor(1, 0, 0)  # AR(1)
            
            if len(model_spec['inputs']) == 0:
                # Use standard Bayesian ARIMA for baseline
                from bayesian import BayesianARIMA
                model = BayesianARIMA(factor, config=quick_config)
                results = model.fit(gdp_serie)
                log_lik = None  # Would need to extract from results
                
            else:
                # Use hierarchical ARIMAX
                model = HierarchicalBayesianARIMAX(
                    factor=factor,
                    input_variables=model_spec['inputs'],
                    config=quick_config
                )
                model.fit_data(gdp_serie)
                
                # Quick sampling for comparison
                sampler = GibbsSampler(model, quick_config)
                mcmc_results = sampler.run_chain()
                
                # Approximate log marginal likelihood
                parameter_store = mcmc_results['parameter_store']
                if 'sigma2' in parameter_store.parameter_names:
                    sigma_samples = parameter_store.get_history('sigma2')
                    log_lik = -len(gdp_serie) * np.log(np.mean(sigma_samples)) / 2  # Rough approximation
                else:
                    log_lik = None
            
            # Store results
            comparison_results.append({
                'name': model_spec['name'],
                'description': model_spec['description'],
                'n_inputs': len(model_spec['inputs']),
                'log_likelihood': log_lik,
                'model': model
            })
            
            print(f"  ✓ Completed: {model_spec['name']}")
            
        except Exception as e:
            print(f"  ✗ Failed: {model_spec['name']} - {str(e)[:50]}...")
            continue
    
    # Display comparison
    print(f"\nModel Comparison Results:")
    print(f"{'Model':>20s} {'Inputs':>7s} {'Log ML':>10s} {'Description':>30s}")
    print("-" * 70)
    
    for result in comparison_results:
        log_ml_str = f"{result['log_likelihood']:.1f}" if result['log_likelihood'] else "N/A"
        print(f"{result['name']:>20s} {result['n_inputs']:>7d} {log_ml_str:>10s} {result['description']:>30s}")
    
    # Economic insights
    print(f"\nEconomic Insights:")
    print(f"  • Hierarchical priors allow borrowing strength across parameters")
    print(f"  • Group-level variances reveal parameter heterogeneity")
    print(f"  • Model comparison shows value of economic variables")
    print(f"  • Bayesian approach quantifies all uncertainty")


def create_forecasts_with_scenarios(hierarchical_arimax, mcmc_results):
    """
    Generate forecasts with different economic scenarios
    """
    print("\n" + "=" * 60)
    print("SCENARIO-BASED FORECASTING")
    print("=" * 60)
    
    parameter_store = mcmc_results['parameter_store']
    
    # Extract posterior samples
    ar_samples = parameter_store.get_history('ar_0')
    unemp_samples = parameter_store.get_history('beta_unemployment')
    oil_samples = parameter_store.get_history('beta_oil_prices')
    intercept_samples = parameter_store.get_history('intercept')
    sigma_samples = parameter_store.get_history('sigma2')
    
    if not all(len(samples) > 0 for samples in [ar_samples, unemp_samples, oil_samples]):
        print("Insufficient MCMC samples for forecasting")
        return
    
    # Define scenarios
    scenarios = {
        'Baseline': {
            'unemployment_change': 0.0,  # No change
            'oil_price_change': 0.0,     # No change
            'description': 'Current conditions continue'
        },
        'Economic Expansion': {
            'unemployment_change': -1.0,  # 1pp decrease in unemployment
            'oil_price_change': 5.0,      # $5 oil price increase
            'description': 'Unemployment falls, oil prices rise'
        },
        'Recession Risk': {
            'unemployment_change': 2.0,   # 2pp increase in unemployment  
            'oil_price_change': -10.0,    # $10 oil price decrease
            'description': 'Unemployment rises, oil prices fall'
        },
        'Oil Shock': {
            'unemployment_change': 0.5,   # 0.5pp increase in unemployment
            'oil_price_change': 20.0,     # $20 oil price increase  
            'description': 'Major oil price shock'
        }
    }
    
    forecast_horizon = 8  # 2 years ahead
    n_forecast_samples = 200
    
    print(f"Generating {forecast_horizon}-quarter forecasts under different scenarios:")
    print()
    
    scenario_forecasts = {}
    
    for scenario_name, scenario in scenarios.items():
        print(f"Scenario: {scenario_name}")
        print(f"  {scenario['description']}")
        
        # Generate forecasts for this scenario
        forecasts = []
        
        for i in range(min(n_forecast_samples, len(ar_samples))):
            # Sample parameters from posterior
            ar_coef = ar_samples[i]
            unemp_coef = unemp_samples[i]
            oil_coef = oil_samples[i]
            intercept = intercept_samples[i]
            sigma = np.sqrt(sigma_samples[i])
            
            # Generate forecast path
            forecast_path = []
            current_gdp = hierarchical_arimax.target_data[-1]  # Last observed GDP growth
            
            for h in range(forecast_horizon):
                # Scenario-based input effects
                unemp_effect = unemp_coef * scenario['unemployment_change']
                oil_effect = oil_coef * scenario['oil_price_change']
                
                # Forecast
                forecast = intercept + ar_coef * current_gdp + unemp_effect + oil_effect
                forecast += np.random.normal(0, sigma)  # Add uncertainty
                
                forecast_path.append(forecast)
                current_gdp = forecast  # Update for next period
            
            forecasts.append(forecast_path)
        
        # Compute forecast statistics
        forecasts = np.array(forecasts)
        forecast_mean = np.mean(forecasts, axis=0)
        forecast_lower = np.quantile(forecasts, 0.05, axis=0)
        forecast_upper = np.quantile(forecasts, 0.95, axis=0)
        
        scenario_forecasts[scenario_name] = {
            'mean': forecast_mean,
            'lower': forecast_lower,
            'upper': forecast_upper
        }
        
        # Display results
        print(f"  Quarterly forecasts (% growth):")
        for q in range(min(4, forecast_horizon)):
            mean_pct = forecast_mean[q] * 100
            lower_pct = forecast_lower[q] * 100
            upper_pct = forecast_upper[q] * 100
            print(f"    Q{q+1}: {mean_pct:5.2f}% [{lower_pct:5.2f}%, {upper_pct:5.2f}%]")
        
        # Annual summary
        annual_mean = np.mean(forecast_mean[:4]) * 400  # Annualized
        print(f"  Average annual growth: {annual_mean:.2f}%")
        print()
    
    # Scenario comparison
    print("Scenario Impact Summary:")
    print(f"{'Scenario':>15s} {'Year 1':>8s} {'Year 2':>8s} {'Difference':>12s}")
    print("-" * 50)
    
    baseline_y1 = np.mean(scenario_forecasts['Baseline']['mean'][:4]) * 400
    
    for scenario_name, forecast in scenario_forecasts.items():
        y1_growth = np.mean(forecast['mean'][:4]) * 400
        y2_growth = np.mean(forecast['mean'][4:8]) * 400 if forecast_horizon >= 8 else np.nan
        diff_from_baseline = y1_growth - baseline_y1
        
        y2_str = f"{y2_growth:.2f}%" if not np.isnan(y2_growth) else "N/A"
        diff_str = f"{diff_from_baseline:+.2f}pp"
        
        print(f"{scenario_name:>15s} {y1_growth:>7.2f}% {y2_str:>7s} {diff_str:>11s}")
    
    return scenario_forecasts


if __name__ == "__main__":
    """
    Complete example of hierarchical Bayesian ARIMAX estimation
    """
    
    print("🚀 HIERARCHICAL BAYESIAN ARIMAX ESTIMATION")
    print("=" * 80)
    print("This example demonstrates sophisticated hierarchical Bayesian modeling")
    print("where users can add input variables and define hierarchical priors.")
    print("=" * 80)
    
    try:
        # Step 1: Create realistic economic data
        gdp_serie, input_variables = create_realistic_economic_example()
        
        # Step 2: Define sophisticated hierarchical priors
        hierarchical_priors = define_sophisticated_hierarchical_priors()
        
        # Step 3: Run hierarchical Bayesian estimation
        hierarchical_arimax, mcmc_results = run_hierarchical_estimation(
            gdp_serie, input_variables, hierarchical_priors
        )
        
        # Step 4: Analyze hierarchical results
        analyze_hierarchical_results(hierarchical_arimax, mcmc_results)
        
        # Step 5: Compare different model specifications
        compare_models(gdp_serie, input_variables)
        
        # Step 6: Generate scenario-based forecasts
        scenario_forecasts = create_forecasts_with_scenarios(hierarchical_arimax, mcmc_results)
        
        print("\n" + "=" * 80)
        print("✅ HIERARCHICAL BAYESIAN ARIMAX ESTIMATION COMPLETED!")
        print("=" * 80)
        print("Key Features Demonstrated:")
        print("✓ Input variables as Serie objects")
        print("✓ Multi-level hierarchical prior structure")
        print("✓ Group-level variance parameters")
        print("✓ Economic interpretation of parameters")
        print("✓ MCMC convergence diagnostics")
        print("✓ Model comparison across specifications")
        print("✓ Scenario-based forecasting")
        print("✓ Full uncertainty quantification")
        print()
        print("This framework allows users to:")
        print("• Add any number of input variables (Serie objects)")
        print("• Define custom hierarchical priors for all parameters")
        print("• Specify group-level variance structures")
        print("• Incorporate economic knowledge through informative priors")
        print("• Perform sophisticated Bayesian model comparison")
        print("• Generate forecasts under different scenarios")
        
    except Exception as e:
        print(f"\n❌ Error in hierarchical estimation: {str(e)}")
        import traceback
        traceback.print_exc()
        
    print("\n🎯 Ready for production-level hierarchical Bayesian econometrics!")