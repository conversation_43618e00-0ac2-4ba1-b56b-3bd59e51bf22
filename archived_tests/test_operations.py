#!/usr/bin/env python3
"""
Test Serie operations - arithmetic, transformations, aggregations
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tol_python import Serie, Date, TimeSet
import numpy as np


def test_arithmetic_operations():
    """Test arithmetic operations on Series"""
    print("=== Arithmetic Operations Test ===\n")
    
    # Create two series
    s1 = Serie(data=[1, 2, 3, 4, 5],
               first_date="y2023m01d01",
               last_date="y2023m01d05")
    
    s2 = Serie(data=[10, 20, 30, 40, 50],
               first_date="y2023m01d01",
               last_date="y2023m01d05")
    
    print(f"Serie 1: {list(s1.to_dict().values())}")
    print(f"Serie 2: {list(s2.to_dict().values())}")
    
    # Addition
    s_add = s1 + s2
    print(f"\nS1 + S2: {list(s_add.to_dict().values())}")
    
    # Scalar operations
    s_scalar = s1 * 2
    print(f"S1 * 2: {list(s_scalar.to_dict().values())}")
    
    s_scalar2 = s1 + 100
    print(f"S1 + 100: {list(s_scalar2.to_dict().values())}")
    
    # Division
    s_div = s2 / s1
    print(f"S2 / S1: {list(s_div.to_dict().values())}")
    
    # Aggregations
    print(f"\nSum of S1: {s1.sum()}")
    print(f"Mean of S1: {s1.mean()}")


def test_time_operations():
    """Test time-based operations like lag and diff"""
    print("\n\n=== Time Operations Test ===\n")
    
    # Create a serie with trend
    s = Serie(data=[100, 105, 110, 108, 115, 120, 118, 125],
              first_date="y2023m01d01",
              last_date="y2023m01d08")
    
    print("Original serie:")
    for date, value in list(s.to_dict().items())[:5]:
        print(f"  {date}: {value}")
    print("  ...")
    
    # Lag operation
    s_lag1 = s.lag(1)
    print("\nLagged by 1 period:")
    for date, value in list(s_lag1.to_dict().items())[:5]:
        print(f"  {date}: {value}")
    print("  ...")
    
    # Difference operation (1-B)
    s_diff = s.diff(1)
    print("\nFirst difference:")
    for date, value in list(s_diff.to_dict().items())[:5]:
        val_str = f"{value:.1f}" if value is not None and not np.isnan(value) else "NaN"
        print(f"  {date}: {val_str}")
    
    # Moving average
    s_ma3 = s.moving_average(3)
    print("\n3-period moving average:")
    for date, value in list(s_ma3.to_dict().items())[:5]:
        val_str = f"{value:.2f}" if value is not None and not np.isnan(value) else "NaN"
        print(f"  {date}: {val_str}")


def test_missing_values():
    """Test operations with missing values"""
    print("\n\n=== Missing Values Test ===\n")
    
    # Create series with missing values
    s1 = Serie(data=[1, None, 3, 4, None],
               first_date="y2023m01d01",
               last_date="y2023m01d05")
    
    s2 = Serie(data=[10, 20, None, 40, 50],
               first_date="y2023m01d01", 
               last_date="y2023m01d05")
    
    print("Serie 1 (with missing):", [s1[Date(f"y2023m01d{i:02d}")] for i in range(1, 6)])
    print("Serie 2 (with missing):", [s2[Date(f"y2023m01d{i:02d}")] for i in range(1, 6)])
    
    # Operations handle missing values
    s_add = s1 + s2
    print("\nS1 + S2 (missing propagated):", 
          [s_add[Date(f"y2023m01d{i:02d}")] for i in range(1, 6)])
    
    print(f"\nSum of S1 (ignoring missing): {s1.sum()}")
    print(f"Mean of S1 (ignoring missing): {s1.mean()}")


def test_apply_function():
    """Test applying custom functions"""
    print("\n\n=== Apply Function Test ===\n")
    
    s = Serie(data=[1, 4, 9, 16, 25],
              first_date="y2023m01d01",
              last_date="y2023m01d05")
    
    print(f"Original: {list(s.to_dict().values())}")
    
    # Apply square root
    s_sqrt = s.apply(np.sqrt)
    print(f"Square root: {list(s_sqrt.to_dict().values())}")
    
    # Apply custom function
    s_custom = s.apply(lambda x: x**2 + 2*x + 1)
    print(f"f(x) = x² + 2x + 1: {list(s_custom.to_dict().values())}")


def test_tol_compatibility():
    """Test TOL-style operations"""
    print("\n\n=== TOL Compatibility Test ===\n")
    
    # Recreate the TOL test example
    np.random.seed(42)
    
    # Create series similar to Rand(1,2,C) and Rand(-2,1,C)
    A = Serie(data=np.random.uniform(1, 2, 2),
              first_date="y2000m01d01",
              last_date="y2000m01d02")
    
    B = Serie(data=np.random.uniform(-2, 1, 2),
              first_date="y2000m01d01",
              last_date="y2000m01d02")
    
    # SumS equivalent
    a = A.sum()
    b = B.sum()
    
    print(f"Sum of A: {a:.6f}")
    print(f"Sum of B: {b:.6f}")
    
    # Test operations
    C = A + B
    c = C.sum()
    print(f"Sum of (A + B): {c:.6f}")
    print(f"Should equal a + b = {(a + b):.6f}")
    print(f"Match: {abs(c - (a + b)) < 1e-10}")


if __name__ == "__main__":
    test_arithmetic_operations()
    test_time_operations()
    test_missing_values()
    test_apply_function()
    test_tol_compatibility()