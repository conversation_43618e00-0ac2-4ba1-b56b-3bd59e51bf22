# TOL Python Developer Guide

## Getting Started

### Development Environment Setup

```bash
# Clone repository
git clone <repository-url>
cd TOL-python

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -e .
pip install -e ".[dev]"
```

### Project Structure

```
TOL-python/
├── core/                    # Core infrastructure
│   ├── __init__.py         # Core exports
│   ├── dates.py           # Date and TimeSet classes
│   └── data.py            # Data storage and management
├── series/                 # Time series implementation
│   ├── __init__.py        # Series exports
│   ├── serie.py           # Main Serie class
│   ├── corrected_serie.py # Enhanced Serie implementation
│   └── operations.py      # Series operations
├── arima/                  # ARIMA modeling
│   ├── __init__.py
│   ├── arima_model.py     # Core ARIMA implementation
│   ├── auto_arima.py      # Automatic model selection
│   ├── estimation.py      # Parameter estimation
│   ├── diagnostics.py     # Model diagnostics
│   └── kalman.py          # Kalman filtering
├── bayesian/               # Bayesian methods
│   ├── __init__.py
│   ├── arima.py           # Bayesian ARIMA
│   ├── core.py            # Bayesian core functionality
│   ├── priors.py          # Prior distributions
│   ├── model_selection.py # Bayesian model selection
│   └── mcmc/              # MCMC algorithms
│       ├── __init__.py
│       ├── gibbs.py       # Gibbs sampling
│       ├── metropolis.py  # Metropolis-Hastings
│       ├── arms.py        # Adaptive rejection sampling
│       └── diagnostics.py # MCMC diagnostics
├── stats/                  # Statistical functions
│   ├── __init__.py
│   ├── statistics.py      # Descriptive statistics
│   ├── test_functions.py  # Statistical tests
│   └── tests.py           # Test implementations
├── frequency/              # Frequency domain analysis
│   ├── __init__.py
│   ├── fft_ops.py         # FFT operations
│   └── filters.py         # Digital filters
├── complex/                # Complex series support
│   ├── __init__.py
│   └── complex_serie.py   # Complex time series
├── io/                     # Input/output operations
│   ├── __init__.py
│   └── serialize.py       # Serialization functions
└── archived_tests/         # Legacy test files
```

## Core Concepts

### 1. Date System

TOL uses a unique date format with sentinels for flexible time series handling.

```python
from tol_python.core import Date

# TOL date format: yYYYYmMMdDD
date = Date("y2023m01d15")  # January 15, 2023

# Special sentinels
begin_date = Date.Begin     # Beginning of time
end_date = Date.End         # End of time

# Date arithmetic
next_day = date.add_days(1)
next_month = date.add_months(1)
```

#### Key Features:
- **Sentinels**: `Begin` and `End` for unbounded ranges
- **Flexible Dating**: Support for incomplete dates (e.g., `y2023m01` for monthly)
- **TOL Compatibility**: Direct translation from TOL date strings

### 2. TimeSet System

TimeSets define dating patterns for time series.

```python
from tol_python.core import TimeSet, DatingType

# Basic dating types
daily = TimeSet("daily")
monthly = TimeSet("monthly")
yearly = TimeSet("yearly")

# Advanced patterns
last_day = TimeSet("last_day_of_month")
custom_dates = TimeSet("specific_dates", dates=["y2023m01d01", "y2023m06d01"])
periodic = TimeSet("periodic", period=7, offset=1)  # Weekly starting Monday
```

#### TimeSet Operations:
```python
# Check if date belongs to timeset
if daily.contains("y2023m01d15"):
    print("Date is valid")

# Get next/previous dates
next_date = monthly.next_date("y2023m01d01")  # y2023m02d01
prev_date = monthly.prev_date("y2023m01d01")  # y2022m12d01

# Generate date ranges
dates = daily.dates_in_range("y2023m01d01", "y2023m01d31")
```

### 3. Serie Class Architecture

The Serie class is the core time series container with TOL-compatible operations.

```python
from tol_python import Serie
import numpy as np

# Creation patterns
data = [100, 102, 105, 103, 107]
serie = Serie(
    data=data,
    first_date="y2023m01d01",
    last_date="y2023m01d05",
    dating=TimeSet("daily"),
    name="Stock_Price"
)
```

#### Data Access Patterns:
```python
# By date (TOL-style)
value = serie["y2023m01d03"]
serie["y2023m01d03"] = 110

# By index (Python-style) 
value = serie[2]
serie[2] = 110

# Slice operations
subset = serie["y2023m01d02":"y2023m01d04"]
subset = serie[1:4]
```

## Development Patterns

### 1. Error Handling

```python
from tol_python.core import TOLDateError, TOLSerieError

def safe_serie_operation(serie, operation):
    try:
        result = operation(serie)
        return result
    except TOLDateError as e:
        print(f"Date error: {e}")
        return None
    except TOLSerieError as e:
        print(f"Serie error: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None
```

### 2. Missing Value Handling

```python
import numpy as np
from tol_python import MISSING_VALUE

# Create series with missing values
data = [1.0, 2.0, np.nan, 4.0, MISSING_VALUE]
serie = Serie(data=data, first_date="y2023m01d01", last_date="y2023m01d05")

# Check for missing values
def has_missing(serie):
    return any(np.isnan(v) or v == MISSING_VALUE for v in serie.values())

# Handle missing values in operations
def safe_mean(serie):
    values = [v for v in serie.values() 
              if not (np.isnan(v) or v == MISSING_VALUE)]
    return np.mean(values) if values else MISSING_VALUE
```

### 3. Performance Optimization

```python
import numpy as np
from numba import jit

# Use NumPy for vectorized operations
def efficient_moving_average(serie, window):
    values = np.array(serie.values())
    # Use pandas rolling or numpy convolve for efficiency
    return np.convolve(values, np.ones(window)/window, mode='valid')

# Use Numba for performance-critical loops
@jit(nopython=True)
def fast_correlation(x, y):
    n = len(x)
    sum_x = np.sum(x)
    sum_y = np.sum(y)
    sum_xy = np.sum(x * y)
    sum_x2 = np.sum(x * x)
    sum_y2 = np.sum(y * y)
    
    numerator = n * sum_xy - sum_x * sum_y
    denominator = np.sqrt((n * sum_x2 - sum_x**2) * (n * sum_y2 - sum_y**2))
    
    return numerator / denominator if denominator != 0 else 0
```

## Testing Framework

### Unit Testing

```python
import unittest
import numpy as np
from tol_python import Serie, Date, TimeSet

class TestSerie(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures"""
        self.data = [1, 2, 3, 4, 5]
        self.serie = Serie(
            data=self.data,
            first_date="y2023m01d01",
            last_date="y2023m01d05"
        )
    
    def test_creation(self):
        """Test serie creation"""
        self.assertEqual(len(self.serie), 5)
        self.assertEqual(self.serie[0], 1)
        self.assertEqual(self.serie[-1], 5)
    
    def test_date_access(self):
        """Test date-based access"""
        self.assertEqual(self.serie["y2023m01d01"], 1)
        self.assertEqual(self.serie["y2023m01d05"], 5)
    
    def test_arithmetic(self):
        """Test arithmetic operations"""
        doubled = self.serie * 2
        self.assertEqual(doubled[0], 2)
        self.assertEqual(doubled[-1], 10)
    
    def test_missing_values(self):
        """Test missing value handling"""
        data_with_nan = [1, 2, np.nan, 4, 5]
        serie_nan = Serie(data=data_with_nan, 
                         first_date="y2023m01d01", 
                         last_date="y2023m01d05")
        
        # Mean should ignore NaN values
        expected_mean = (1 + 2 + 4 + 5) / 4
        self.assertAlmostEqual(serie_nan.mean(), expected_mean)

if __name__ == '__main__':
    unittest.main()
```

### Integration Testing

```python
import unittest
from tol_python import Serie, SerieStatistics, ARIMAModel

class TestIntegration(unittest.TestCase):
    
    def test_arima_workflow(self):
        """Test complete ARIMA modeling workflow"""
        # Generate synthetic data
        np.random.seed(42)
        n = 100
        data = np.cumsum(np.random.randn(n))  # Random walk
        
        serie = Serie(data=data, 
                     first_date="y2020m01d01", 
                     last_date="y2020m04d09")
        
        # Statistical analysis
        stats = SerieStatistics(serie)
        adf_result = stats.stationarity_test()
        
        # Differencing if needed
        if adf_result['p_value'] > 0.05:
            serie_diff = serie.diff()
        else:
            serie_diff = serie
        
        # ARIMA modeling
        model = ARIMAModel(order=(1, 1, 1))
        fitted_model = model.fit(serie_diff)
        
        # Forecasting
        forecast = fitted_model.predict(steps=10)
        
        self.assertEqual(len(forecast), 10)
        self.assertIsNotNone(fitted_model.aic)
```

## Extending TOL Python

### 1. Adding New TimeSet Types

```python
from tol_python.core.dates import TimeSetBase

class BusinessDayTimeSet(TimeSetBase):
    """Business days only (Monday-Friday)"""
    
    def __init__(self, holidays=None):
        super().__init__()
        self.holidays = holidays or []
    
    def contains(self, date):
        """Check if date is a business day"""
        dt = date.to_datetime()
        
        # Check if weekend
        if dt.weekday() >= 5:  # Saturday=5, Sunday=6
            return False
        
        # Check if holiday
        if date.to_string() in self.holidays:
            return False
        
        return True
    
    def next_date(self, date):
        """Get next business day"""
        next_date = date.add_days(1)
        while not self.contains(next_date):
            next_date = next_date.add_days(1)
        return next_date
```

### 2. Adding Statistical Functions

```python
from tol_python.stats.statistics import SerieStatistics

class ExtendedStatistics(SerieStatistics):
    """Extended statistical functions"""
    
    def rolling_sharpe_ratio(self, window=30, risk_free_rate=0.02):
        """Calculate rolling Sharpe ratio"""
        returns = self.serie.diff() / self.serie.lag(1)
        
        rolling_means = []
        rolling_stds = []
        
        for i in range(window-1, len(returns)):
            window_data = returns[i-window+1:i+1]
            rolling_means.append(np.mean(window_data))
            rolling_stds.append(np.std(window_data))
        
        annualized_return = np.array(rolling_means) * 252
        annualized_vol = np.array(rolling_stds) * np.sqrt(252)
        
        sharpe_ratios = (annualized_return - risk_free_rate) / annualized_vol
        
        return Serie(data=sharpe_ratios.tolist(),
                    first_date=returns.dates()[window-1],
                    last_date=returns.dates()[-1])
    
    def maximum_drawdown(self):
        """Calculate maximum drawdown"""
        cumulative = self.serie.cumsum()
        running_max = cumulative.expanding_max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
```

### 3. Adding Custom I/O Formats

```python
from tol_python.io.serialize import SerieIO
import json
import gzip

class CustomSerieIO(SerieIO):
    """Custom I/O formats"""
    
    @staticmethod
    def save_compressed_json(serie, filename):
        """Save serie as compressed JSON"""
        data = {
            'values': serie.values().tolist(),
            'first_date': serie.first_date.to_string(),
            'last_date': serie.last_date.to_string(),
            'dating_type': serie.dating.dating_type,
            'name': serie.name,
            'metadata': {
                'version': '0.2.0',
                'compression': 'gzip'
            }
        }
        
        with gzip.open(filename, 'wt', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
    
    @staticmethod
    def load_compressed_json(filename):
        """Load serie from compressed JSON"""
        with gzip.open(filename, 'rt', encoding='utf-8') as f:
            data = json.load(f)
        
        return Serie(
            data=data['values'],
            first_date=data['first_date'],
            last_date=data['last_date'],
            dating=TimeSet(data['dating_type']),
            name=data.get('name')
        )
```

## Best Practices

### 1. Code Style

Follow PEP 8 with TOL-specific conventions:

```python
# TOL-style naming for compatibility
def sum_s(serie):          # TOL function: SumS
    """Sum of serie values"""
    return serie.sum()

def avr_s(serie):          # TOL function: AvrS  
    """Average of serie values"""
    return serie.mean()

# Python-style for new functionality
def calculate_volatility(serie, window=30):
    """Calculate rolling volatility"""
    returns = serie.pct_change()
    return returns.rolling(window).std()
```

### 2. Documentation

Use comprehensive docstrings:

```python
def lag(self, periods=1):
    """
    Lag the series by specified number of periods.
    
    Parameters
    ----------
    periods : int, default 1
        Number of periods to lag. Positive values lag backwards,
        negative values lead forwards.
    
    Returns
    -------
    Serie
        New Serie object with lagged values.
    
    Examples
    --------
    >>> s = Serie([1, 2, 3, 4], first_date="y2023m01d01", last_date="y2023m01d04")
    >>> s_lag = s.lag(1)
    >>> print(s_lag.values())
    [NaN, 1, 2, 3]
    
    See Also
    --------
    lead : Lead the series forward
    diff : Calculate differences
    """
    pass
```

### 3. Memory Management

```python
# Efficient data handling
def process_large_serie(serie):
    """Process large series efficiently"""
    
    # Use generators for large datasets
    def chunk_generator(data, chunk_size=1000):
        for i in range(0, len(data), chunk_size):
            yield data[i:i + chunk_size]
    
    # Process in chunks
    results = []
    for chunk in chunk_generator(serie.values()):
        chunk_result = np.mean(chunk)  # Example operation
        results.append(chunk_result)
    
    return results

# Memory-efficient operations
def memory_efficient_correlation(serie1, serie2):
    """Calculate correlation without copying data"""
    # Use views instead of copies when possible
    x = serie1.values()  # Returns view, not copy
    y = serie2.values()  # Returns view, not copy
    
    return np.corrcoef(x, y)[0, 1]
```

### 4. Error Recovery

```python
def robust_serie_operation(operation, serie, *args, **kwargs):
    """Robust wrapper for serie operations"""
    
    # Input validation
    if not isinstance(serie, Serie):
        raise TypeError("Expected Serie object")
    
    if len(serie) == 0:
        raise ValueError("Serie is empty")
    
    # Operation with fallback
    try:
        return operation(serie, *args, **kwargs)
    except (ValueError, TypeError) as e:
        # Try alternative approach
        print(f"Primary operation failed: {e}")
        return fallback_operation(serie, *args, **kwargs)
    except Exception as e:
        # Log error and return safe default
        print(f"Unexpected error: {e}")
        return None

def fallback_operation(serie, *args, **kwargs):
    """Fallback implementation"""
    # Simplified but robust alternative
    return serie.mean()  # Safe default
```

## Contributing Guidelines

### 1. Code Review Process

```bash
# Create feature branch
git checkout -b feature/new-statistical-function

# Make changes with tests
# Run test suite
python -m pytest tests/

# Run linting
flake8 tol_python/
mypy tol_python/

# Submit pull request
```

### 2. Documentation Updates

- Update API_REFERENCE.md for new public methods
- Add examples to DEVELOPER_GUIDE.md
- Update README.md if core functionality changes
- Add docstrings following NumPy style

### 3. Performance Benchmarks

```python
import time
import numpy as np
from tol_python import Serie

def benchmark_operation(operation, serie, iterations=1000):
    """Benchmark serie operation"""
    start_time = time.time()
    
    for _ in range(iterations):
        result = operation(serie)
    
    end_time = time.time()
    avg_time = (end_time - start_time) / iterations
    
    return avg_time

# Example benchmark
large_serie = Serie(data=np.random.randn(10000), 
                   first_date="y2020m01d01", 
                   last_date="y2047m05d18")

mean_time = benchmark_operation(lambda s: s.mean(), large_serie)
print(f"Mean calculation: {mean_time:.6f} seconds")
```

This developer guide provides comprehensive information for extending and maintaining the TOL Python library while preserving compatibility with the original TOL language.