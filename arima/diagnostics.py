"""
ARIMA residual diagnostics and model validation
Implements comprehensive diagnostic tests for ARIMA model adequacy
"""

import numpy as np
import numpy.ma as ma
from typing import Dict, Optional, Tuple, List
from scipy import stats
import warnings

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from ..stats.tests import StatisticalTests
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from stats.tests import StatisticalTests


class ARIMADiagnostics:
    """
    Comprehensive diagnostics for ARIMA model residuals
    
    Provides tests for:
    - Serial correlation (Ljung-Box test)
    - Normality (Jarque-Bera, Shapiro-Wilk)
    - Heteroscedasticity (ARCH-LM test)
    - Outlier detection
    - Residual plots data
    """
    
    @staticmethod
    def ljung_box_test(residuals: Serie, lags: int = 10, 
                      fitdf: int = 0) -> Dict[str, float]:
        """
        Ljung-Box test for residual autocorrelation
        
        H0: Residuals are independently distributed (no autocorrelation)
        H1: Residuals exhibit serial correlation
        
        Args:
            residuals: Model residuals
            lags: Number of lags to test
            fitdf: Degrees of freedom used in model fitting (p+q for ARMA)
            
        Returns:
            Dictionary with test statistic, p-value, and critical value
        """
        # Use existing implementation from StatisticalTests
        result = StatisticalTests.box_pierce_ljung_test(residuals, lags)
        
        # Adjust degrees of freedom for ARIMA model
        # df = lags - fitdf (number of AR + MA parameters)
        adjusted_df = max(1, lags - fitdf)
        
        # Recalculate p-value with adjusted df
        test_stat = result['statistic']
        p_value = 1 - stats.chi2.cdf(test_stat, adjusted_df)
        critical_value = stats.chi2.ppf(0.95, adjusted_df)
        
        return {
            'statistic': test_stat,
            'p_value': p_value,
            'critical_value': critical_value,
            'df': adjusted_df,
            'reject_independence': test_stat > critical_value
        }
    
    @staticmethod
    def normality_tests(residuals: Serie) -> Dict[str, Dict]:
        """
        Multiple normality tests for residuals
        
        Returns:
            Dictionary containing results from multiple normality tests
        """
        results = {}
        
        # Get residual data
        data = residuals._data.to_numpy()
        valid_data = data[~np.isnan(data)]
        
        if len(valid_data) < 3:
            return {
                'jarque_bera': {'statistic': np.nan, 'p_value': np.nan},
                'shapiro_wilk': {'statistic': np.nan, 'p_value': np.nan},
                'normal_qq': {'theoretical_quantiles': [], 'sample_quantiles': []}
            }
        
        # Jarque-Bera test (from existing implementation)
        jb_result = StatisticalTests.jarque_bera_test(residuals)
        results['jarque_bera'] = jb_result
        
        # Shapiro-Wilk test (good for smaller samples)
        if len(valid_data) <= 5000:  # Shapiro-Wilk limit
            sw_stat, sw_pval = stats.shapiro(valid_data)
            results['shapiro_wilk'] = {
                'statistic': sw_stat,
                'p_value': sw_pval,
                'reject_normality': sw_pval < 0.05
            }
        else:
            results['shapiro_wilk'] = {
                'statistic': np.nan,
                'p_value': np.nan,
                'reject_normality': None,
                'note': 'Sample too large for Shapiro-Wilk test'
            }
        
        # Q-Q plot data
        results['normal_qq'] = ARIMADiagnostics._qq_plot_data(valid_data)
        
        # Skewness and kurtosis
        results['skewness'] = stats.skew(valid_data)
        results['kurtosis'] = stats.kurtosis(valid_data)
        results['excess_kurtosis'] = stats.kurtosis(valid_data, fisher=True)
        
        return results
    
    @staticmethod
    def _qq_plot_data(data: np.ndarray) -> Dict[str, np.ndarray]:
        """Generate Q-Q plot data for normal distribution"""
        # Sort the data
        sorted_data = np.sort(data)
        n = len(sorted_data)
        
        # Calculate theoretical quantiles
        # Use (i - 0.5) / n for better tail behavior
        theoretical_quantiles = stats.norm.ppf((np.arange(1, n + 1) - 0.5) / n)
        
        return {
            'theoretical_quantiles': theoretical_quantiles,
            'sample_quantiles': sorted_data
        }
    
    @staticmethod
    def arch_lm_test(residuals: Serie, lags: int = 5) -> Dict[str, float]:
        """
        ARCH-LM test for heteroscedasticity (volatility clustering)
        
        H0: No ARCH effects (homoscedasticity)
        H1: ARCH effects present (heteroscedasticity)
        
        Args:
            residuals: Model residuals
            lags: Number of lags for squared residuals
            
        Returns:
            Dictionary with test results
        """
        # Get residual data
        data = residuals._data.to_numpy()
        valid_data = data[~np.isnan(data)]
        
        if len(valid_data) < lags + 10:
            return {
                'statistic': np.nan,
                'p_value': np.nan,
                'critical_value': np.nan,
                'reject_homoscedasticity': None
            }
        
        # Square the residuals
        squared_resid = valid_data ** 2
        
        # Regression of squared residuals on lagged squared residuals
        n = len(squared_resid)
        
        # Create design matrix
        X = np.ones((n - lags, lags + 1))  # Include intercept
        for i in range(lags):
            X[:, i + 1] = squared_resid[lags - i - 1:n - i - 1]
        
        y = squared_resid[lags:]
        
        # OLS regression
        try:
            beta = np.linalg.lstsq(X, y, rcond=None)[0]
            y_pred = X @ beta
            residuals_reg = y - y_pred
            
            # Calculate R-squared
            ss_res = np.sum(residuals_reg ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            # LM statistic = n * R²
            lm_stat = (n - lags) * r_squared
            
            # Chi-square test with 'lags' degrees of freedom
            p_value = 1 - stats.chi2.cdf(lm_stat, lags)
            critical_value = stats.chi2.ppf(0.95, lags)
            
            return {
                'statistic': lm_stat,
                'p_value': p_value,
                'critical_value': critical_value,
                'df': lags,
                'r_squared': r_squared,
                'reject_homoscedasticity': lm_stat > critical_value
            }
            
        except np.linalg.LinAlgError:
            return {
                'statistic': np.nan,
                'p_value': np.nan,
                'critical_value': np.nan,
                'reject_homoscedasticity': None
            }
    
    @staticmethod
    def outlier_detection(residuals: Serie, method: str = 'iqr',
                         threshold: float = 3.0) -> Dict[str, any]:
        """
        Detect outliers in residuals
        
        Args:
            residuals: Model residuals
            method: 'iqr' for interquartile range, 'zscore' for z-score method
            threshold: Threshold for outlier detection (1.5 for IQR, 3.0 for z-score)
            
        Returns:
            Dictionary with outlier information
        """
        # Get residual data
        data = residuals._data.to_numpy()
        valid_idx = ~np.isnan(data)
        valid_data = data[valid_idx]
        
        if len(valid_data) < 4:
            return {
                'outliers': [],
                'outlier_indices': [],
                'n_outliers': 0,
                'pct_outliers': 0.0
            }
        
        outlier_mask = np.zeros(len(valid_data), dtype=bool)
        
        if method == 'iqr':
            # Interquartile range method
            q1 = np.percentile(valid_data, 25)
            q3 = np.percentile(valid_data, 75)
            iqr = q3 - q1
            
            if iqr > 0:
                lower_bound = q1 - threshold * iqr
                upper_bound = q3 + threshold * iqr
                outlier_mask = (valid_data < lower_bound) | (valid_data > upper_bound)
            
            bounds = {'lower': lower_bound, 'upper': upper_bound, 'iqr': iqr}
            
        elif method == 'zscore':
            # Z-score method
            mean = np.mean(valid_data)
            std = np.std(valid_data, ddof=1)
            
            if std > 0:
                z_scores = np.abs((valid_data - mean) / std)
                outlier_mask = z_scores > threshold
            
            bounds = {'mean': mean, 'std': std, 'threshold': threshold}
            
        else:
            raise ValueError(f"Unknown outlier detection method: {method}")
        
        # Get outlier values and original indices
        outlier_values = valid_data[outlier_mask]
        
        # Map back to original indices
        original_indices = np.where(valid_idx)[0]
        outlier_indices = original_indices[outlier_mask]
        
        return {
            'outliers': outlier_values.tolist(),
            'outlier_indices': outlier_indices.tolist(),
            'n_outliers': len(outlier_values),
            'pct_outliers': 100 * len(outlier_values) / len(valid_data),
            'method': method,
            'bounds': bounds
        }
    
    @staticmethod
    def residual_autocorrelation(residuals: Serie, max_lags: int = 20) -> Dict[str, Serie]:
        """
        Compute residual ACF and PACF for diagnostic plots
        
        Args:
            residuals: Model residuals
            max_lags: Maximum lags for ACF/PACF
            
        Returns:
            Dictionary with ACF and PACF series
        """
        from ..stats.statistics import SerieStatistics
        
        # Compute ACF and PACF
        acf = SerieStatistics.autocorrelation(residuals, max_lags)
        pacf = SerieStatistics.partial_autocorrelation(residuals, max_lags)
        
        # Compute confidence bounds (95% CI)
        n = len(residuals._data.to_numpy()[~np.isnan(residuals._data.to_numpy())])
        confidence_bound = 1.96 / np.sqrt(n)
        
        return {
            'acf': acf,
            'pacf': pacf,
            'confidence_bound': confidence_bound,
            'n_obs': n
        }
    
    @staticmethod
    def comprehensive_diagnostics(residuals: Serie, ar_order: int = 0,
                                ma_order: int = 0) -> Dict[str, any]:
        """
        Run all diagnostic tests on ARIMA residuals
        
        Args:
            residuals: Model residuals
            ar_order: Number of AR parameters (for df adjustment)
            ma_order: Number of MA parameters (for df adjustment)
            
        Returns:
            Comprehensive diagnostic results
        """
        diagnostics = {}
        
        # Serial correlation tests
        fitdf = ar_order + ma_order
        diagnostics['ljung_box_10'] = ARIMADiagnostics.ljung_box_test(
            residuals, lags=10, fitdf=fitdf
        )
        diagnostics['ljung_box_20'] = ARIMADiagnostics.ljung_box_test(
            residuals, lags=20, fitdf=fitdf
        )
        
        # Normality tests
        diagnostics['normality'] = ARIMADiagnostics.normality_tests(residuals)
        
        # Heteroscedasticity test
        diagnostics['arch_lm'] = ARIMADiagnostics.arch_lm_test(residuals, lags=5)
        
        # Outlier detection
        diagnostics['outliers_iqr'] = ARIMADiagnostics.outlier_detection(
            residuals, method='iqr', threshold=1.5
        )
        diagnostics['outliers_zscore'] = ARIMADiagnostics.outlier_detection(
            residuals, method='zscore', threshold=3.0
        )
        
        # Residual ACF/PACF
        diagnostics['residual_correlation'] = ARIMADiagnostics.residual_autocorrelation(
            residuals, max_lags=20
        )
        
        # Summary statistics
        data = residuals._data.to_numpy()
        valid_data = data[~np.isnan(data)]
        
        if len(valid_data) > 0:
            diagnostics['summary'] = {
                'mean': np.mean(valid_data),
                'std': np.std(valid_data, ddof=1),
                'min': np.min(valid_data),
                'max': np.max(valid_data),
                'n_obs': len(valid_data)
            }
        else:
            diagnostics['summary'] = {
                'mean': np.nan,
                'std': np.nan,
                'min': np.nan,
                'max': np.nan,
                'n_obs': 0
            }
        
        # Overall model adequacy
        diagnostics['model_adequate'] = ARIMADiagnostics._assess_model_adequacy(diagnostics)
        
        return diagnostics
    
    @staticmethod
    def _assess_model_adequacy(diagnostics: Dict) -> Dict[str, bool]:
        """
        Assess overall model adequacy based on diagnostic tests
        
        Returns:
            Dictionary with pass/fail for each criterion
        """
        adequacy = {
            'no_serial_correlation': diagnostics['ljung_box_10']['p_value'] > 0.05,
            'residuals_normal': diagnostics['normality']['jarque_bera']['p_value'] > 0.05,
            'homoscedastic': not diagnostics['arch_lm']['reject_homoscedasticity'],
            'few_outliers': diagnostics['outliers_zscore']['pct_outliers'] < 5.0
        }
        
        # Overall assessment
        adequacy['overall'] = all([
            adequacy['no_serial_correlation'],
            adequacy['residuals_normal'] or diagnostics['normality']['jarque_bera']['p_value'] > 0.01,  # Be lenient
            adequacy['homoscedastic'] or diagnostics['arch_lm']['p_value'] > 0.01,  # Be lenient
            adequacy['few_outliers']
        ])
        
        return adequacy
    
    @staticmethod
    def diagnostic_summary(diagnostics: Dict) -> str:
        """
        Generate a text summary of diagnostic results
        
        Args:
            diagnostics: Results from comprehensive_diagnostics
            
        Returns:
            Formatted summary string
        """
        lines = []
        lines.append("ARIMA Model Diagnostics")
        lines.append("=" * 50)
        
        # Serial correlation
        lines.append("\n1. Serial Correlation Tests:")
        lb10 = diagnostics['ljung_box_10']
        lines.append(f"   Ljung-Box (10 lags): χ² = {lb10['statistic']:.2f}, p = {lb10['p_value']:.4f}")
        lines.append(f"   No serial correlation: {'✓' if lb10['p_value'] > 0.05 else '✗'}")
        
        # Normality
        lines.append("\n2. Normality Tests:")
        norm = diagnostics['normality']
        lines.append(f"   Jarque-Bera: JB = {norm['jarque_bera']['statistic']:.2f}, p = {norm['jarque_bera']['p_value']:.4f}")
        lines.append(f"   Skewness: {norm['skewness']:.3f}")
        lines.append(f"   Excess kurtosis: {norm['excess_kurtosis']:.3f}")
        lines.append(f"   Residuals normal: {'✓' if norm['jarque_bera']['p_value'] > 0.05 else '✗'}")
        
        # Heteroscedasticity
        lines.append("\n3. Heteroscedasticity Test:")
        arch = diagnostics['arch_lm']
        lines.append(f"   ARCH-LM (5 lags): LM = {arch['statistic']:.2f}, p = {arch['p_value']:.4f}")
        lines.append(f"   Homoscedastic: {'✓' if not arch['reject_homoscedasticity'] else '✗'}")
        
        # Outliers
        lines.append("\n4. Outlier Detection:")
        out_z = diagnostics['outliers_zscore']
        lines.append(f"   Z-score method (|z| > 3): {out_z['n_outliers']} outliers ({out_z['pct_outliers']:.1f}%)")
        lines.append(f"   Few outliers: {'✓' if out_z['pct_outliers'] < 5.0 else '✗'}")
        
        # Overall assessment
        lines.append("\n5. Overall Model Adequacy:")
        adequacy = diagnostics['model_adequate']
        for criterion, passed in adequacy.items():
            if criterion != 'overall':
                lines.append(f"   {criterion.replace('_', ' ').title()}: {'✓' if passed else '✗'}")
        
        lines.append(f"\n   Overall: {'Model appears adequate ✓' if adequacy['overall'] else 'Model may need improvement ✗'}")
        
        return "\n".join(lines)