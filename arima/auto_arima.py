"""
Automatic ARIMA model selection
Implements auto.arima algorithm for optimal (p,d,q) selection
"""

import numpy as np
from typing import List, Tuple, Optional, Dict, Union
import warnings
from itertools import product

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from ..stats.tests import StatisticalTests
    from .arima_factor import ARIMAFactor
    from .arima_model import ARIMA
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from stats.tests import StatisticalTests
    from arima.arima_factor import ARIMAFactor
    from arima.arima_model import ARIMA


class AutoARIMA:
    """
    Automatic ARIMA model selection
    
    Implements a stepwise algorithm similar to R's forecast::auto.arima
    for finding the optimal ARIMA(p,d,q) model based on information criteria.
    """
    
    def __init__(self, max_p: int = 5, max_d: int = 2, max_q: int = 5,
                 max_P: int = 2, max_D: int = 1, max_Q: int = 2,
                 seasonal: bool = False, season_length: int = 1,
                 ic: str = 'aic', stepwise: bool = True,
                 include_mean: bool = True, include_drift: bool = False,
                 test: str = 'adf', alpha: float = 0.05,
                 max_iter: int = 1000, method: str = 'css',
                 trace: bool = False):
        """
        Initialize AutoARIMA
        
        Args:
            max_p: Maximum AR order
            max_d: Maximum differencing order
            max_q: Maximum MA order
            max_P: Maximum seasonal AR order
            max_D: Maximum seasonal differencing order
            max_Q: Maximum seasonal MA order
            seasonal: Whether to consider seasonal models
            season_length: Seasonal period (e.g., 12 for monthly)
            ic: Information criterion ('aic', 'bic', 'hqc')
            stepwise: Use stepwise algorithm (faster) vs exhaustive search
            include_mean: Include mean/intercept term
            include_drift: Include drift term for non-stationary models
            test: Unit root test ('adf' for Augmented Dickey-Fuller)
            alpha: Significance level for tests
            max_iter: Maximum iterations for estimation
            method: Estimation method ('css', 'mle')
            trace: Print progress during search
        """
        self.max_p = max_p
        self.max_d = max_d
        self.max_q = max_q
        self.max_P = max_P if seasonal else 0
        self.max_D = max_D if seasonal else 0
        self.max_Q = max_Q if seasonal else 0
        self.seasonal = seasonal
        self.season_length = season_length
        self.ic = ic.lower()
        self.stepwise = stepwise
        self.include_mean = include_mean
        self.include_drift = include_drift
        self.test = test
        self.alpha = alpha
        self.max_iter = max_iter
        self.method = method
        self.trace = trace
        
        # Results storage
        self.best_model = None
        self.best_factor = None
        self.best_ic = np.inf
        self.all_models = []
    
    def fit(self, serie: Serie, trace: bool = False) -> ARIMA:
        """
        Find optimal ARIMA model for the time series
        
        Args:
            serie: Time series to fit
            trace: Print progress information
            
        Returns:
            Fitted ARIMA model with optimal parameters
        """
        # Use instance trace or parameter trace
        trace_enabled = trace or self.trace
        
        # Step 1: Determine differencing order
        d, D = self._determine_differencing(serie, trace_enabled)
        
        if trace_enabled:
            print(f"Selected differencing: d={d}, D={D}")
        
        # Step 2: Search for optimal (p,q) and (P,Q)
        if self.stepwise:
            best_order = self._stepwise_search(serie, d, D, trace_enabled)
        else:
            best_order = self._exhaustive_search(serie, d, D, trace_enabled)
        
        # Step 3: Fit final model
        p, d_final, q = best_order[:3]
        
        if self.seasonal and len(best_order) == 6:
            P, D_final, Q = best_order[3:6]
            seasonal_order = (P, D_final, Q, self.season_length)
        else:
            seasonal_order = None
        
        # Determine mean/drift
        include_mean = self.include_mean and d_final == 0 and (not self.seasonal or D_final == 0)
        include_drift = self.include_drift and (d_final > 0 or (self.seasonal and D_final > 0))
        
        # Create final factor
        self.best_factor = ARIMAFactor.from_order(
            (p, d_final, q),
            seasonal_order=seasonal_order,
            include_mean=include_mean,
            include_drift=include_drift
        )
        
        # Fit final model
        self.best_model = ARIMA(self.best_factor)
        results = self.best_model.fit(serie, method=self.method, maxiter=self.max_iter)
        
        if trace_enabled:
            print(f"\nBest model: {self.best_factor.get_model_string()}")
            print(f"Final {self.ic.upper()}: {getattr(results, self.ic):.2f}")
        
        return self.best_model
    
    def _determine_differencing(self, serie: Serie, trace: bool) -> Tuple[int, int]:
        """
        Determine optimal differencing orders using unit root tests
        
        Returns:
            Tuple of (d, D) differencing orders
        """
        # Regular differencing
        d = 0
        current_series = serie
        
        while d < self.max_d:
            # Apply unit root test
            if self.test == 'adf':
                test_result = StatisticalTests.augmented_dickey_fuller_test(current_series, regression='c')
                p_value = test_result['p_value']
            else:
                # Default to ADF
                test_result = StatisticalTests.augmented_dickey_fuller_test(current_series, regression='c')
                p_value = test_result['p_value']
            
            if trace:
                print(f"Unit root test at d={d}: p-value = {p_value:.4f}")
            
            if p_value < self.alpha:
                # Series is stationary
                break
            
            # Difference the series
            d += 1
            if d <= self.max_d:
                current_series = current_series.diff()
        
        # Seasonal differencing
        D = 0
        if self.seasonal and self.season_length > 1:
            while D < self.max_D:
                # Apply seasonal difference
                if D > 0:
                    current_series = current_series - current_series.lag(self.season_length)
                
                # Test for seasonal unit root (simplified)
                # In practice, would use HEGY or CH test
                test_result = StatisticalTests.augmented_dickey_fuller_test(current_series, regression='c')
                p_value = test_result['p_value']
                
                if trace:
                    print(f"Seasonal unit root test at D={D}: p-value = {p_value:.4f}")
                
                if p_value < self.alpha:
                    break
                
                D += 1
        
        return d, D
    
    def _stepwise_search(self, serie: Serie, d: int, D: int, trace: bool) -> Tuple:
        """
        Stepwise search algorithm for optimal (p,q) and (P,Q)
        
        Based on Hyndman-Khandakar algorithm
        """
        # Start with simple models
        initial_models = [
            (0, d, 0),  # White noise after differencing
            (1, d, 0),  # AR(1)
            (0, d, 1),  # MA(1)
            (1, d, 1),  # ARMA(1,1)
        ]
        
        if self.seasonal:
            # Add seasonal variants
            seasonal_models = []
            for p, _, q in initial_models[:4]:
                seasonal_models.extend([
                    (p, d, q, 1, D, 0),  # Seasonal AR
                    (p, d, q, 0, D, 1),  # Seasonal MA
                    (p, d, q, 1, D, 1),  # Seasonal ARMA
                ])
            initial_models = [(p, d, q, 0, D, 0) for p, _, q in initial_models]
            initial_models.extend(seasonal_models)
        
        # Evaluate initial models
        best_order = None
        best_ic_value = np.inf
        
        for order in initial_models:
            ic_value = self._evaluate_model(serie, order, trace)
            
            if ic_value < best_ic_value:
                best_ic_value = ic_value
                best_order = order
        
        if trace:
            print(f"Initial best: {self._order_to_string(best_order)} with {self.ic.upper()}={best_ic_value:.2f}")
        
        # Stepwise improvement
        improved = True
        while improved:
            improved = False
            neighbors = self._get_neighbors(best_order)
            
            for neighbor in neighbors:
                ic_value = self._evaluate_model(serie, neighbor, trace)
                
                if ic_value < best_ic_value - 0.01:  # Small improvement threshold
                    best_ic_value = ic_value
                    best_order = neighbor
                    improved = True
                    
                    if trace:
                        print(f"Improved: {self._order_to_string(best_order)} with {self.ic.upper()}={best_ic_value:.2f}")
        
        self.best_ic = best_ic_value
        return best_order
    
    def _exhaustive_search(self, serie: Serie, d: int, D: int, trace: bool) -> Tuple:
        """
        Exhaustive grid search over all possible models
        """
        best_order = None
        best_ic_value = np.inf
        
        # Generate all combinations
        if self.seasonal:
            param_grid = product(
                range(self.max_p + 1),      # p
                [d],                         # d (fixed)
                range(self.max_q + 1),      # q
                range(self.max_P + 1),      # P
                [D],                         # D (fixed)
                range(self.max_Q + 1),      # Q
            )
        else:
            param_grid = product(
                range(self.max_p + 1),      # p
                [d],                         # d (fixed)
                range(self.max_q + 1),      # q
            )
        
        # Evaluate each model
        for order in param_grid:
            # Skip models that are too complex
            if self.seasonal:
                if order[0] + order[3] > 6 or order[2] + order[5] > 6:
                    continue
            else:
                if order[0] + order[2] > 6:
                    continue
            
            ic_value = self._evaluate_model(serie, order, trace)
            
            if ic_value < best_ic_value:
                best_ic_value = ic_value
                best_order = order
        
        self.best_ic = best_ic_value
        return best_order
    
    def _evaluate_model(self, serie: Serie, order: Tuple, trace: bool) -> float:
        """
        Evaluate a specific ARIMA model and return IC value
        """
        try:
            # Create factor
            if self.seasonal and len(order) == 6:
                p, d, q, P, D, Q = order
                seasonal_order = (P, D, Q, self.season_length)
            else:
                p, d, q = order
                seasonal_order = None
            
            # Check if model has been evaluated before
            model_key = order
            for evaluated_model in self.all_models:
                if evaluated_model['order'] == model_key:
                    return evaluated_model[self.ic]
            
            # Determine mean/drift
            include_mean = self.include_mean and d == 0 and (not self.seasonal or (len(order) == 6 and order[4] == 0))
            include_drift = self.include_drift and (d > 0 or (self.seasonal and len(order) == 6 and order[4] > 0))
            
            factor = ARIMAFactor.from_order(
                (p, d, q),
                seasonal_order=seasonal_order,
                include_mean=include_mean,
                include_drift=include_drift
            )
            
            # Fit model
            model = ARIMA(factor)
            results = model.fit(serie, method=self.method, maxiter=self.max_iter)
            
            # Get IC value
            ic_value = getattr(results, self.ic)
            
            # Store results
            self.all_models.append({
                'order': model_key,
                'aic': results.aic,
                'bic': results.bic,
                'hqc': results.hqc,
                'converged': results.converged
            })
            
            if trace and results.converged:
                print(f"  {self._order_to_string(order)}: {self.ic.upper()}={ic_value:.2f}")
            
            return ic_value if np.isfinite(ic_value) else np.inf
            
        except Exception as e:
            if trace:
                print(f"  {self._order_to_string(order)}: Failed - {str(e)}")
            return np.inf
    
    def _get_neighbors(self, order: Tuple) -> List[Tuple]:
        """Get neighboring models for stepwise search"""
        neighbors = []
        
        if self.seasonal and len(order) == 6:
            p, d, q, P, D, Q = order
            
            # Vary p
            if p > 0:
                neighbors.append((p-1, d, q, P, D, Q))
            if p < self.max_p:
                neighbors.append((p+1, d, q, P, D, Q))
            
            # Vary q
            if q > 0:
                neighbors.append((p, d, q-1, P, D, Q))
            if q < self.max_q:
                neighbors.append((p, d, q+1, P, D, Q))
            
            # Vary P
            if P > 0:
                neighbors.append((p, d, q, P-1, D, Q))
            if P < self.max_P:
                neighbors.append((p, d, q, P+1, D, Q))
            
            # Vary Q
            if Q > 0:
                neighbors.append((p, d, q, P, D, Q-1))
            if Q < self.max_Q:
                neighbors.append((p, d, q, P, D, Q+1))
        else:
            p, d, q = order
            
            # Vary p
            if p > 0:
                neighbors.append((p-1, d, q))
            if p < self.max_p:
                neighbors.append((p+1, d, q))
            
            # Vary q
            if q > 0:
                neighbors.append((p, d, q-1))
            if q < self.max_q:
                neighbors.append((p, d, q+1))
        
        return neighbors
    
    def _order_to_string(self, order: Tuple) -> str:
        """Convert order tuple to string representation"""
        if self.seasonal and len(order) == 6:
            p, d, q, P, D, Q = order
            return f"ARIMA({p},{d},{q})({P},{D},{Q})[{self.season_length}]"
        else:
            p, d, q = order
            return f"ARIMA({p},{d},{q})"
    
    def summary(self) -> str:
        """Generate summary of model selection results"""
        if self.best_model is None:
            return "No model has been fitted yet."
        
        lines = []
        lines.append("AutoARIMA Model Selection Results")
        lines.append("=" * 50)
        lines.append(f"Best model: {self.best_factor.get_model_string()}")
        lines.append(f"Selection criterion: {self.ic.upper()}")
        lines.append(f"Final {self.ic.upper()}: {self.best_ic:.2f}")
        lines.append(f"Search method: {'Stepwise' if self.stepwise else 'Exhaustive'}")
        lines.append(f"Models evaluated: {len(self.all_models)}")
        
        # Top 5 models
        if len(self.all_models) > 1:
            lines.append("\nTop 5 models by " + self.ic.upper() + ":")
            sorted_models = sorted(self.all_models, key=lambda x: x[self.ic])[:5]
            
            for i, model in enumerate(sorted_models):
                order_str = self._order_to_string(model['order'])
                ic_val = model[self.ic]
                lines.append(f"  {i+1}. {order_str}: {ic_val:.2f}")
        
        return "\n".join(lines)