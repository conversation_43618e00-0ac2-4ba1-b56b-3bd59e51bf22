"""
ARIMA parameter estimation methods
Implements TOL's BARIMA estimation algorithms including <PERSON><PERSON><PERSON><PERSON> and CSS
"""

import numpy as np
import numpy.ma as ma
from typing import Optional, Dict, Any, Tuple, List
from scipy import optimize
from scipy import linalg
import warnings

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from ..core import Date, TimeSet
    from ..stats.statistics import SerieStatistics
    from .arima_factor import ARIMAFactor, ARIMAParameterBounds
    from .arima_model import ARIMAResults
    from .kalman import ARIMAKalmanFilter
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from core.dates import Date, TimeSet
    from stats.statistics import SerieStatistics
    from arima.arima_factor import ARIMAFactor, ARIMAParameterBounds
    from arima.arima_model import ARIMAResults
    from arima.kalman import ARIMAKalmanFilter


class ARIMAEstimator:
    """
    ARIMA parameter estimation class
    
    Provides multiple estimation methods for ARIMA models:
    - Yule-Walker estimation for AR parameters
    - Conditional Sum of Squares (CSS) for ARMA models
    - Maximum Likelihood Estimation (MLE) via <PERSON><PERSON> filter
    """
    
    def __init__(self, factor: ARIMAFactor):
        """
        Initialize ARIMA estimator
        
        Args:
            factor: ARIMAFactor specifying the model structure
        """
        self.factor = factor
        self.bounds = ARIMAParameterBounds(factor)
    
    def fit(self, serie: Serie, method: str = "css",
            start_params: Optional[np.ndarray] = None,
            maxiter: int = 1000, tolerance: float = 1e-6) -> ARIMAResults:
        """
        Fit ARIMA model using specified estimation method
        
        Args:
            serie: Time series to fit
            method: Estimation method ("yule_walker", "css", "mle")
            start_params: Initial parameter values
            maxiter: Maximum iterations for optimization
            tolerance: Convergence tolerance
            
        Returns:
            ARIMAResults object with estimation results
        """
        # Prepare data
        original_series = serie.copy()
        differenced_series = self._apply_differencing(serie)
        
        # Validate data length after differencing
        diff_data = differenced_series._data.to_numpy()
        valid_data = diff_data[~np.isnan(diff_data)]
        
        if len(valid_data) < self.factor.required_observations():
            raise ValueError(f"Insufficient data after differencing: need {self.factor.required_observations()}, "
                           f"got {len(valid_data)}")
        
        # Choose estimation method
        if method.lower() == "yule_walker":
            results = self._estimate_yule_walker(differenced_series)
        elif method.lower() == "css":
            results = self._estimate_css(differenced_series, start_params, maxiter, tolerance)
        elif method.lower() == "mle":
            results = self._estimate_mle(differenced_series, start_params, maxiter, tolerance)
        else:
            raise ValueError(f"Unknown estimation method: {method}")
        
        # Store series information
        results.original_series = original_series
        results.differenced_series = differenced_series
        results.method = method
        results.n_obs = len(valid_data)
        results.n_params = self.factor.num_params
        
        # Generate fitted values and residuals
        results.fitted_values, results.residuals = self._compute_fitted_values(results)
        
        return results
    
    def _apply_differencing(self, serie: Serie) -> Serie:
        """Apply differencing operations specified by the factor"""
        result = serie.copy()
        
        # Apply regular differencing
        for _ in range(self.factor.diff_order):
            result = result.diff()
        
        # Apply seasonal differencing
        for _ in range(self.factor.seasonal_diff):
            # Seasonal difference with lag = season_length
            lag = self.factor.season_length
            result = result - result.lag(lag)
        
        return result
    
    def _estimate_yule_walker(self, differenced_series: Serie) -> ARIMAResults:
        """
        Estimate AR parameters using Yule-Walker equations
        
        Note: This method only works for pure AR models (MA order = 0)
        """
        if self.factor.ma_order > 0 or self.factor.seasonal_ma > 0:
            raise ValueError("Yule-Walker estimation only applicable to pure AR models")
        
        # Get data
        data = differenced_series._data.to_numpy()
        valid_data = data[~np.isnan(data)]
        
        if len(valid_data) == 0:
            raise ValueError("No valid data after differencing")
        
        # Total AR order
        total_ar_order = self.factor.num_ar_params
        
        if total_ar_order == 0:
            # No AR terms, just estimate intercept
            ar_params = np.array([])
            intercept = np.mean(valid_data) if self.factor.include_mean else None
            sigma2 = np.var(valid_data, ddof=1)
        else:
            # Estimate autocorrelations
            acf_serie = SerieStatistics.autocorrelation(differenced_series, max_lags=total_ar_order)
            acf_data = acf_serie._data.to_numpy()
            acf_values = acf_data[~np.isnan(acf_data)]
            
            # Build Yule-Walker system
            # R * phi = r, where R is Toeplitz matrix of autocorrelations
            R = np.zeros((total_ar_order, total_ar_order))
            r = np.zeros(total_ar_order)
            
            # Get AR lags
            ar_lags, _ = self.factor.get_polynomial_orders()
            
            # Fill autocorrelation matrix and vector
            for i in range(total_ar_order):
                lag_idx = ar_lags[i] - 1  # Convert to 0-based index (lag 1 -> index 0)
                r[i] = acf_values[lag_idx] if lag_idx < len(acf_values) else 0.0
                
                for j in range(total_ar_order):
                    lag_diff = abs(ar_lags[i] - ar_lags[j])
                    if lag_diff == 0:
                        R[i, j] = 1.0  # Autocorrelation at lag 0
                    elif lag_diff - 1 < len(acf_values):  # Convert to 0-based index
                        R[i, j] = acf_values[lag_diff - 1]
                    else:
                        R[i, j] = 0.0
            
            # Solve Yule-Walker equations
            try:
                ar_params = linalg.solve(R, r)
            except linalg.LinAlgError:
                # Fallback to least squares if matrix is singular
                ar_params = linalg.lstsq(R, r)[0]
            
            # Check stationarity
            if not self.bounds.check_stationarity(ar_params):
                warnings.warn("Estimated AR parameters may not satisfy stationarity conditions")
            
            # Estimate intercept
            if self.factor.include_mean:
                ar_sum = np.sum(ar_params)
                if abs(ar_sum) < 1.0:
                    intercept = np.mean(valid_data) * (1 - ar_sum)
                else:
                    intercept = 0.0
            else:
                intercept = None
            
            # Estimate innovation variance
            # σ² = γ₀(1 - φ₁ρ₁ - φ₂ρ₂ - ... - φₚρₚ)
            gamma0 = np.var(valid_data, ddof=1)
            innovation_var = gamma0 * (1 - np.dot(ar_params, r))
            sigma2 = max(innovation_var, 1e-10)  # Ensure positive variance
        
        # Create results
        results = ARIMAResults(
            factor=self.factor,
            ar_params=ar_params,
            ma_params=np.array([]),  # No MA parameters in Yule-Walker
            intercept=intercept,
            sigma2=sigma2,
            converged=True,
            n_iter=1,
            n_obs=len(valid_data),
            n_params=self.factor.num_params
        )
        
        return results
    
    def _estimate_css(self, differenced_series: Serie, start_params: Optional[np.ndarray],
                     maxiter: int, tolerance: float) -> ARIMAResults:
        """
        Estimate parameters using Conditional Sum of Squares
        """
        # Get data
        data = differenced_series._data.to_numpy()
        valid_data = data[~np.isnan(data)]
        
        if len(valid_data) == 0:
            raise ValueError("No valid data after differencing")
        
        # Initialize parameters
        if start_params is None:
            start_params = self._get_initial_parameters(differenced_series)
        
        # Parameter bounds
        bounds = self.bounds.get_all_bounds()
        
        # Define objective function (sum of squared residuals)
        def objective_function(params):
            try:
                ssr = self._compute_css_objective(params, valid_data)
                return ssr
            except Exception:
                return np.inf
        
        # Optimize
        try:
            result = optimize.minimize(
                objective_function,
                start_params,
                method='L-BFGS-B',
                bounds=bounds,
                options={'maxiter': maxiter, 'ftol': tolerance}
            )
            
            converged = result.success
            n_iter = result.nit
            optimal_params = result.x
            final_ssr = result.fun
            
        except Exception as e:
            warnings.warn(f"Optimization failed: {e}")
            converged = False
            n_iter = 0
            optimal_params = start_params
            final_ssr = np.inf
        
        # Extract parameters
        ar_params, ma_params, intercept = self._unpack_parameters(optimal_params)
        
        # Estimate residual variance
        n_params = len(optimal_params)
        degrees_freedom = len(valid_data) - n_params
        if degrees_freedom > 0 and np.isfinite(final_ssr):
            sigma2 = final_ssr / degrees_freedom
        else:
            sigma2 = np.var(valid_data, ddof=1)
        
        # Create results
        results = ARIMAResults(
            factor=self.factor,
            ar_params=ar_params,
            ma_params=ma_params,
            intercept=intercept,
            sigma2=sigma2,
            converged=converged,
            n_iter=n_iter,
            n_obs=len(valid_data),
            n_params=self.factor.num_params
        )
        
        return results
    
    def _compute_css_objective(self, params: np.ndarray, data: np.ndarray) -> float:
        """
        Compute Conditional Sum of Squares objective function
        """
        ar_params, ma_params, intercept = self._unpack_parameters(params)
        
        # Get lag structures
        ar_lags, ma_lags = self.factor.get_polynomial_orders()
        
        max_lag = max([0] + ar_lags + ma_lags)
        n = len(data)
        
        if n <= max_lag:
            return np.inf
        
        # Initialize residuals
        residuals = np.zeros(n)
        
        # Compute residuals recursively
        for t in range(max_lag, n):
            fitted_value = 0.0
            
            # Add intercept
            if intercept is not None:
                fitted_value += intercept
            
            # AR component
            for i, lag in enumerate(ar_lags):
                if t - lag >= 0:
                    fitted_value += ar_params[i] * data[t - lag]
            
            # MA component (use past residuals)
            for i, lag in enumerate(ma_lags):
                if t - lag >= 0:
                    fitted_value += ma_params[i] * residuals[t - lag]
            
            residuals[t] = data[t] - fitted_value
        
        # Sum of squared residuals (conditional on initial values)
        ssr = np.sum(residuals[max_lag:] ** 2)
        
        return ssr
    
    def _estimate_mle(self, differenced_series: Serie, start_params: Optional[np.ndarray],
                     maxiter: int, tolerance: float) -> ARIMAResults:
        """
        Maximum Likelihood Estimation using Kalman filter
        
        Provides exact likelihood computation via state space representation
        and Kalman filter recursions.
        """
        # Get data
        data = differenced_series._data.to_numpy()
        valid_data = data[~np.isnan(data)]
        
        if len(valid_data) == 0:
            raise ValueError("No valid data after differencing")
        
        # Initialize parameters
        if start_params is None:
            start_params = self._get_initial_parameters(differenced_series)
        
        # Parameter bounds
        bounds = self.bounds.get_all_bounds()
        
        # Create Kalman filter
        kalman = ARIMAKalmanFilter(self.factor)
        
        # Define negative log-likelihood objective
        def neg_log_likelihood(params):
            try:
                # Extract sigma2 (last parameter, use exp to ensure positivity)
                sigma2 = np.exp(params[-1])
                
                # Unpack other parameters (excluding log(sigma2))
                ar_params, ma_params, intercept = self._unpack_parameters(params[:-1])
                
                # Compute likelihood
                ll, _ = kalman.compute_likelihood(valid_data, ar_params, ma_params, sigma2, intercept)
                
                return -ll if np.isfinite(ll) else 1e10
                
            except Exception:
                return 1e10
        
        # Add bounds for log(sigma2) 
        bounds_adj = bounds + [(-10, 10)]  # log(sigma2) bounds
        
        # Add log(sigma2) to initial parameters
        start_params_adj = np.append(start_params, np.log(1.0))  # log(sigma2) = 0 -> sigma2 = 1
        
        # Optimize
        try:
            result = optimize.minimize(
                neg_log_likelihood,
                start_params_adj,
                method='L-BFGS-B',
                bounds=bounds_adj,
                options={'maxiter': maxiter, 'ftol': tolerance}
            )
            
            converged = result.success
            n_iter = result.nit
            optimal_params = result.x
            final_nll = result.fun
            
        except Exception as e:
            warnings.warn(f"MLE optimization failed: {e}")
            converged = False
            n_iter = 0
            optimal_params = start_params_adj
            final_nll = np.inf
        
        # Extract parameters
        sigma2 = np.exp(optimal_params[-1])
        ar_params, ma_params, intercept = self._unpack_parameters(optimal_params[:-1])
        
        # Compute final likelihood and diagnostics
        final_ll, kalman_info = kalman.compute_likelihood(valid_data, ar_params, ma_params, sigma2, intercept)
        
        # Compute standard errors
        standard_errors = kalman.compute_standard_errors(valid_data, ar_params, ma_params, sigma2, intercept)
        
        # Create results
        results = ARIMAResults(
            factor=self.factor,
            ar_params=ar_params,
            ma_params=ma_params,
            intercept=intercept,
            sigma2=sigma2,
            converged=converged,
            n_iter=n_iter,
            loglikelihood=final_ll,
            n_obs=len(valid_data),
            n_params=self.factor.num_params
        )
        
        # Store standard errors and Kalman filter info
        results._standard_errors = standard_errors
        results._kalman_info = kalman_info
        
        return results
    
    def _get_initial_parameters(self, differenced_series: Serie) -> np.ndarray:
        """Get initial parameter values for optimization"""
        params = []
        
        # AR parameter initialization
        if self.factor.num_ar_params > 0:
            # Use Yule-Walker for AR initialization if possible
            try:
                if self.factor.ma_order == 0 and self.factor.seasonal_ma == 0:
                    # Pure AR model - use Yule-Walker
                    temp_factor = ARIMAFactor(
                        ar_order=self.factor.ar_order,
                        seasonal_ar=self.factor.seasonal_ar,
                        season_length=self.factor.season_length,
                        include_mean=False
                    )
                    temp_estimator = ARIMAEstimator(temp_factor)
                    yw_results = temp_estimator._estimate_yule_walker(differenced_series)
                    params.extend(yw_results.ar_params)
                else:
                    # ARMA model - use small random values
                    params.extend(np.random.normal(0, 0.1, self.factor.num_ar_params))
            except:
                # Fallback to small random values
                params.extend(np.random.normal(0, 0.1, self.factor.num_ar_params))
        
        # MA parameter initialization (small random values)
        if self.factor.num_ma_params > 0:
            params.extend(np.random.normal(0, 0.1, self.factor.num_ma_params))
        
        # Intercept initialization
        if self.factor.include_mean or self.factor.include_drift:
            data = differenced_series._data.to_numpy()
            valid_data = data[~np.isnan(data)]
            if len(valid_data) > 0:
                params.append(np.mean(valid_data))
            else:
                params.append(0.0)
        
        return np.array(params)
    
    def _unpack_parameters(self, params: np.ndarray) -> Tuple[np.ndarray, np.ndarray, Optional[float]]:
        """Unpack parameter vector into AR, MA, and intercept components"""
        idx = 0
        
        # AR parameters
        if self.factor.num_ar_params > 0:
            ar_params = params[idx:idx + self.factor.num_ar_params]
            idx += self.factor.num_ar_params
        else:
            ar_params = np.array([])
        
        # MA parameters
        if self.factor.num_ma_params > 0:
            ma_params = params[idx:idx + self.factor.num_ma_params]
            idx += self.factor.num_ma_params
        else:
            ma_params = np.array([])
        
        # Intercept
        if self.factor.include_mean or self.factor.include_drift:
            intercept = params[idx]
        else:
            intercept = None
        
        return ar_params, ma_params, intercept
    
    def _compute_fitted_values(self, results: ARIMAResults) -> Tuple[Serie, Serie]:
        """
        Compute fitted values and residuals for the model
        """
        if results.differenced_series is None:
            return None, None
        
        # Get differenced data
        diff_data = results.differenced_series._data.to_numpy()
        valid_mask = ~np.isnan(diff_data)
        
        if not np.any(valid_mask):
            return None, None
        
        n = len(diff_data)
        fitted_values = np.full(n, np.nan)
        residuals = np.full(n, np.nan)
        
        # Get lag structures
        ar_lags, ma_lags = results.factor.get_polynomial_orders()
        max_lag = max([0] + ar_lags + ma_lags)
        
        # Compute fitted values and residuals
        for t in range(max_lag, n):
            if not valid_mask[t]:
                continue
            
            fitted_val = 0.0
            
            # Add intercept
            if results.intercept is not None:
                fitted_val += results.intercept
            
            # AR component
            for i, lag in enumerate(ar_lags):
                if t - lag >= 0 and valid_mask[t - lag]:
                    fitted_val += results.ar_params[i] * diff_data[t - lag]
            
            # MA component (use computed residuals)
            for i, lag in enumerate(ma_lags):
                if t - lag >= max_lag:  # Only use residuals we've computed
                    fitted_val += results.ma_params[i] * residuals[t - lag]
            
            fitted_values[t] = fitted_val
            residuals[t] = diff_data[t] - fitted_val
        
        # Create Serie objects
        fitted_serie = Serie(data=fitted_values,
                           first_date=results.differenced_series.first_date,
                           last_date=results.differenced_series.last_date,
                           dating=results.differenced_series.dating)
        
        residual_serie = Serie(data=residuals,
                             first_date=results.differenced_series.first_date,
                             last_date=results.differenced_series.last_date,
                             dating=results.differenced_series.dating)
        
        return fitted_serie, residual_serie