"""
Main ARIMA model class
Implements TOL's ARIMA estimation and forecasting capabilities
"""

import numpy as np
import numpy.ma as ma
from typing import Optional, Dict, Any, Tuple, Union, List
import warnings
from dataclasses import dataclass

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from ..core import Date, TimeSet
    from .arima_factor import ARIMAFactor, ARIMAParameterBounds
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from core.dates import Date, TimeSet
    from arima.arima_factor import ARIMAFactor, ARIMAParameterBounds


@dataclass
class ARIMAResults:
    """
    Results from ARIMA model estimation
    
    Contains estimated parameters, diagnostics, and methods for forecasting
    """
    
    # Model specification
    factor: ARIMAFactor
    
    # Estimated parameters
    ar_params: np.ndarray
    ma_params: np.ndarray
    intercept: Optional[float] = None
    sigma2: float = 0.0
    
    # Estimation details
    method: str = "css"
    converged: bool = False
    n_iter: int = 0
    loglikelihood: float = np.nan
    aic: float = np.nan
    bic: float = np.nan
    hqc: float = np.nan
    
    # Data and fitted values
    original_series: Optional[Serie] = None
    differenced_series: Optional[Serie] = None
    fitted_values: Optional[Serie] = None
    residuals: Optional[Serie] = None
    
    # Sample information
    n_obs: int = 0
    n_params: int = 0
    degrees_freedom: int = 0
    
    # Enhanced diagnostics (populated by MLE)
    _standard_errors: Optional[Dict] = None
    _kalman_info: Optional[Dict] = None
    _diagnostics: Optional[Dict] = None
    
    def __post_init__(self):
        """Calculate derived statistics"""
        if self.n_obs > 0 and self.n_params > 0:
            self.degrees_freedom = self.n_obs - self.n_params
            
        # Calculate information criteria if likelihood is available
        if not np.isnan(self.loglikelihood) and self.n_obs > 0:
            self.aic = -2 * self.loglikelihood + 2 * self.n_params
            self.bic = -2 * self.loglikelihood + np.log(self.n_obs) * self.n_params
            self.hqc = -2 * self.loglikelihood + 2 * np.log(np.log(self.n_obs)) * self.n_params
    
    @property
    def all_params(self) -> np.ndarray:
        """Get all parameters as a single array"""
        params = []
        
        if len(self.ar_params) > 0:
            params.extend(self.ar_params)
        
        if len(self.ma_params) > 0:
            params.extend(self.ma_params)
        
        if self.intercept is not None:
            params.append(self.intercept)
        
        return np.array(params)
    
    @property
    def param_names(self) -> List[str]:
        """Get parameter names"""
        names = []
        
        # AR parameter names
        ar_lags, ma_lags = self.factor.get_polynomial_orders()
        
        for i, lag in enumerate(ar_lags):
            if lag <= self.factor.ar_order:
                names.append(f"ar.L{lag}")
            else:
                names.append(f"ar.S{lag//self.factor.season_length}")
        
        # MA parameter names  
        for i, lag in enumerate(ma_lags):
            if lag <= self.factor.ma_order:
                names.append(f"ma.L{lag}")
            else:
                names.append(f"ma.S{lag//self.factor.season_length}")
        
        # Intercept/drift name
        if self.intercept is not None:
            if self.factor.is_stationary:
                names.append("intercept")
            else:
                names.append("drift")
        
        return names
    
    @property
    def standard_errors(self) -> Optional[Dict]:
        """Get parameter standard errors (available with MLE estimation)"""
        return self._standard_errors
    
    @property
    def t_statistics(self) -> Optional[Dict]:
        """Compute t-statistics for parameters"""
        if self._standard_errors is None:
            return None
        
        t_stats = {}
        
        # AR parameter t-statistics
        if len(self.ar_params) > 0 and len(self._standard_errors['ar_se']) > 0:
            t_stats['ar_t'] = self.ar_params / self._standard_errors['ar_se']
        
        # MA parameter t-statistics  
        if len(self.ma_params) > 0 and len(self._standard_errors['ma_se']) > 0:
            t_stats['ma_t'] = self.ma_params / self._standard_errors['ma_se']
        
        # Intercept t-statistic
        if (self.intercept is not None and 
            self._standard_errors['intercept_se'] is not None and
            not np.isnan(self._standard_errors['intercept_se'])):
            t_stats['intercept_t'] = self.intercept / self._standard_errors['intercept_se']
        
        return t_stats
    
    @property 
    def p_values(self) -> Optional[Dict]:
        """Compute p-values for parameters (two-tailed t-test)"""
        t_stats = self.t_statistics
        if t_stats is None:
            return None
        
        from scipy import stats
        
        p_vals = {}
        df = self.degrees_freedom
        
        if df <= 0:
            return None
        
        # AR parameter p-values
        if 'ar_t' in t_stats:
            p_vals['ar_p'] = 2 * (1 - stats.t.cdf(np.abs(t_stats['ar_t']), df))
        
        # MA parameter p-values
        if 'ma_t' in t_stats:
            p_vals['ma_p'] = 2 * (1 - stats.t.cdf(np.abs(t_stats['ma_t']), df))
        
        # Intercept p-value
        if 'intercept_t' in t_stats:
            p_vals['intercept_p'] = 2 * (1 - stats.t.cdf(np.abs(t_stats['intercept_t']), df))
        
        return p_vals
    
    def summary(self) -> str:
        """Generate a summary string of the estimation results"""
        lines = []
        
        # Model specification
        lines.append(f"ARIMA Model Results")
        lines.append("=" * 50)
        lines.append(f"Model: {self.factor.get_model_string()}")
        lines.append(f"Estimation method: {self.method.upper()}")
        lines.append(f"Sample size: {self.n_obs}")
        lines.append(f"Parameters: {self.n_params}")
        lines.append(f"Degrees of freedom: {self.degrees_freedom}")
        lines.append("")
        
        # Convergence
        lines.append(f"Converged: {self.converged}")
        if self.n_iter > 0:
            lines.append(f"Iterations: {self.n_iter}")
        lines.append("")
        
        # Information criteria
        if not np.isnan(self.aic):
            lines.append("Information Criteria:")
            lines.append(f"  AIC: {self.aic:.4f}")
            lines.append(f"  BIC: {self.bic:.4f}")
            lines.append(f"  HQC: {self.hqc:.4f}")
            lines.append("")
        
        # Parameter estimates
        if len(self.all_params) > 0:
            lines.append("Parameter Estimates:")
            param_names = self.param_names
            all_params = self.all_params
            
            # Check if we have standard errors (MLE estimation)
            if self._standard_errors is not None:
                t_stats = self.t_statistics
                p_vals = self.p_values
                
                lines.append(f"{'Parameter':>12s} {'Estimate':>10s} {'Std.Error':>10s} {'t-stat':>8s} {'p-value':>8s}")
                lines.append("-" * 60)
                
                # AR parameters
                for i, name in enumerate([n for n in param_names if n.startswith('ar.')]):
                    param = self.ar_params[i]
                    se = self._standard_errors['ar_se'][i] if i < len(self._standard_errors['ar_se']) else np.nan
                    t_val = t_stats['ar_t'][i] if t_stats and 'ar_t' in t_stats and i < len(t_stats['ar_t']) else np.nan
                    p_val = p_vals['ar_p'][i] if p_vals and 'ar_p' in p_vals and i < len(p_vals['ar_p']) else np.nan
                    
                    lines.append(f"{name:>12s} {param:10.4f} {se:10.4f} {t_val:8.3f} {p_val:8.3f}")
                
                # MA parameters
                for i, name in enumerate([n for n in param_names if n.startswith('ma.')]):
                    param = self.ma_params[i]
                    se = self._standard_errors['ma_se'][i] if i < len(self._standard_errors['ma_se']) else np.nan
                    t_val = t_stats['ma_t'][i] if t_stats and 'ma_t' in t_stats and i < len(t_stats['ma_t']) else np.nan
                    p_val = p_vals['ma_p'][i] if p_vals and 'ma_p' in p_vals and i < len(p_vals['ma_p']) else np.nan
                    
                    lines.append(f"{name:>12s} {param:10.4f} {se:10.4f} {t_val:8.3f} {p_val:8.3f}")
                
                # Intercept
                if self.intercept is not None:
                    intercept_name = [n for n in param_names if n in ['intercept', 'drift']]
                    if intercept_name:
                        name = intercept_name[0]
                        param = self.intercept
                        se = self._standard_errors['intercept_se'] if self._standard_errors['intercept_se'] is not None else np.nan
                        t_val = t_stats['intercept_t'] if t_stats and 'intercept_t' in t_stats else np.nan
                        p_val = p_vals['intercept_p'] if p_vals and 'intercept_p' in p_vals else np.nan
                        
                        lines.append(f"{name:>12s} {param:10.4f} {se:10.4f} {t_val:8.3f} {p_val:8.3f}")
                
            else:
                # Simple format without standard errors
                for name, param in zip(param_names, all_params):
                    lines.append(f"  {name:>12s}: {param:8.4f}")
            
            lines.append("")
        
        # Residual diagnostics
        if self.sigma2 > 0:
            lines.append(f"Residual standard error: {np.sqrt(self.sigma2):.4f}")
            lines.append(f"Residual variance: {self.sigma2:.4f}")
        
        return "\n".join(lines)
    
    def diagnostics(self, recompute: bool = False) -> Dict:
        """
        Get comprehensive residual diagnostics
        
        Args:
            recompute: Force recomputation of diagnostics
            
        Returns:
            Dictionary with diagnostic test results
        """
        if self._diagnostics is None or recompute:
            if self.residuals is None:
                raise ValueError("No residuals available for diagnostics")
            
            from .diagnostics import ARIMADiagnostics
            
            # Get AR and MA orders
            ar_order = len(self.ar_params)
            ma_order = len(self.ma_params)
            
            # Run comprehensive diagnostics
            self._diagnostics = ARIMADiagnostics.comprehensive_diagnostics(
                self.residuals, ar_order, ma_order
            )
        
        return self._diagnostics
    
    def diagnostic_summary(self) -> str:
        """Get formatted diagnostic summary"""
        from .diagnostics import ARIMADiagnostics
        
        diagnostics = self.diagnostics()
        return ARIMADiagnostics.diagnostic_summary(diagnostics)
    
    def forecast(self, steps: int, alpha: float = 0.05) -> Tuple[Serie, Serie, Serie]:
        """
        Generate forecasts with confidence intervals
        
        Args:
            steps: Number of periods to forecast
            alpha: Significance level for confidence intervals (default 0.05 for 95% CI)
            
        Returns:
            Tuple of (forecasts, lower_bounds, upper_bounds)
        """
        if self.differenced_series is None or self.original_series is None:
            raise ValueError("Cannot forecast without fitted model data")
        
        # Use Kalman filter forecasting if available (MLE estimation)
        if self._kalman_info is not None and self.method == "mle":
            forecasts, forecast_vars = self._generate_kalman_forecasts(steps)
            forecast_errors = forecast_vars
        else:
            # Generate point forecasts
            forecasts = self._generate_point_forecasts(steps)
            # Generate confidence intervals
            forecast_errors = self._generate_forecast_errors(steps)
        
        # Critical value (use t-distribution if we have degrees of freedom)
        if self.degrees_freedom > 0:
            from scipy import stats
            critical_val = stats.t.ppf(1 - alpha/2, self.degrees_freedom)
        else:
            critical_val = 1.96  # Normal approximation
        
        lower_bounds = forecasts - critical_val * np.sqrt(forecast_errors)
        upper_bounds = forecasts + critical_val * np.sqrt(forecast_errors)
        
        # Create forecast series with appropriate dates
        if self.original_series.is_stochastic():
            start_date = self.original_series.dating.successor(self.original_series.last_date)
            forecast_dates = []
            current_date = start_date
            
            for _ in range(steps):
                forecast_dates.append(current_date)
                current_date = self.original_series.dating.successor(current_date)
            
            forecast_serie = Serie(data=forecasts, 
                                 first_date=forecast_dates[0],
                                 last_date=forecast_dates[-1],
                                 dating=self.original_series.dating)
            
            lower_serie = Serie(data=lower_bounds,
                              first_date=forecast_dates[0], 
                              last_date=forecast_dates[-1],
                              dating=self.original_series.dating)
            
            upper_serie = Serie(data=upper_bounds,
                              first_date=forecast_dates[0],
                              last_date=forecast_dates[-1], 
                              dating=self.original_series.dating)
        else:
            forecast_serie = Serie(data=forecasts)
            lower_serie = Serie(data=lower_bounds)
            upper_serie = Serie(data=upper_bounds)
        
        return forecast_serie, lower_serie, upper_serie
    
    def _generate_point_forecasts(self, steps: int) -> np.ndarray:
        """Generate point forecasts using ARIMA recursions"""
        # Extract last observations from differenced series
        diff_data = self.differenced_series._data.to_numpy()
        diff_data = diff_data[~np.isnan(diff_data)]
        
        if len(diff_data) == 0:
            return np.full(steps, np.nan)
        
        # Initialize forecast array
        forecasts = np.zeros(steps)
        
        # Get AR and MA lags
        ar_lags, ma_lags = self.factor.get_polynomial_orders()
        
        # Extend series with zeros for forecasting
        extended_series = np.concatenate([diff_data, np.zeros(steps)])
        
        # Residuals for MA part (assume zero for future)
        if self.residuals is not None:
            resid_data = self.residuals._data.to_numpy()
            resid_data = resid_data[~np.isnan(resid_data)]
            extended_residuals = np.concatenate([resid_data, np.zeros(steps)])
        else:
            extended_residuals = np.zeros(len(extended_series))
        
        n_orig = len(diff_data)
        
        # Generate forecasts recursively
        for h in range(steps):
            forecast_idx = n_orig + h
            forecast_val = 0.0
            
            # Add intercept/mean
            if self.intercept is not None:
                forecast_val += self.intercept
            
            # AR component
            for i, lag in enumerate(ar_lags):
                if forecast_idx - lag >= 0:
                    forecast_val += self.ar_params[i] * extended_series[forecast_idx - lag]
            
            # MA component (only use past residuals, future are zero)
            for i, lag in enumerate(ma_lags):
                if forecast_idx - lag >= 0 and forecast_idx - lag < len(extended_residuals):
                    if forecast_idx - lag < n_orig:  # Only past residuals
                        forecast_val += self.ma_params[i] * extended_residuals[forecast_idx - lag]
            
            extended_series[forecast_idx] = forecast_val
            forecasts[h] = forecast_val
        
        # If model was differenced, integrate forecasts back to original scale
        if self.factor.num_diff_operations > 0:
            forecasts = self._integrate_forecasts(forecasts)
        
        return forecasts
    
    def _integrate_forecasts(self, diff_forecasts: np.ndarray) -> np.ndarray:
        """Integrate differenced forecasts back to original scale"""
        # This is a simplified implementation
        # TODO: Implement proper integration for seasonal differencing
        
        orig_data = self.original_series._data.to_numpy()
        orig_data = orig_data[~np.isnan(orig_data)]
        
        if len(orig_data) == 0:
            return diff_forecasts
        
        # Simple integration for first differences
        integrated = np.zeros(len(diff_forecasts))
        last_level = orig_data[-1]
        
        for i, diff_val in enumerate(diff_forecasts):
            integrated[i] = last_level + diff_val
            last_level = integrated[i]
        
        return integrated
    
    def _generate_forecast_errors(self, steps: int) -> np.ndarray:
        """Generate forecast error variances"""
        # Simplified implementation - assume constant variance
        # TODO: Implement proper forecast error variance calculation
        forecast_errors = np.zeros(steps)
        
        for h in range(steps):
            # Base forecast error variance
            forecast_errors[h] = self.sigma2
            
            # Add cumulative MA coefficient effects
            # This is a very simplified approximation
            forecast_errors[h] *= (1 + h * 0.1)  # Increasing uncertainty
        
        return forecast_errors
    
    def _generate_kalman_forecasts(self, steps: int) -> Tuple[np.ndarray, np.ndarray]:
        """Generate forecasts using Kalman filter (exact method)"""
        from .kalman import ARIMAKalmanFilter
        
        # Get differenced data
        diff_data = self.differenced_series._data.to_numpy()
        valid_data = diff_data[~np.isnan(diff_data)]
        
        if len(valid_data) == 0:
            return np.full(steps, np.nan), np.full(steps, np.nan)
        
        # Create Kalman filter
        kalman = ARIMAKalmanFilter(self.factor)
        
        # Generate forecasts
        forecasts, forecast_vars = kalman.forecast_kalman(
            valid_data, self.ar_params, self.ma_params, 
            self.sigma2, self.intercept, steps
        )
        
        # If model was differenced, integrate forecasts back to original scale
        if self.factor.num_diff_operations > 0:
            forecasts = self._integrate_forecasts(forecasts)
        
        return forecasts, forecast_vars


class ARIMA:
    """
    ARIMA model class
    
    Provides ARIMA model estimation, diagnostics, and forecasting
    following TOL's BARIMA implementation structure.
    """
    
    def __init__(self, factor: ARIMAFactor):
        """
        Initialize ARIMA model
        
        Args:
            factor: ARIMAFactor specifying the model structure
        """
        self.factor = factor
        self.bounds = ARIMAParameterBounds(factor)
        self.results: Optional[ARIMAResults] = None
    
    def fit(self, serie: Serie, method: str = "css", 
            start_params: Optional[np.ndarray] = None,
            maxiter: int = 1000, tolerance: float = 1e-6) -> ARIMAResults:
        """
        Fit ARIMA model to time series data
        
        Args:
            serie: Time series to fit
            method: Estimation method ("css", "mle", "yule_walker")
            start_params: Initial parameter values
            maxiter: Maximum iterations for optimization
            tolerance: Convergence tolerance
            
        Returns:
            ARIMAResults object with estimation results
        """
        # Import estimation module here to avoid circular imports
        from .estimation import ARIMAEstimator
        
        # Validate data length
        if not self.factor.validate_against_data_length(len(serie)):
            raise ValueError(f"Insufficient data: need at least {self.factor.required_observations()} "
                           f"observations, got {len(serie)}")
        
        # Create estimator and fit model
        estimator = ARIMAEstimator(self.factor)
        self.results = estimator.fit(serie, method=method, 
                                   start_params=start_params,
                                   maxiter=maxiter, tolerance=tolerance)
        
        return self.results
    
    def forecast(self, steps: int, alpha: float = 0.05) -> Tuple[Serie, Serie, Serie]:
        """
        Generate forecasts from fitted model
        
        Args:
            steps: Number of periods to forecast
            alpha: Significance level for confidence intervals
            
        Returns:
            Tuple of (forecasts, lower_bounds, upper_bounds)
        """
        if self.results is None:
            raise ValueError("Model must be fitted before forecasting")
        
        return self.results.forecast(steps, alpha)
    
    def predict(self, start: Optional[int] = None, end: Optional[int] = None,
                in_sample: bool = True) -> Serie:
        """
        Generate predictions for specific periods
        
        Args:
            start: Start index for predictions
            end: End index for predictions  
            in_sample: Whether to generate in-sample predictions
            
        Returns:
            Serie with predictions
        """
        if self.results is None:
            raise ValueError("Model must be fitted before prediction")
        
        if in_sample and self.results.fitted_values is not None:
            if start is None and end is None:
                return self.results.fitted_values
            else:
                # TODO: Implement subset selection
                return self.results.fitted_values
        else:
            # Out-of-sample prediction
            if start is None:
                start = len(self.results.original_series)
            if end is None:
                end = start + 10  # Default 10 periods
            
            steps = end - start + 1
            forecasts, _, _ = self.forecast(steps)
            return forecasts
    
    def summary(self) -> str:
        """Get summary of fitted model"""
        if self.results is None:
            return f"ARIMA{self.factor.order} model (not fitted)"
        
        return self.results.summary()
    
    def __str__(self) -> str:
        return f"ARIMA{self.factor.order}"
    
    def __repr__(self) -> str:
        if self.results is not None:
            return f"ARIMA(factor={self.factor}, fitted=True, method={self.results.method})"
        else:
            return f"ARIMA(factor={self.factor}, fitted=False)"