"""ARIMA modeling for TOL Python time series"""

try:
    # Try relative imports first (when used as package)
    from .arima_factor import ARIMAFactor
    from .arima_model import ARIMA, ARIMAResults
    from .estimation import ARIMAEstimator
    from .kalman import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ilter
    from .diagnostics import ARIMADiagnostics
    from .auto_arima import AutoARIMA
except ImportError:
    # Fallback to absolute imports (when used directly)
    from arima.arima_factor import ARIMAFactor
    from arima.arima_model import ARIMA, ARIMAResults
    from arima.estimation import ARIMAEstimator
    from arima.kalman import <PERSON>IM<PERSON><PERSON>almanFilter
    from arima.diagnostics import ARIMADiagnostics
    from arima.auto_arima import AutoARIMA

__all__ = [
    'ARIMAFactor', 'ARIMA', 'ARIMAResults', 'ARIMAEstimator',
    'ARIMAKalmanFilter', 'ARIMADiagnostics', 'AutoARIMA'
]