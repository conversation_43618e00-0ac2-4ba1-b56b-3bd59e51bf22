"""
ARIMA Factor specification
Mirrors TOL's BARIMAFactor for model specification and validation
"""

import numpy as np
from typing import Op<PERSON>, <PERSON><PERSON>, List
from dataclasses import dataclass


@dataclass
class ARIMAFactor:
    """
    ARIMA model specification factor
    
    Represents an ARIMA(p,d,q) x (P,D,Q)s model specification,
    matching TOL's BARIMAFactor structure.
    
    Attributes:
        ar_order (int): Autoregressive order (p)
        diff_order (int): Differencing order (d)  
        ma_order (int): Moving average order (q)
        seasonal_ar (int): Seasonal autoregressive order (P)
        seasonal_diff (int): Seasonal differencing order (D)
        seasonal_ma (int): Seasonal moving average order (Q)
        season_length (int): Length of seasonal period (s)
        include_mean (bool): Whether to include mean/constant term
        include_drift (bool): Whether to include drift term (for I(1) series)
    """
    
    ar_order: int = 0
    diff_order: int = 0
    ma_order: int = 0
    seasonal_ar: int = 0
    seasonal_diff: int = 0
    seasonal_ma: int = 0
    season_length: int = 1
    include_mean: bool = True
    include_drift: bool = False
    
    def __post_init__(self):
        """Validate ARIMA factor specification"""
        if self.ar_order < 0:
            raise ValueError("AR order must be non-negative")
        if self.diff_order < 0:
            raise ValueError("Differencing order must be non-negative")
        if self.ma_order < 0:
            raise ValueError("MA order must be non-negative")
        if self.seasonal_ar < 0:
            raise ValueError("Seasonal AR order must be non-negative")
        if self.seasonal_diff < 0:
            raise ValueError("Seasonal differencing order must be non-negative")
        if self.seasonal_ma < 0:
            raise ValueError("Seasonal MA order must be non-negative")
        if self.season_length < 1:
            raise ValueError("Season length must be positive")
        
        # Check for seasonal components without seasonal period
        if (self.seasonal_ar > 0 or self.seasonal_diff > 0 or self.seasonal_ma > 0) and self.season_length <= 1:
            raise ValueError("Seasonal components require season_length > 1")
        
        # Validate maximum differencing
        if self.diff_order + self.seasonal_diff > 2:
            raise ValueError("Total differencing order should not exceed 2")
    
    @classmethod
    def from_order(cls, order: Tuple[int, int, int], 
                   seasonal_order: Optional[Tuple[int, int, int, int]] = None,
                   **kwargs) -> 'ARIMAFactor':
        """
        Create ARIMAFactor from order tuples
        
        Args:
            order: (p, d, q) tuple for non-seasonal part
            seasonal_order: (P, D, Q, s) tuple for seasonal part
            **kwargs: Additional parameters (include_mean, include_drift)
            
        Returns:
            ARIMAFactor instance
        """
        p, d, q = order
        
        if seasonal_order is not None:
            P, D, Q, s = seasonal_order
        else:
            P, D, Q, s = 0, 0, 0, 1
        
        return cls(ar_order=p, diff_order=d, ma_order=q,
                  seasonal_ar=P, seasonal_diff=D, seasonal_ma=Q,
                  season_length=s, **kwargs)
    
    @property
    def order(self) -> Tuple[int, int, int]:
        """Get non-seasonal order as (p, d, q) tuple"""
        return (self.ar_order, self.diff_order, self.ma_order)
    
    @property
    def seasonal_order(self) -> Tuple[int, int, int, int]:
        """Get seasonal order as (P, D, Q, s) tuple"""
        return (self.seasonal_ar, self.seasonal_diff, self.seasonal_ma, self.season_length)
    
    @property
    def is_seasonal(self) -> bool:
        """Check if model has seasonal components"""
        return (self.seasonal_ar > 0 or self.seasonal_diff > 0 or 
                self.seasonal_ma > 0) and self.season_length > 1
    
    @property
    def is_stationary(self) -> bool:
        """Check if model specification implies stationarity"""
        return self.diff_order == 0 and self.seasonal_diff == 0
    
    @property
    def is_invertible(self) -> bool:
        """
        Check if model is potentially invertible
        (Actual invertibility depends on parameter values)
        """
        return True  # Will be checked after parameter estimation
    
    @property
    def num_ar_params(self) -> int:
        """Number of AR parameters to estimate"""
        return self.ar_order + self.seasonal_ar
    
    @property
    def num_ma_params(self) -> int:
        """Number of MA parameters to estimate"""
        return self.ma_order + self.seasonal_ma
    
    @property
    def num_diff_operations(self) -> int:
        """Total number of differencing operations"""
        return self.diff_order + self.seasonal_diff
    
    @property
    def num_params(self) -> int:
        """Total number of parameters to estimate"""
        params = self.num_ar_params + self.num_ma_params
        
        # Add intercept/mean parameter
        if self.include_mean and self.is_stationary:
            params += 1
        elif self.include_drift and not self.is_stationary:
            params += 1
            
        return params
    
    def get_polynomial_orders(self) -> Tuple[List[int], List[int]]:
        """
        Get polynomial lag orders for AR and MA parts
        
        Returns:
            Tuple of (ar_lags, ma_lags) where each is a list of lag indices
        """
        ar_lags = []
        ma_lags = []
        
        # Non-seasonal AR lags
        for i in range(1, self.ar_order + 1):
            ar_lags.append(i)
        
        # Seasonal AR lags
        for i in range(1, self.seasonal_ar + 1):
            ar_lags.append(i * self.season_length)
        
        # Non-seasonal MA lags
        for i in range(1, self.ma_order + 1):
            ma_lags.append(i)
        
        # Seasonal MA lags
        for i in range(1, self.seasonal_ma + 1):
            ma_lags.append(i * self.season_length)
        
        return sorted(ar_lags), sorted(ma_lags)
    
    def max_lag(self) -> int:
        """Maximum lag needed for this model"""
        max_ar_lag = max([self.ar_order, self.seasonal_ar * self.season_length] + [0])
        max_ma_lag = max([self.ma_order, self.seasonal_ma * self.season_length] + [0])
        max_diff_lag = max([self.diff_order, self.seasonal_diff * self.season_length] + [0])
        
        return max(max_ar_lag, max_ma_lag, max_diff_lag)
    
    def required_observations(self) -> int:
        """Minimum number of observations needed for estimation"""
        return max(self.max_lag() + self.num_params + 10, 30)
    
    def create_differenced_name(self) -> str:
        """Create a descriptive name for the differenced series"""
        if self.diff_order == 0 and self.seasonal_diff == 0:
            return "original"
        
        parts = []
        if self.diff_order > 0:
            if self.diff_order == 1:
                parts.append("diff")
            else:
                parts.append(f"diff{self.diff_order}")
        
        if self.seasonal_diff > 0:
            if self.seasonal_diff == 1:
                parts.append(f"sdiff{self.season_length}")
            else:
                parts.append(f"sdiff{self.seasonal_diff}_{self.season_length}")
        
        return "_".join(parts)
    
    def validate_against_data_length(self, n_obs: int) -> bool:
        """
        Validate that model can be estimated with given data length
        
        Args:
            n_obs: Number of observations available
            
        Returns:
            True if model can be estimated, False otherwise
        """
        required = self.required_observations()
        return n_obs >= required
    
    def get_model_string(self) -> str:
        """Get string representation of model"""
        if self.is_seasonal:
            base = f"ARIMA({self.ar_order},{self.diff_order},{self.ma_order})"
            seasonal = f"({self.seasonal_ar},{self.seasonal_diff},{self.seasonal_ma})[{self.season_length}]"
            model_str = base + seasonal
        else:
            model_str = f"ARIMA({self.ar_order},{self.diff_order},{self.ma_order})"
        
        # Add modifiers
        modifiers = []
        if self.include_mean and self.is_stationary:
            modifiers.append("with mean")
        elif self.include_drift and not self.is_stationary:
            modifiers.append("with drift")
        
        if modifiers:
            model_str += " " + ", ".join(modifiers)
        
        return model_str
    
    def __str__(self) -> str:
        return self.get_model_string()
    
    def __repr__(self) -> str:
        return (f"ARIMAFactor(ar_order={self.ar_order}, diff_order={self.diff_order}, "
                f"ma_order={self.ma_order}, seasonal_ar={self.seasonal_ar}, "
                f"seasonal_diff={self.seasonal_diff}, seasonal_ma={self.seasonal_ma}, "
                f"season_length={self.season_length})")


class ARIMAParameterBounds:
    """
    Parameter bounds and constraints for ARIMA estimation
    """
    
    def __init__(self, factor: ARIMAFactor):
        self.factor = factor
    
    def get_ar_bounds(self) -> List[Tuple[float, float]]:
        """Get bounds for AR parameters (stationarity constraints)"""
        # For now, use conservative bounds
        # TODO: Implement proper stationarity constraints
        bounds = []
        for _ in range(self.factor.num_ar_params):
            bounds.append((-0.999, 0.999))
        return bounds
    
    def get_ma_bounds(self) -> List[Tuple[float, float]]:
        """Get bounds for MA parameters (invertibility constraints)"""
        # For now, use conservative bounds
        # TODO: Implement proper invertibility constraints
        bounds = []
        for _ in range(self.factor.num_ma_params):
            bounds.append((-0.999, 0.999))
        return bounds
    
    def get_all_bounds(self) -> List[Tuple[float, float]]:
        """Get bounds for all parameters"""
        bounds = []
        
        # AR parameter bounds
        bounds.extend(self.get_ar_bounds())
        
        # MA parameter bounds
        bounds.extend(self.get_ma_bounds())
        
        # Intercept/drift bounds
        if self.factor.include_mean or self.factor.include_drift:
            bounds.append((-np.inf, np.inf))  # No bounds on mean/drift
        
        return bounds
    
    def check_stationarity(self, ar_params: np.ndarray) -> bool:
        """
        Check if AR parameters satisfy stationarity conditions
        
        Args:
            ar_params: AR parameter array
            
        Returns:
            True if stationary, False otherwise
        """
        if len(ar_params) == 0:
            return True
        
        # For AR(1): |phi| < 1
        if len(ar_params) == 1:
            return abs(ar_params[0]) < 1
        
        # For higher order: check characteristic equation roots
        # Create characteristic polynomial: 1 - phi1*z - phi2*z^2 - ...
        poly_coeffs = np.concatenate([[1], -ar_params])
        roots = np.roots(poly_coeffs)
        
        # All roots should be outside unit circle (|root| > 1)
        return np.all(np.abs(roots) > 1.001)  # Small tolerance for numerical stability
    
    def check_invertibility(self, ma_params: np.ndarray) -> bool:
        """
        Check if MA parameters satisfy invertibility conditions
        
        Args:
            ma_params: MA parameter array
            
        Returns:
            True if invertible, False otherwise
        """
        if len(ma_params) == 0:
            return True
        
        # For MA(1): |theta| < 1
        if len(ma_params) == 1:
            return abs(ma_params[0]) < 1
        
        # For higher order: check characteristic equation roots
        # Create characteristic polynomial: 1 + theta1*z + theta2*z^2 + ...
        poly_coeffs = np.concatenate([[1], ma_params])
        roots = np.roots(poly_coeffs)
        
        # All roots should be outside unit circle (|root| > 1)
        return np.all(np.abs(roots) > 1.001)  # Small tolerance for numerical stability