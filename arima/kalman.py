"""
Kalman filter implementation for ARIMA maximum likelihood estimation
Provides exact likelihood computation and parameter standard errors
"""

import numpy as np
import numpy.ma as ma
from typing import Tuple, Optional, Dict, List
from scipy import linalg
import warnings

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from .arima_factor import ARIMAFactor
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from arima.arima_factor import ARIMAFactor


class ARIMAKalmanFilter:
    """
    <PERSON>lman filter for ARIMA models in state space form
    
    Converts ARIMA(p,d,q) to state space representation and computes
    exact likelihood via <PERSON><PERSON> filter recursions.
    """
    
    def __init__(self, factor: ARIMAFactor):
        """
        Initialize <PERSON>lman filter for ARIMA model
        
        Args:
            factor: ARIMA model specification
        """
        self.factor = factor
        self.state_dim = None
        self.obs_dim = 1  # Single observed variable (the time series)
        
    def setup_state_space(self, ar_params: np.ndarray, ma_params: np.ndarray, 
                         sigma2: float) -> Tuple[np.n<PERSON><PERSON>, np.n<PERSON><PERSON>, np.n<PERSON><PERSON>, np.ndar<PERSON>]:
        """
        Convert ARIMA to state space representation
        
        State space form:
        x[t+1] = F * x[t] + G * w[t]    (state equation)
        y[t]   = H * x[t] + v[t]        (observation equation)
        
        where w[t] ~ N(0, Q) and v[t] ~ N(0, R)
        
        Args:
            ar_params: AR parameters
            ma_params: MA parameters  
            sigma2: Innovation variance
            
        Returns:
            Tuple of (F, G, H, Q) matrices
        """
        # Get polynomial orders
        ar_lags, ma_lags = self.factor.get_polynomial_orders()
        
        p = len(ar_params)  # Total AR order
        q = len(ma_params)  # Total MA order
        
        # State dimension = max(p, q+1)
        self.state_dim = max(p, q + 1, 1)
        
        # Initialize state space matrices
        F = np.zeros((self.state_dim, self.state_dim))
        G = np.zeros((self.state_dim, 1))
        H = np.zeros((1, self.state_dim))
        Q = np.array([[sigma2]])
        
        # Fill F matrix (state transition)
        if p > 0:
            # AR part: first row contains AR coefficients
            for i, lag in enumerate(ar_lags):
                if lag <= self.state_dim:
                    F[0, lag - 1] = ar_params[i]
            
            # Companion form: shift states
            for i in range(1, min(p, self.state_dim)):
                F[i, i - 1] = 1.0
        
        # Fill G matrix (innovation loading)
        G[0, 0] = 1.0
        
        # Add MA effects to G if present
        if q > 0:
            for i, lag in enumerate(ma_lags):
                if lag < self.state_dim:
                    G[lag, 0] = ma_params[i]
        
        # Fill H matrix (observation equation)
        H[0, 0] = 1.0
        
        return F, G, H, Q
    
    def compute_likelihood(self, data: np.ndarray, ar_params: np.ndarray,
                          ma_params: np.ndarray, sigma2: float,
                          intercept: Optional[float] = None) -> Tuple[float, Dict]:
        """
        Compute exact log-likelihood using Kalman filter
        
        Args:
            data: Observed time series (after differencing)
            ar_params: AR parameters
            ma_params: MA parameters
            sigma2: Innovation variance
            intercept: Intercept/mean parameter
            
        Returns:
            Tuple of (log_likelihood, info_dict)
        """
        # Remove missing values
        valid_data = data[~np.isnan(data)]
        n = len(valid_data)
        
        if n == 0:
            return -np.inf, {}
        
        # Adjust for intercept
        if intercept is not None:
            adjusted_data = valid_data - intercept
        else:
            adjusted_data = valid_data.copy()
        
        # Setup state space matrices
        F, G, H, Q = self.setup_state_space(ar_params, ma_params, sigma2)
        
        # Initialize Kalman filter
        x = np.zeros((self.state_dim, 1))  # Initial state
        P = np.eye(self.state_dim) * 1000  # Initial state covariance (diffuse)
        
        log_likelihood = 0.0
        prediction_errors = []
        error_variances = []
        
        # Kalman filter recursions
        for t in range(n):
            y_t = adjusted_data[t]
            
            # Prediction step
            y_pred = H @ x                    # Predicted observation
            e_t = y_t - float(y_pred[0, 0])  # Prediction error (ensure scalar)
            S_t = float(H @ P @ H.T) + sigma2    # Prediction error variance (ensure scalar)
            
            # Handle numerical issues
            if S_t <= 1e-12:
                S_t = 1e-8
            
            # Log-likelihood contribution
            log_likelihood += -0.5 * (np.log(2 * np.pi) + np.log(S_t) + (e_t ** 2) / S_t)
            
            # Store for diagnostics
            prediction_errors.append(e_t)
            error_variances.append(S_t)
            
            # Update step (if not at end)
            if t < n - 1:
                # Kalman gain
                K = P @ H.T / S_t
                
                # State update
                x = x + K * e_t
                
                # Covariance update
                P = P - K @ H @ P
                
                # Prediction for next period
                x = F @ x + G @ np.array([[0]])  # No innovation for prediction
                P = F @ P @ F.T + G @ Q @ G.T
        
        # Return results
        info = {
            'prediction_errors': np.array(prediction_errors),
            'error_variances': np.array(error_variances),
            'final_state': x,
            'final_covariance': P,
            'n_obs': n
        }
        
        return log_likelihood, info
    
    def compute_information_matrix(self, data: np.ndarray, ar_params: np.ndarray,
                                 ma_params: np.ndarray, sigma2: float,
                                 intercept: Optional[float] = None,
                                 delta: float = 1e-5) -> np.ndarray:
        """
        Compute Fisher information matrix via numerical differentiation
        
        Used to compute parameter standard errors: se = sqrt(diag(inv(I)))
        
        Args:
            data: Observed time series
            ar_params: AR parameters
            ma_params: MA parameters  
            sigma2: Innovation variance
            intercept: Intercept parameter
            delta: Step size for numerical derivatives
            
        Returns:
            Fisher information matrix
        """
        # Pack parameters
        params = []
        params.extend(ar_params)
        params.extend(ma_params)
        if intercept is not None:
            params.append(intercept)
        params.append(np.log(sigma2))  # Use log(sigma2) for positivity
        
        params = np.array(params)
        n_params = len(params)
        
        # Compute Hessian matrix via finite differences
        hessian = np.zeros((n_params, n_params))
        
        def neg_log_likelihood(p):
            """Negative log-likelihood for minimization"""
            ar_p = p[:len(ar_params)]
            ma_p = p[len(ar_params):len(ar_params) + len(ma_params)]
            sigma2_p = np.exp(p[-1])  # Transform back from log
            
            if intercept is not None:
                intercept_p = p[-2]
            else:
                intercept_p = None
            
            ll, _ = self.compute_likelihood(data, ar_p, ma_p, sigma2_p, intercept_p)
            return -ll
        
        # Central difference approximation for Hessian
        for i in range(n_params):
            for j in range(i, n_params):
                # f(x + h_i + h_j)
                params_pp = params.copy()
                params_pp[i] += delta
                params_pp[j] += delta
                f_pp = neg_log_likelihood(params_pp)
                
                # f(x + h_i - h_j)
                params_pm = params.copy()
                params_pm[i] += delta
                params_pm[j] -= delta
                f_pm = neg_log_likelihood(params_pm)
                
                # f(x - h_i + h_j)
                params_mp = params.copy()
                params_mp[i] -= delta
                params_mp[j] += delta
                f_mp = neg_log_likelihood(params_mp)
                
                # f(x - h_i - h_j)
                params_mm = params.copy()
                params_mm[i] -= delta
                params_mm[j] -= delta
                f_mm = neg_log_likelihood(params_mm)
                
                # Central difference formula
                hessian[i, j] = (f_pp - f_pm - f_mp + f_mm) / (4 * delta * delta)
                
                # Symmetric matrix
                if i != j:
                    hessian[j, i] = hessian[i, j]
        
        return hessian
    
    def compute_standard_errors(self, data: np.ndarray, ar_params: np.ndarray,
                              ma_params: np.ndarray, sigma2: float,
                              intercept: Optional[float] = None) -> Dict[str, np.ndarray]:
        """
        Compute parameter standard errors from Fisher information matrix
        
        Args:
            data: Observed time series
            ar_params: AR parameters
            ma_params: MA parameters
            sigma2: Innovation variance
            intercept: Intercept parameter
            
        Returns:
            Dictionary with standard errors for each parameter type
        """
        try:
            # Compute Fisher information matrix
            fisher_info = self.compute_information_matrix(
                data, ar_params, ma_params, sigma2, intercept
            )
            
            # Invert to get covariance matrix
            cov_matrix = linalg.inv(fisher_info)
            
            # Extract standard errors (square root of diagonal)
            se = np.sqrt(np.diag(cov_matrix))
            
            # Split into parameter types
            idx = 0
            result = {}
            
            if len(ar_params) > 0:
                result['ar_se'] = se[idx:idx + len(ar_params)]
                idx += len(ar_params)
            else:
                result['ar_se'] = np.array([])
            
            if len(ma_params) > 0:
                result['ma_se'] = se[idx:idx + len(ma_params)]
                idx += len(ma_params)
            else:
                result['ma_se'] = np.array([])
            
            if intercept is not None:
                result['intercept_se'] = se[idx]
                idx += 1
            else:
                result['intercept_se'] = None
            
            # Standard error for sigma2 (delta method)
            result['sigma2_se'] = se[idx] * sigma2  # Transform from log(sigma2)
            
            return result
            
        except (linalg.LinAlgError, np.linalg.LinAlgError) as e:
            warnings.warn(f"Could not compute standard errors - {str(e)}")
            
            # Return NaN standard errors
            return {
                'ar_se': np.full(len(ar_params), np.nan),
                'ma_se': np.full(len(ma_params), np.nan),
                'intercept_se': np.nan if intercept is not None else None,
                'sigma2_se': np.nan
            }
        except Exception as e:
            warnings.warn(f"Unexpected error computing standard errors - {str(e)}")
            
            # Return NaN standard errors
            return {
                'ar_se': np.full(len(ar_params), np.nan),
                'ma_se': np.full(len(ma_params), np.nan),
                'intercept_se': np.nan if intercept is not None else None,
                'sigma2_se': np.nan
            }
    
    def forecast_kalman(self, data: np.ndarray, ar_params: np.ndarray,
                       ma_params: np.ndarray, sigma2: float,
                       intercept: Optional[float], steps: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate forecasts using Kalman filter state space approach
        
        Args:
            data: Observed time series (after differencing)
            ar_params: AR parameters
            ma_params: MA parameters
            sigma2: Innovation variance
            intercept: Intercept parameter
            steps: Number of forecast steps
            
        Returns:
            Tuple of (forecasts, forecast_variances)
        """
        # Remove missing values
        valid_data = data[~np.isnan(data)]
        n = len(valid_data)
        
        if n == 0:
            return np.full(steps, np.nan), np.full(steps, np.nan)
        
        # Adjust for intercept
        if intercept is not None:
            adjusted_data = valid_data - intercept
        else:
            adjusted_data = valid_data.copy()
        
        # Setup state space matrices
        F, G, H, Q = self.setup_state_space(ar_params, ma_params, sigma2)
        
        # Run Kalman filter to get final state
        x = np.zeros((self.state_dim, 1))
        P = np.eye(self.state_dim) * 1000
        
        # Filter through all observations
        for t in range(n):
            y_t = adjusted_data[t]
            
            # Prediction
            y_pred = H @ x
            e_t = y_t - float(y_pred[0, 0])
            S_t = float(H @ P @ H.T) + sigma2
            
            if S_t <= 1e-12:
                S_t = 1e-8
            
            # Update
            K = P @ H.T / S_t
            x = x + K * e_t
            P = P - K @ H @ P
            
            # Predict next state
            if t < n - 1:
                x = F @ x
                P = F @ P @ F.T + G @ Q @ G.T
        
        # Generate forecasts
        forecasts = np.zeros(steps)
        forecast_vars = np.zeros(steps)
        
        for h in range(steps):
            # Forecast
            x = F @ x
            P = F @ P @ F.T + G @ Q @ G.T
            
            # Predicted observation
            y_forecast = H @ x
            forecast_var = H @ P @ H.T + sigma2
            
            forecasts[h] = float(y_forecast[0, 0])
            forecast_vars[h] = float(forecast_var[0, 0])
            
            # Add intercept back if needed
            if intercept is not None:
                forecasts[h] += intercept
        
        return forecasts, forecast_vars