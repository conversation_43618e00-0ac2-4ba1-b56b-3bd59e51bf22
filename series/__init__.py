"""Series implementation for TOL Python"""

from .serie import Serie
from .operations import SerieOperations
from .corrected_serie import Serie as CorrectedSerie, SerieBase, IndicatorSerie, SerieTable, cal_ind

__all__ = ['Serie', 'SerieOperations', 'CorrectedSerie', 'SerieBase', 'IndicatorSerie', 'SerieTable', 'cal_ind']

# Export key functions at module level for convenience
# Note: cal_ind is available from both SerieOperations.cal_ind (old) and corrected_serie.cal_ind (new)
dat_ch = SerieOperations.dat_ch
first_s = SerieOperations.first_s
last_s = SerieOperations.last_s
avr_s = SerieOperations.avr_s
sum_s_range = SerieOperations.sum_s_range