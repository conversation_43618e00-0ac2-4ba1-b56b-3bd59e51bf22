"""
Serie operations - arithmetic, transformations, and aggregations
Mirrors TOL's serie operation functions
"""

from typing import Union, Optional, Callable
import numpy as np
from .serie import Serie

try:
    # Try relative imports first (when used as package)
    from ..core import Date, TimeSet
except ImportError:
    # Fallback to absolute imports (when used directly)
    from core.dates import Date, TimeSet


class SerieOperations:
    """
    Static methods for Serie operations
    These can be called as functions or added as Serie methods
    """
    
    @staticmethod
    def sum_s(serie: Serie) -> float:
        """
        Sum all values in a serie (equivalent to TOL's SumS)
        Ignores missing values
        """
        total = 0.0
        count = 0
        
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is not None and not np.isnan(value):
                total += value
                count += 1
            current = serie.dating.successor(current)
        
        return total
    
    @staticmethod
    def mean_s(serie: Serie) -> float:
        """Calculate mean of serie values, ignoring missing values"""
        total = 0.0
        count = 0
        
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is not None and not np.isnan(value):
                total += value
                count += 1
            current = serie.dating.successor(current)
        
        return total / count if count > 0 else np.nan
    
    @staticmethod
    def lag(serie: Serie, periods: int = 1) -> Serie:
        """
        Lag operation - shift serie by n periods
        Equivalent to TOL's B operator (lag) 
        """
        if periods == 0:
            return serie.copy()
        
        # Calculate new date range
        if periods > 0:
            # Lag - shift forward in time
            new_first = serie.first_date
            new_last = serie.last_date
            for _ in range(periods):
                new_first = serie.dating.successor(new_first)
                new_last = serie.dating.successor(new_last)
        else:
            # Lead - shift backward in time
            new_first = serie.first_date
            new_last = serie.last_date
            for _ in range(-periods):
                new_first = serie.dating.predecessor(new_first)
                new_last = serie.dating.predecessor(new_last)
        
        # Create new serie with lagged data
        result = Serie(first_date=new_first,
                      last_date=new_last,
                      dating=serie.dating,
                      dating_type=serie._dating_type)
        
        # Copy data with lag
        current_src = serie.first_date
        current_dst = new_first
        
        while current_src <= serie.last_date and current_dst <= new_last:
            result[current_dst] = serie[current_src]
            current_src = serie.dating.successor(current_src)
            current_dst = serie.dating.successor(current_dst)
        
        return result
    
    @staticmethod
    def diff(serie: Serie, periods: int = 1) -> Serie:
        """
        Difference operation
        Equivalent to (1-B) operator in TOL
        """
        lagged = SerieOperations.lag(serie, periods)
        return SerieOperations.subtract(serie, lagged)
    
    @staticmethod
    def add(serie1: Serie, serie2: Union[Serie, float]) -> Serie:
        """Add two series or serie and scalar"""
        if isinstance(serie2, (int, float)):
            # Serie + scalar
            result = serie1.copy()
            current = result.first_date
            while current <= result.last_date:
                value = result[current]
                if value is not None and not np.isnan(value):
                    result[current] = value + serie2
                current = result.dating.successor(current)
            return result
        
        else:
            # Serie + Serie
            # Find common date range
            first = serie1.first_date if serie1.first_date > serie2.first_date else serie2.first_date
            last = serie1.last_date if serie1.last_date < serie2.last_date else serie2.last_date
            
            result = Serie(first_date=first,
                          last_date=last,
                          dating=serie1.dating,
                          dating_type=serie1._dating_type)
            
            current = first
            while current <= last:
                val1 = serie1[current]
                val2 = serie2[current]
                
                if (val1 is not None and not np.isnan(val1) and 
                    val2 is not None and not np.isnan(val2)):
                    result[current] = val1 + val2
                else:
                    result[current] = None
                
                current = serie1.dating.successor(current)
            
            return result
    
    @staticmethod
    def subtract(serie1: Serie, serie2: Union[Serie, float]) -> Serie:
        """Subtract two series or serie and scalar"""
        if isinstance(serie2, (int, float)):
            return SerieOperations.add(serie1, -serie2)
        else:
            # Serie - Serie
            neg_serie2 = SerieOperations.multiply(serie2, -1)
            return SerieOperations.add(serie1, neg_serie2)
    
    @staticmethod
    def multiply(serie1: Serie, serie2: Union[Serie, float]) -> Serie:
        """Multiply two series or serie and scalar"""
        if isinstance(serie2, (int, float)):
            # Serie * scalar
            result = serie1.copy()
            current = result.first_date
            while current <= result.last_date:
                value = result[current]
                if value is not None and not np.isnan(value):
                    result[current] = value * serie2
                current = result.dating.successor(current)
            return result
        
        else:
            # Serie * Serie
            first = serie1.first_date if serie1.first_date > serie2.first_date else serie2.first_date
            last = serie1.last_date if serie1.last_date < serie2.last_date else serie2.last_date
            
            result = Serie(first_date=first,
                          last_date=last,
                          dating=serie1.dating,
                          dating_type=serie1._dating_type)
            
            current = first
            while current <= last:
                val1 = serie1[current]
                val2 = serie2[current]
                
                if (val1 is not None and not np.isnan(val1) and 
                    val2 is not None and not np.isnan(val2)):
                    result[current] = val1 * val2
                else:
                    result[current] = None
                
                current = serie1.dating.successor(current)
            
            return result
    
    @staticmethod
    def divide(serie1: Serie, serie2: Union[Serie, float]) -> Serie:
        """Divide two series or serie by scalar"""
        if isinstance(serie2, (int, float)):
            if serie2 == 0:
                raise ValueError("Division by zero")
            return SerieOperations.multiply(serie1, 1.0 / serie2)
        else:
            # Serie / Serie
            first = serie1.first_date if serie1.first_date > serie2.first_date else serie2.first_date
            last = serie1.last_date if serie1.last_date < serie2.last_date else serie2.last_date
            
            result = Serie(first_date=first,
                          last_date=last,
                          dating=serie1.dating,
                          dating_type=serie1._dating_type)
            
            current = first
            while current <= last:
                val1 = serie1[current]
                val2 = serie2[current]
                
                if (val1 is not None and not np.isnan(val1) and 
                    val2 is not None and not np.isnan(val2) and val2 != 0):
                    result[current] = val1 / val2
                else:
                    result[current] = None
                
                current = serie1.dating.successor(current)
            
            return result
    
    @staticmethod
    def moving_average(serie: Serie, window: int) -> Serie:
        """
        Calculate moving average
        Window is the number of periods
        """
        if window <= 0:
            raise ValueError("Window must be positive")
        
        result = serie.copy()
        
        current = serie.first_date
        # Move to first position where we have enough history
        for _ in range(window - 1):
            if current > serie.last_date:
                break
            result[current] = None
            current = serie.dating.successor(current)
        
        # Calculate moving averages
        while current <= serie.last_date:
            # Collect window values
            values = []
            temp = current
            for i in range(window):
                try:
                    val = serie[temp]
                    if val is not None and not np.isnan(val):
                        values.append(val)
                except (IndexError, KeyError):
                    # Date is outside range
                    pass
                
                if i < window - 1:  # Don't go before first date on last iteration
                    temp = serie.dating.predecessor(temp)
            
            if len(values) > 0:
                result[current] = sum(values) / len(values)
            else:
                result[current] = None
            
            current = serie.dating.successor(current)
        
        return result
    
    @staticmethod
    def apply(serie: Serie, func: Callable[[float], float]) -> Serie:
        """Apply a function to each value in the serie"""
        result = serie.copy()
        
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is not None and not np.isnan(value):
                result[current] = func(value)
            else:
                result[current] = None
            current = serie.dating.successor(current)
        
        return result
    
    @staticmethod
    def cal_ind(timeset: TimeSet, dating: TimeSet, 
                start_date: Optional[Date] = None, 
                end_date: Optional[Date] = None) -> 'IndicatorSerie':
        """
        Calendar Indicator function (CalInd in TOL) - Lazy Evaluation Version
        
        Creates a Serie with 1.0 for dates in timeset, 0.0 otherwise
        Uses lazy evaluation matching C++ BTsrIndicator for optimal performance
        
        Args:
            timeset: TimeSet to check membership against
            dating: TimeSet that defines the dating/frequency of output
            start_date: Start date for the serie (default: current date)
            end_date: End date for the serie (default: 1 year from start)
            
        Returns:
            IndicatorSerie with lazy evaluation - values computed on access
            
        Example:
            # Create indicator for Sundays
            sunday_indicator = SerieOperations.cal_ind(TimeSet.wd(6), TimeSet("daily"))
            
            # Create indicator for January dates
            january_indicator = SerieOperations.cal_ind(TimeSet.m(1), TimeSet("daily"))
        """
        # Import here to avoid circular imports
        from .corrected_serie import IndicatorSerie
        
        # Default start date
        if start_date is None:
            start_date = Date("y2023m01d01")  # Default reasonable start
        
        # Default end date (1 year later)
        if end_date is None:
            # Calculate 1 year later
            year = start_date._value.year + 1
            end_date = Date(f"y{year}m{start_date._value.month:02d}d{start_date._value.day:02d}")
        
        # Return lazy indicator serie (no pre-computation)
        return IndicatorSerie(timeset, dating, start_date, end_date)
    
    @staticmethod
    def first_s(serie: Serie, date_start: Date, date_end: Date) -> float:
        """First value statistic for DatCh - returns first non-missing value in range"""
        current = date_start
        while current < date_end and current <= serie.last_date:
            if current >= serie.first_date:
                try:
                    value = serie[current]
                    if value is not None and not np.isnan(value):
                        return value
                except (IndexError, KeyError):
                    pass
            next_date = serie.dating.successor(current)
            if not next_date.is_normal() or next_date <= current:
                break
            current = next_date
        return np.nan
    
    @staticmethod
    def last_s(serie: Serie, date_start: Date, date_end: Date) -> float:
        """Last value statistic for DatCh - returns last non-missing value in range"""
        result = np.nan
        current = date_start
        while current < date_end and current <= serie.last_date:
            if current >= serie.first_date:
                try:
                    value = serie[current]
                    if value is not None and not np.isnan(value):
                        result = value
                except (IndexError, KeyError):
                    pass
            next_date = serie.dating.successor(current)
            if not next_date.is_normal() or next_date <= current:
                break
            current = next_date
        return result
    
    @staticmethod
    def avr_s(serie: Serie, date_start: Date, date_end: Date) -> float:
        """Average statistic for DatCh - returns mean of values in range"""
        total = 0.0
        count = 0
        current = date_start
        while current < date_end and current <= serie.last_date:
            if current >= serie.first_date:
                try:
                    value = serie[current]
                    if value is not None and not np.isnan(value):
                        total += value
                        count += 1
                except (IndexError, KeyError):
                    pass
            next_date = serie.dating.successor(current)
            if not next_date.is_normal() or next_date <= current:
                break
            current = next_date
        return total / count if count > 0 else np.nan
    
    @staticmethod
    def sum_s_range(serie: Serie, date_start: Date, date_end: Date) -> float:
        """Sum statistic for DatCh - returns sum of values in range"""
        total = 0.0
        current = date_start
        while current < date_end and current <= serie.last_date:
            if current >= serie.first_date:
                try:
                    value = serie[current]
                    if value is not None and not np.isnan(value):
                        total += value
                except (IndexError, KeyError):
                    pass
            next_date = serie.dating.successor(current)
            if not next_date.is_normal() or next_date <= current:
                break
            current = next_date
        return total
    
    @staticmethod
    def dat_ch(serie: Serie, dating: TimeSet, 
               statistic: Optional[Callable[[Serie, Date, Date], float]] = None) -> Serie:
        """
        Dating Change function (DatCh in TOL)
        
        Transforms a serie from one dating/frequency to another by applying
        a statistical function over the source period intervals
        
        Args:
            serie: Input Serie to transform
            dating: New TimeSet/dating for the output series
            statistic: Statistical function to apply (default: avr_s)
                      Must have signature: func(serie, date_start, date_end) -> float
                      
        Returns:
            Serie with new dating
            
        Example:
            # Convert daily to monthly averages
            monthly_avg = SerieOperations.dat_ch(daily_serie, TimeSet("monthly"))
            
            # Convert daily to monthly sums
            monthly_sum = SerieOperations.dat_ch(daily_serie, TimeSet("monthly"), 
                                                SerieOperations.sum_s_range)
        """
        if statistic is None:
            statistic = SerieOperations.avr_s
        
        # Get the range of the new dating that overlaps with source serie
        # Find the first date in the new dating that contains serie.first_date
        start = serie.first_date
        # Move back to find the period start in the new dating
        test_date = dating.predecessor(start)
        while test_date.is_normal() and test_date >= serie.first_date:
            start = test_date
            test_date = dating.predecessor(test_date)
            
        # Find where to end
        end = serie.last_date
        
        # Create list to collect valid dates and values
        dates = []
        values = []
        
        current = start
        while current <= end:
            # Find next date in new dating
            next_date = dating.successor(current)
            if not next_date.is_normal():
                # Handle last period
                next_date = serie.last_date.successor()
            
            # Apply statistic over the interval [current, next_date)
            value = statistic(serie, current, next_date)
            
            if not np.isnan(value):
                dates.append(current)
                values.append(value)
            
            # Move to next period
            if next_date > end or not next_date.is_normal():
                break
            current = next_date
        
        # Create result serie
        if dates:
            result = Serie(
                data=values,
                first_date=dates[0],
                dating=dating
            )
            return result
        else:
            # Empty result
            return Serie(first_date=start, last_date=start, dating=dating)


# Add operations as methods to Serie class
def _add_operations_to_serie():
    """Add operation methods to Serie class"""
    
    # Arithmetic operations
    Serie.__add__ = lambda self, other: SerieOperations.add(self, other)
    Serie.__radd__ = lambda self, other: SerieOperations.add(self, other)
    Serie.__sub__ = lambda self, other: SerieOperations.subtract(self, other)
    Serie.__rsub__ = lambda self, other: SerieOperations.subtract(other, self)
    Serie.__mul__ = lambda self, other: SerieOperations.multiply(self, other)
    Serie.__rmul__ = lambda self, other: SerieOperations.multiply(self, other)
    Serie.__truediv__ = lambda self, other: SerieOperations.divide(self, other)
    Serie.__rtruediv__ = lambda self, other: SerieOperations.divide(other, self)
    
    # Named methods
    Serie.sum = lambda self: SerieOperations.sum_s(self)
    Serie.mean = lambda self: SerieOperations.mean_s(self)
    Serie.lag = lambda self, periods=1: SerieOperations.lag(self, periods)
    Serie.diff = lambda self, periods=1: SerieOperations.diff(self, periods)
    Serie.moving_average = lambda self, window: SerieOperations.moving_average(self, window)
    Serie.apply = lambda self, func: SerieOperations.apply(self, func)
    
    # Calendar/Dating operations
    Serie.cal_ind = staticmethod(SerieOperations.cal_ind)
    Serie.dat_ch = lambda self, dating, statistic=None: SerieOperations.dat_ch(self, dating, statistic)


# Auto-add operations when module is imported
_add_operations_to_serie()