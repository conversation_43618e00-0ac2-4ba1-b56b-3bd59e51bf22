"""
Serie class - Main time series implementation
Mirrors the BTimeSerie and BUserTimeSerie functionality from TOL
"""

from typing import Optional, Union, List, Dict, Any
import numpy as np

try:
    # Try relative imports first (when used as package)
    from ..core import Date, TimeSet, DatingType, Data
except ImportError:
    # Fallback to absolute imports (when used directly)
    from core.dates import Date, TimeSet, DatingType
    from core.data import Data


class Serie:
    """
    TOL-compatible time series class
    
    A Serie represents a time-indexed sequence of values with:
    - Flexible dating (daily, weekly, monthly, etc.)
    - Support for missing values
    - Lazy evaluation capabilities
    - Efficient memory management
    """
    
    def __init__(self, 
                 data: Optional[Union[List, np.ndarray, Data]] = None,
                 first_date: Optional[Union[Date, str]] = None,
                 last_date: Optional[Union[Date, str]] = None,
                 dating: Optional[TimeSet] = None,
                 dating_type: DatingType = DatingType.FIXED):
        """
        Initialize a Serie
        
        Args:
            data: The data values (list, numpy array, or Data object)
            first_date: First date of the series
            last_date: Last date of the series
            dating: TimeSet defining the dating pattern
            dating_type: Whether dating is fixed or volatile
        """
        # Initialize data
        if isinstance(data, Data):
            self._data = data
        elif data is not None:
            self._data = Data(data=data)
        else:
            self._data = Data()
        
        # Initialize dating
        self._dating = dating if dating is not None else TimeSet("daily")
        self._dating_type = dating_type
        
        # Initialize dates
        if isinstance(first_date, str):
            self._first_date = Date(first_date)
        elif isinstance(first_date, Date):
            self._first_date = first_date
        else:
            # If data is provided but no first_date, create a default starting date
            if data is not None and len(self._data) > 0:
                self._first_date = Date("y2000m01d01")  # Default start date
            else:
                self._first_date = Date.begin()
        
        if isinstance(last_date, str):
            self._last_date = Date(last_date)
        elif isinstance(last_date, Date):
            self._last_date = last_date
        else:
            # If data is provided but no last_date, calculate based on data length and dating
            if data is not None and len(self._data) > 0 and self._first_date.is_normal():
                # Calculate last date based on data length
                current_date = self._first_date
                for _ in range(len(self._data) - 1):
                    current_date = self._dating.successor(current_date)
                self._last_date = current_date
            else:
                self._last_date = Date.end()
        
        # Cache management
        self._first_cache = Date.begin()
        self._length = None
        self._compacted = False
    
    @property
    def dating(self) -> TimeSet:
        """Get the time set (dating pattern)"""
        return self._dating
    
    @dating.setter
    def dating(self, value: TimeSet):
        """Set the time set (dating pattern)"""
        self._dating = value
    
    @property
    def first_date(self) -> Date:
        """Get the first date"""
        return self._first_date
    
    @first_date.setter
    def first_date(self, value: Union[Date, str]):
        """Set the first date"""
        if isinstance(value, str):
            self._first_date = Date(value)
        else:
            self._first_date = value
        if self._dating and value.is_normal():
            self._first_date = self._dating.non_smaller(value)
    
    @property
    def last_date(self) -> Date:
        """Get the last date"""
        return self._last_date
    
    @last_date.setter
    def last_date(self, value: Union[Date, str]):
        """Set the last date"""
        if isinstance(value, str):
            self._last_date = Date(value)
        else:
            self._last_date = value
        if self._dating and value.is_normal():
            self._last_date = self._dating.non_greater(value)
    
    @property
    def length(self) -> int:
        """Get the length of the series"""
        if self._length is None:
            if self.first_date.is_normal() and self.last_date.is_normal():
                self._length = self._dating.distance(self.first_date, self.last_date) + 1
            else:
                self._length = len(self._data)
        return self._length
    
    def __len__(self) -> int:
        """Get the length of the series"""
        return self.length
    
    def _get_index(self, date: Date) -> int:
        """Get array index for a given date"""
        if not date.is_normal():
            raise ValueError("Cannot get index for sentinel date")
        
        if not self.first_date.is_normal():
            raise ValueError("Serie has no defined first date")
        
        index = self._dating.distance(self.first_date, date)
        if index < 0 or (self.last_date.is_normal() and date > self.last_date):
            raise IndexError(f"Date {date} is outside serie range")
        
        return index
    
    def _index_to_date(self, index: int) -> Date:
        """Convert array index to date"""
        if not self.first_date.is_normal():
            raise ValueError("Serie has no defined first date")
        
        current = self.first_date
        for _ in range(index):
            current = self._dating.successor(current)
        
        return current
    
    def __getitem__(self, key: Union[Date, str, int, slice]) -> Union[float, 'Serie']:
        """
        Get value(s) by date, index, or slice
        
        Args:
            key: Date, date string, integer index, or slice
            
        Returns:
            Single value for date/index, new Serie for slice
        """
        if isinstance(key, str):
            key = Date(key)
        
        if isinstance(key, Date):
            index = self._get_index(key)
            if index >= len(self._data):
                return None  # Missing value
            return self._data[index]
        
        elif isinstance(key, int):
            if key < 0:
                key = self.length + key
            date = self._index_to_date(key)
            return self[date]
        
        elif isinstance(key, slice):
            # Handle slice - return a new Serie
            start_idx = key.start if key.start is not None else 0
            stop_idx = key.stop if key.stop is not None else self.length
            
            start_date = self._index_to_date(start_idx)
            stop_date = self._index_to_date(stop_idx - 1)
            
            # Extract data
            new_data = []
            current = start_date
            while current <= stop_date:
                new_data.append(self[current])
                current = self._dating.successor(current)
            
            return Serie(data=new_data, 
                        first_date=start_date,
                        last_date=stop_date,
                        dating=self._dating,
                        dating_type=self._dating_type)
        
        else:
            raise TypeError(f"Invalid key type: {type(key)}")
    
    def __setitem__(self, key: Union[Date, str, int], value: Union[float, None]):
        """Set value by date or index"""
        if isinstance(key, str):
            key = Date(key)
        
        if isinstance(key, Date):
            index = self._get_index(key)
            
            # Expand data array if needed
            if index >= len(self._data):
                self._data.realloc_buffer(index + 1)
            
            self._data[index] = value
        
        elif isinstance(key, int):
            if key < 0:
                key = self.length + key
            date = self._index_to_date(key)
            self[date] = value
        
        else:
            raise TypeError(f"Invalid key type: {type(key)}")
    
    def sub_ser(self, first_date: Union[Date, str], last_date: Union[Date, str]) -> 'Serie':
        """
        Extract a subseries between two dates
        Equivalent to TOL's SubSer function
        """
        if isinstance(first_date, str):
            first_date = Date(first_date)
        if isinstance(last_date, str):
            last_date = Date(last_date)
        
        # Extract data for the date range
        data_list = []
        current = first_date
        while current <= last_date:
            try:
                data_list.append(self[current])
            except (IndexError, KeyError):
                data_list.append(None)  # Missing value
            current = self._dating.successor(current)
        
        return Serie(data=data_list,
                    first_date=first_date,
                    last_date=last_date,
                    dating=self._dating,
                    dating_type=self._dating_type)
    
    def subseries_by_date(self, first_date: Union[Date, str], last_date: Union[Date, str]) -> 'Serie':
        """
        Extract a subseries between two dates (alias for sub_ser)
        More descriptive name for better user experience
        """
        return self.sub_ser(first_date, last_date)
    
    def subseries(self, start_index: int, end_index: int) -> 'Serie':
        """
        Extract a subseries by index range
        
        Args:
            start_index: Starting index (inclusive)
            end_index: Ending index (exclusive, like Python slicing)
            
        Returns:
            Serie containing data from start_index to end_index-1
        """
        return self[start_index:end_index]
    
    def is_stochastic(self) -> bool:
        """Check if serie is stochastic (has defined date range)"""
        return self.first_date.is_normal() and self.last_date.is_normal()
    
    def is_empty(self) -> bool:
        """Check if serie is empty"""
        return len(self._data) == 0
    
    def has_missing(self) -> bool:
        """Check if serie contains any missing values"""
        if len(self._data) == 0:
            return False
        
        # Check if any values are masked or NaN
        for i in range(len(self._data)):
            if self._data.is_missing(i):
                return True
            
            # Also check for NaN values (in case they weren't properly masked)
            value = self._data[i]
            if value is not None and np.isnan(value):
                return True
        
        return False
    
    def count_missing(self) -> int:
        """Count the number of missing values in the serie"""
        if len(self._data) == 0:
            return 0
        
        count = 0
        for i in range(len(self._data)):
            if self._data.is_missing(i):
                count += 1
            else:
                # Also check for NaN values
                value = self._data[i]
                if value is not None and np.isnan(value):
                    count += 1
        
        return count
    
    def count_valid(self) -> int:
        """Count the number of valid (non-missing) values in the serie"""
        return len(self._data) - self.count_missing()
    
    def copy(self) -> 'Serie':
        """Create a deep copy of the serie"""
        return Serie(data=self._data.copy(),
                    first_date=self.first_date,
                    last_date=self.last_date,
                    dating=self._dating,
                    dating_type=self._dating_type)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert serie to dictionary"""
        result = {}
        current = self.first_date
        while current <= self.last_date:
            result[str(current)] = self[current]
            current = self._dating.successor(current)
        return result
    
    def to_pandas(self):
        """Convert to pandas Series"""
        try:
            import pandas as pd
            
            dates = []
            values = []
            current = self.first_date
            while current <= self.last_date:
                dates.append(current.to_datetime())
                values.append(self[current])
                current = self._dating.successor(current)
            
            return pd.Series(values, index=dates)
        except ImportError:
            raise ImportError("pandas is required for to_pandas() method")
    
    def __repr__(self) -> str:
        if self.is_empty():
            return "Serie(empty)"
        elif self.is_stochastic():
            return f"Serie({self.first_date} to {self.last_date}, {self.length} obs)"
        else:
            return f"Serie({self.length} obs)"
    
    def __str__(self) -> str:
        """String representation showing some data"""
        if self.is_empty():
            return "Empty Serie"
        
        lines = [f"Serie from {self.first_date} to {self.last_date}"]
        lines.append(f"Dating: {self._dating.frequency}")
        lines.append(f"Length: {self.length}")
        
        # Show first few values
        if self.is_stochastic():
            lines.append("\nFirst values:")
            current = self.first_date
            for i in range(min(5, self.length)):
                value = self[current]
                if value is None:
                    lines.append(f"  {current}: <missing>")
                else:
                    lines.append(f"  {current}: {value}")
                current = self._dating.successor(current)
            
            if self.length > 5:
                lines.append("  ...")
        
        return "\n".join(lines)
    
    def table(self, max_rows: int = None, format: str = "grid") -> str:
        """
        Display Serie data in a table format
        
        Args:
            max_rows: Maximum number of rows to display (None for all)
            format: Table format - "grid", "plain", "simple", "github", "pipe"
            
        Returns:
            Formatted table string
        """
        try:
            from tabulate import tabulate
        except ImportError:
            # Fallback to simple formatting without tabulate
            return self._simple_table(max_rows)
        
        # Collect data
        dates = []
        values = []
        current = self.first_date
        count = 0
        
        while current <= self.last_date:
            if max_rows and count >= max_rows:
                break
                
            dates.append(str(current))
            value = self[current]
            if value is None or (hasattr(value, '__iter__') and len(str(value)) == 0):
                values.append("<missing>")
            else:
                values.append(value)
            
            current = self._dating.successor(current)
            count += 1
        
        # Create table data
        table_data = list(zip(dates, values))
        headers = ["Date", "Value"]
        
        # Add summary info if truncated
        title_info = f"Serie Table ({len(dates)} of {self.length} rows shown)" if max_rows and self.length > max_rows else f"Serie Table ({len(dates)} rows)"
        
        table_str = tabulate(table_data, headers=headers, tablefmt=format, floatfmt=".6g")
        
        return f"{title_info}\n{'-' * len(title_info)}\n{table_str}"
    
    def _simple_table(self, max_rows: int = None) -> str:
        """Simple table formatting without tabulate dependency"""
        lines = [f"Serie Table ({self.length} rows)"]
        lines.append("-" * 40)
        lines.append(f"{'Date':<15} {'Value':<15}")
        lines.append("-" * 40)
        
        current = self.first_date
        count = 0
        
        while current <= self.last_date:
            if max_rows and count >= max_rows:
                lines.append("...")
                break
                
            value = self[current]
            if value is None:
                value_str = "<missing>"
            elif isinstance(value, float) and (value != value):  # Check for NaN
                value_str = "<missing>"
            elif isinstance(value, (int, float)):
                value_str = f"{value:g}"
            else:
                value_str = str(value)
            lines.append(f"{str(current):<15} {value_str:<15}")
            
            current = self._dating.successor(current)
            count += 1
        
        return "\n".join(lines)
    
    def chart(self, title: str = None, figsize: tuple = (10, 6), 
              style: str = 'line', save_path: str = None, show: bool = True) -> None:
        """
        Plot Serie data as a chart
        
        Args:
            title: Chart title (default: auto-generated)
            figsize: Figure size as (width, height) 
            style: Chart style - 'line', 'bar', 'scatter', 'area'
            save_path: Path to save chart (optional)
            show: Whether to display the chart
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
        except ImportError:
            raise ImportError("matplotlib is required for chart() method. Install with: pip install matplotlib")
        
        # Collect data
        dates = []
        values = []
        current = self.first_date
        
        while current <= self.last_date:
            dates.append(current.to_datetime())
            value = self[current]
            # Handle missing values
            values.append(value if value is not None else float('nan'))
            current = self._dating.successor(current)
        
        # Create the plot
        fig, ax = plt.subplots(figsize=figsize)
        
        # Plot based on style
        if style == 'line':
            ax.plot(dates, values, marker='o', markersize=3, linewidth=1.5)
        elif style == 'bar':
            ax.bar(dates, values, width=1)
        elif style == 'scatter':
            ax.scatter(dates, values, alpha=0.7)
        elif style == 'area':
            ax.fill_between(dates, values, alpha=0.7)
        else:
            raise ValueError(f"Unknown style: {style}. Use 'line', 'bar', 'scatter', or 'area'")
        
        # Formatting
        if title is None:
            title = f"Serie Chart ({self.first_date} to {self.last_date})"
        ax.set_title(title)
        ax.set_xlabel("Date")
        ax.set_ylabel("Value")
        
        # Format x-axis dates
        if len(dates) > 20:
            ax.xaxis.set_major_locator(mdates.MonthLocator())
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        else:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        
        # Rotate labels for better readability
        plt.xticks(rotation=45)
        
        # Grid for better readability
        ax.grid(True, alpha=0.3)
        
        # Tight layout to prevent label cutoff
        plt.tight_layout()
        
        # Save if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Chart saved to: {save_path}")
        
        # Show if requested
        if show:
            plt.show()
        
        return fig, ax
    
    def _get_all_dates(self):
        """Helper method to get all dates in the serie"""
        dates = []
        current = self.first_date
        while current <= self.last_date:
            dates.append(current)
            current = self._dating.successor(current)
        return dates