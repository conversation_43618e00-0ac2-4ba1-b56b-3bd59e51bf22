"""
Corrected Serie implementation matching C++ BSerie architecture
Based on analysis of TOL C++ codebase for optimal performance
"""

import numpy as np
from typing import Union, Optional, Callable, Any
from abc import ABC, abstractmethod

try:
    from ..core import Date, TimeSet, DatingType
except ImportError:
    from core.dates import Date, TimeSet, DatingType


class SerieBase(ABC):
    """Abstract base class matching BSerie structure"""
    
    def __init__(self, first_date=None, last_date=None, dating=None):
        self._first_date = first_date
        self._last_date = last_date  
        self._dating = dating
        
        # C++ pattern: lazy data loading
        self._data: Optional[np.ndarray] = None
        self._is_data_loaded = False
        self._data_loader: Optional[Callable] = None
        
        # Cache management like C++
        self._length_cache: Optional[int] = None
        self._is_compacted = False
    
    @abstractmethod
    def _load_data_impl(self):
        """Load data implementation - override in subclasses"""
        pass
    
    def _ensure_data_loaded(self):
        """Lazy loading mechanism (C++ pattern)"""
        if not self._is_data_loaded:
            if self._data_loader:
                self._load_data(self._data_loader())
            else:
                self._load_data_impl()
    
    def _load_data(self, data):
        """Load data into NumPy array (like C++ ReadData())"""
        if isinstance(data, (list, tuple)):
            self._data = np.array(data, dtype=np.float64)
        elif isinstance(data, np.ndarray):
            self._data = data.astype(np.float64)
        else:
            raise ValueError("Data must be list, tuple, or numpy array")
        
        self._is_data_loaded = True
        self._compact_data()  # C++ optimization
    
    def _compact_data(self):
        """Remove unknown values at edges (C++ CompactData())"""
        if self._data is None:
            return
        
        # Find first valid index
        first_valid = 0
        while first_valid < len(self._data) and np.isnan(self._data[first_valid]):
            first_valid += 1
        
        # Find last valid index
        last_valid = len(self._data) - 1
        while last_valid >= 0 and np.isnan(self._data[last_valid]):
            last_valid -= 1
        
        if first_valid <= last_valid:
            # Compact data
            self._data = self._data[first_valid:last_valid+1]
            
            # Update date range (C++ pattern)
            for _ in range(first_valid):
                self._first_date = self._dating.successor(self._first_date)
            
            end_reductions = len(self._data) - 1 - last_valid + first_valid
            for _ in range(end_reductions):
                self._last_date = self._dating.predecessor(self._last_date)
        
        self._is_compacted = True
    
    def _get_index(self, date: Date) -> int:
        """Efficient date-to-index conversion (like C++ GetIndex())"""
        if not (self._first_date <= date <= self._last_date):
            raise IndexError(f"Date {date} outside serie range")
        
        # Use TimeSet's distance method (C++ Difference equivalent)
        return self._dating.distance(self._first_date, date)
    
    def __getitem__(self, date: Date) -> float:
        """Optimized data access (like C++ operator[])"""
        if not self._is_data_loaded:
            self._ensure_data_loaded()
        
        try:
            index = self._get_index(date)
            return float(self._data[index])
        except (IndexError, TypeError):
            return np.nan
    
    def __setitem__(self, date: Date, value: float):
        """Efficient date-based assignment"""
        if not self._is_data_loaded:
            self._ensure_data_loaded()
        
        index = self._get_index(date)
        self._data[index] = value
    
    def length(self) -> int:
        """Get series length with caching"""
        if self._length_cache is None:
            if self._is_data_loaded:
                self._length_cache = len(self._data)
            else:
                # Calculate from date range
                self._length_cache = self._dating.distance(self._first_date, self._last_date) + 1
        return self._length_cache
    
    @property
    def first_date(self) -> Date:
        return self._first_date
    
    @property 
    def last_date(self) -> Date:
        return self._last_date
    
    @property
    def dating(self) -> TimeSet:
        return self._dating
    
    def table(self, output_file: str = None) -> str:
        """
        Generate table output for this Serie - matches C++ Table method
        
        Args:
            output_file: Output file path, "Std" for stdout, or None to return string
            
        Returns:
            BDT format table string
        """
        table = SerieTable()
        table.add_serie(self, "Serie1")
        return table.to_bdt_format(output_file)


class Serie(SerieBase):
    """Corrected Serie implementation matching BSerie architecture"""
    
    def __init__(self, data=None, first_date=None, last_date=None, dating=None):
        super().__init__(first_date, last_date, dating)
        
        # Set default dating if not provided
        if self._dating is None:
            self._dating = TimeSet("daily")
        
        # Set default dates if not provided
        if self._first_date is None:
            self._first_date = Date("y2000m01d01")
        if self._last_date is None and data is not None:
            # Calculate last date from data length
            length = len(data) if hasattr(data, '__len__') else 1
            current = self._first_date
            for _ in range(length - 1):
                current = self._dating.successor(current)
            self._last_date = current
        elif self._last_date is None:
            self._last_date = self._first_date
        
        # Load data if provided
        if data is not None:
            self._load_data(data)
    
    def _load_data_impl(self):
        """Default data loading - initialize empty array"""
        if not self._is_data_loaded:
            length = self._dating.distance(self._first_date, self._last_date) + 1
            self._data = np.full(length, np.nan)
            self._is_data_loaded = True


class IndicatorSerie(SerieBase):
    """Lazy CalInd implementation matching C++ BTsrIndicator"""
    
    def __init__(self, center_timeset: TimeSet, dating: TimeSet, 
                 start_date: Date, end_date: Date):
        # Don't initialize data - use lazy evaluation
        super().__init__(first_date=start_date, last_date=end_date, dating=dating)
        self.center = center_timeset
        self._is_lazy = True
    
    def _load_data_impl(self):
        """Don't pre-load data - maintain lazy evaluation"""
        pass
    
    def __getitem__(self, date: Date) -> float:
        """Lazy evaluation (C++ GetDat pattern)"""
        if not self._is_in_domain(date):
            return np.nan
        
        # Direct containment check (no pre-computation)
        return 1.0 if self.center.includes(date) else 0.0
    
    def _is_in_domain(self, date: Date) -> bool:
        """Check if date is in serie domain"""
        return (self._first_date <= date <= self._last_date and 
                self._dating.includes(date))


class SerieTable:
    """
    BSerieTable equivalent - handles tabular output of multiple series
    Matches C++ BSerieTable functionality from tol_btsrgrp.h
    """
    
    def __init__(self, series_list=None):
        self.series = series_list if series_list else []
        self.series_names = []
        
        # Computed during Fill()
        self.dates = []
        self.data_matrix = None
        self.first_date = None
        self.last_date = None
    
    def add_serie(self, serie: SerieBase, name: str = None):
        """Add a serie to the table"""
        self.series.append(serie)
        if name is None:
            name = f"Serie{len(self.series)}"
        self.series_names.append(name)
    
    def fill(self, first_date: Date = None, last_date: Date = None):
        """
        Fill the table with data - equivalent to C++ BSerieTable::Fill()
        Creates aligned data matrix with dates as rows, series as columns
        """
        if not self.series:
            return
        
        # Determine date range
        if first_date is None or last_date is None:
            # Find intersection of all series date ranges
            min_first = min(s.first_date for s in self.series)
            max_last = max(s.last_date for s in self.series)
            
            self.first_date = first_date if first_date else min_first
            self.last_date = last_date if last_date else max_last
        else:
            self.first_date = first_date
            self.last_date = last_date
        
        # Get common dating (use first series dating as reference)
        dating = self.series[0].dating
        
        # Create date array
        self.dates = []
        current = self.first_date
        while current <= self.last_date:
            if dating.includes(current):
                self.dates.append(current)
            current = dating.successor(current)
        
        # Create data matrix (dates x series)
        num_dates = len(self.dates)
        num_series = len(self.series)
        self.data_matrix = np.full((num_dates, num_series), np.nan)
        
        # Fill matrix with data from each serie
        for s_idx, serie in enumerate(self.series):
            for d_idx, date in enumerate(self.dates):
                try:
                    value = serie[date]
                    if not np.isnan(value):
                        self.data_matrix[d_idx, s_idx] = value
                except (IndexError, KeyError):
                    # Missing value - leave as NaN
                    pass
    
    def to_bdt_format(self, output_file=None):
        """
        Generate BDT (Bayes Data Table) format output
        Matches C++ BSerieTable output format
        """
        if self.data_matrix is None:
            self.fill()
        
        lines = []
        
        # Header line: Dating + Serie names
        dating_str = self.series[0].dating.frequency if self.series else "daily"
        header = [dating_str] + self.series_names
        lines.append("\t".join(header))
        
        # Data lines: Date + values for each serie
        for d_idx, date in enumerate(self.dates):
            row = [str(date)]
            for s_idx in range(len(self.series)):
                value = self.data_matrix[d_idx, s_idx]
                if np.isnan(value):
                    row.append("?")  # TOL unknown value marker
                else:
                    row.append(f"{value:.6g}")
            lines.append("\t".join(row))
        
        result = "\n".join(lines)
        
        # Output to file or return string
        if output_file:
            if output_file.lower() == "std":
                print(result)
            else:
                with open(output_file, 'w') as f:
                    f.write(result)
        
        return result


def cal_ind(timeset: TimeSet, dating: TimeSet, 
           start_date: Date, end_date: Date) -> SerieBase:
    """Corrected CalInd implementation"""
    return IndicatorSerie(timeset, dating, start_date, end_date)