{"name": "@jupyterlab/metadataform-extension", "version": "4.4.5", "description": "A helper to build form for metadata", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "schema/*.json", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -w --listEmittedFiles"}, "dependencies": {"@jupyterlab/application": "^4.4.5", "@jupyterlab/metadataform": "^4.4.5", "@jupyterlab/notebook": "^4.4.5", "@jupyterlab/settingregistry": "^4.4.5", "@jupyterlab/translation": "^4.4.5", "@jupyterlab/ui-components": "^4.4.5", "@lumino/coreutils": "^2.2.1"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}