Metadata-Version: 2.4
Name: sphinxext-opengraph
Version: 0.12.0
Summary: Sphinx Extension to enable OGP support
Author-email: Itay Ziv <<EMAIL>>
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-Expression: BSD-3-Clause
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Plugins
Classifier: Environment :: Web Environment
Classifier: Framework :: Sphinx :: Extension
Classifier: Intended Audience :: Developers
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Programming Language :: Python
Classifier: Topic :: Documentation :: Sphinx
Classifier: Topic :: Documentation
Classifier: Topic :: Software Development :: Documentation
Classifier: Topic :: Text Processing
Classifier: Topic :: Utilities
License-File: LICENCE.rst
Requires-Dist: Sphinx>=6.0
Requires-Dist: furo>=2024 ; extra == "rtd"
Requires-Dist: sphinx-design ; extra == "rtd"
Requires-Dist: sphinx~=8.1.0 ; extra == "rtd"
Requires-Dist: matplotlib>=3 ; extra == "social-cards"
Project-URL: Code, https://github.com/sphinx-doc/sphinxext-opengraph/
Project-URL: Documentation, https://sphinxext-opengraph.readthedocs.io/
Project-URL: Download, https://pypi.org/project/sphinxext-opengraph/
Project-URL: Homepage, https://github.com/sphinx-doc/sphinxext-opengraph/
Project-URL: Issue tracker, https://github.com/sphinx-doc/sphinxext-opengraph/issues
Provides-Extra: rtd
Provides-Extra: social-cards

===================
sphinxext-opengraph
===================

.. image:: https://img.shields.io/pypi/v/sphinxext-opengraph.svg
   :target: https://pypi.org/project/sphinxext-opengraph/
   :alt: Package on PyPI

.. image:: https://github.com/sphinx-doc/sphinxext-opengraph/actions/workflows/test.yml/badge.svg
   :target: https://github.com/sphinx-doc/sphinxext-opengraph/actions
   :alt: Build Status

.. image:: https://img.shields.io/badge/License-BSD%203--Clause-blue.svg
   :target: https://opensource.org/licenses/BSD-3-Clause
   :alt: BSD 3 Clause

Sphinx extension to generate `Open Graph metadata`_
for each page of your documentation.

Installation
============

.. code-block:: sh

   python -m pip install sphinxext-opengraph[social-cards]
   # or
   uv pip install sphinxext-opengraph[social-cards]

Usage
=====

See the `documentation`_.

.. _Open Graph metadata: https://ogp.me/
.. _documentation: https://sphinxext-opengraph.readthedocs.io/

