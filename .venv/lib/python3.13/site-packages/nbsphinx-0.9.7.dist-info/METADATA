Metadata-Version: 2.2
Name: nbsphinx
Version: 0.9.7
Summary: <PERSON><PERSON><PERSON> Notebook Tools for Sphinx
Home-page: https://nbsphinx.readthedocs.io/
Author: <PERSON>
Author-email: <PERSON><PERSON>@gmail.com
License: MIT
Project-URL: Documentation, https://nbsphinx.readthedocs.io/
Project-URL: Source Code, https://github.com/spatialaudio/nbsphinx/
Project-URL: Bug Tracker, https://github.com/spatialaudio/nbsphinx/issues/
Keywords: Sphinx,<PERSON>py<PERSON>,notebook
Platform: any
Classifier: Framework :: Sphinx
Classifier: Framework :: Sphinx :: Extension
Classifier: Framework :: Jupyter
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Documentation :: Sphinx
Requires-Python: >=3.6
License-File: LICENSE
Requires-Dist: docutils>=0.18.1
Requires-Dist: jinja2
Requires-Dist: nbconvert!=5.4,>=5.3
Requires-Dist: traitlets>=5
Requires-Dist: nbformat
Requires-Dist: sphinx<8.2,>=1.8
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: platform
Dynamic: project-url
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Jupyter Notebook Tools for Sphinx
=================================

``nbsphinx`` is a Sphinx_ extension that provides a source parser for
``*.ipynb`` files.
Custom Sphinx directives are used to show `Jupyter Notebook`_ code cells (and of
course their results) in both HTML and LaTeX output.
Un-evaluated notebooks -- i.e. notebooks without stored output cells -- will be
automatically executed during the Sphinx build process.

Quick Start:
    #. Install ``nbsphinx``

    #. Edit your ``conf.py`` and add ``'nbsphinx'`` to ``extensions``.

    #. Edit your ``index.rst`` and add the names of your ``*.ipynb`` files
       to the ``toctree``.

    #. Run Sphinx!

Online documentation (and example of use):
    https://nbsphinx.readthedocs.io/

Source code repository (and issue tracker):
    https://github.com/spatialaudio/nbsphinx/

License:
    MIT -- see the file ``LICENSE`` for details.

.. _Sphinx: https://www.sphinx-doc.org/
.. _Jupyter Notebook: https://jupyter.org/
