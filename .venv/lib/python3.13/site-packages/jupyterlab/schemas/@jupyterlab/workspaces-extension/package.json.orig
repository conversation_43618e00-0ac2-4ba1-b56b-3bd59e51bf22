{"name": "@jupyterlab/workspaces-extension", "version": "4.4.5", "description": "JupyterLab Extension providing UI for workspace management", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "schema/*.json", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "src/**/*.{ts,tsx}", "style/index.js"], "scripts": {"build": "tsc -b", "build:test": "tsc --build tsconfig.test.json", "clean": "rimraf lib tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.4.5", "@jupyterlab/apputils": "^4.5.5", "@jupyterlab/coreutils": "^6.4.5", "@jupyterlab/filebrowser": "^4.4.5", "@jupyterlab/running": "^4.4.5", "@jupyterlab/services": "^7.4.5", "@jupyterlab/settingregistry": "^4.4.5", "@jupyterlab/statedb": "^4.4.5", "@jupyterlab/translation": "^4.4.5", "@jupyterlab/ui-components": "^4.4.5", "@jupyterlab/workspaces": "^4.4.5", "react": "^18.2.0"}, "devDependencies": {"@types/jest": "^29.2.0", "rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}