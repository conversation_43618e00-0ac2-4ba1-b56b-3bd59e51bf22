"""
Comprehensive test suite for ARIMA implementation
Tests all components: ARIMAFactor, ARIMA estimation, and forecasting
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from tol_python.series import Serie
from tol_python.arima import ARIMAFactor, ARIMA, ARIMAEstimator
from tol_python.core import Date


def raises(exception_type):
    """Simple context manager to check for exceptions"""
    class ExceptionChecker:
        def __enter__(self):
            return self
        def __exit__(self, exc_type, exc_val, exc_tb):
            if exc_type is None:
                raise AssertionError(f"Expected {exception_type.__name__} but no exception was raised")
            if not issubclass(exc_type, exception_type):
                raise AssertionError(f"Expected {exception_type.__name__} but got {exc_type.__name__}")
            return True
    return ExceptionChecker()


class TestARIMAFactor:
    """Test ARIMAFactor model specification"""
    
    def test_basic_creation(self):
        """Test basic ARIMAFactor creation"""
        factor = ARIMAFactor(ar_order=2, diff_order=1, ma_order=1)
        
        assert factor.ar_order == 2
        assert factor.diff_order == 1
        assert factor.ma_order == 1
        assert factor.seasonal_ar == 0
        assert factor.seasonal_diff == 0
        assert factor.seasonal_ma == 0
        assert factor.season_length == 1
    
    def test_from_order(self):
        """Test creation from order tuples"""
        factor = ARIMAFactor.from_order((2, 1, 1))
        assert factor.order == (2, 1, 1)
        
        factor_seasonal = ARIMAFactor.from_order((1, 1, 1), (1, 1, 1, 12))
        assert factor_seasonal.order == (1, 1, 1)
        assert factor_seasonal.seasonal_order == (1, 1, 1, 12)
    
    def test_validation(self):
        """Test parameter validation"""
        # Valid factor
        factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1)
        assert factor.ar_order == 1
        
        # Invalid AR order
        with raises(ValueError):
            ARIMAFactor(ar_order=-1)
        
        # Invalid season length for seasonal model
        with raises(ValueError):
            ARIMAFactor(seasonal_ar=1, season_length=0)
        
        # Too much differencing
        with raises(ValueError):
            ARIMAFactor(diff_order=2, seasonal_diff=1)
    
    def test_properties(self):
        """Test factor properties"""
        # Non-seasonal model
        factor = ARIMAFactor(ar_order=2, diff_order=1, ma_order=1)
        assert not factor.is_seasonal
        assert not factor.is_stationary
        assert factor.num_ar_params == 2
        assert factor.num_ma_params == 1
        assert factor.num_diff_operations == 1
        
        # Seasonal model
        factor_seasonal = ARIMAFactor(ar_order=1, seasonal_ar=1, season_length=12)
        assert factor_seasonal.is_seasonal
        assert factor_seasonal.is_stationary
        assert factor_seasonal.num_ar_params == 2  # 1 regular + 1 seasonal
    
    def test_polynomial_orders(self):
        """Test polynomial lag order generation"""
        factor = ARIMAFactor(ar_order=2, ma_order=1, seasonal_ar=1, seasonal_ma=1, season_length=12)
        
        ar_lags, ma_lags = factor.get_polynomial_orders()
        
        # AR lags: 1, 2 (regular) + 12 (seasonal)
        assert ar_lags == [1, 2, 12]
        
        # MA lags: 1 (regular) + 12 (seasonal)
        assert ma_lags == [1, 12]
    
    def test_required_observations(self):
        """Test minimum observation requirements"""
        factor = ARIMAFactor(ar_order=2, diff_order=1, ma_order=1)
        required = factor.required_observations()
        
        # Should be at least max_lag + num_params + buffer
        assert required >= factor.max_lag() + factor.num_params + 10
        assert required >= 30  # Minimum threshold
    
    def test_model_string(self):
        """Test model string representation"""
        # Simple ARIMA
        factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1)
        assert factor.get_model_string() == "ARIMA(1,1,1) with mean"
        
        # Seasonal ARIMA
        factor_seasonal = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1,
                                    seasonal_ar=1, seasonal_diff=1, seasonal_ma=1,
                                    season_length=12)
        expected = "ARIMA(1,1,1)(1,1,1)[12] with drift"
        assert factor_seasonal.get_model_string() == expected


class TestARIMAEstimation:
    """Test ARIMA parameter estimation"""
    
    def setup_method(self):
        """Create test data"""
        # Generate AR(1) process: y_t = 0.7 * y_{t-1} + ε_t
        np.random.seed(42)
        n = 200
        phi = 0.7
        sigma = 1.0
        
        y = np.zeros(n)
        errors = np.random.normal(0, sigma, n)
        
        for t in range(1, n):
            y[t] = phi * y[t-1] + errors[t]
        
        self.ar1_data = Serie(data=y)
        self.true_phi = phi
        self.true_sigma = sigma
    
    def test_ar1_yule_walker(self):
        """Test AR(1) estimation using Yule-Walker"""
        factor = ARIMAFactor(ar_order=1, include_mean=False)
        estimator = ARIMAEstimator(factor)
        
        results = estimator.fit(self.ar1_data, method="yule_walker")
        
        # Check convergence
        assert results.converged
        
        # Check parameter estimate (should be close to 0.7)
        assert len(results.ar_params) == 1
        estimated_phi = results.ar_params[0]
        assert abs(estimated_phi - self.true_phi) < 0.1
        
        # Check variance estimate
        assert results.sigma2 > 0
        
        # Check fitted values and residuals
        assert results.fitted_values is not None
        assert results.residuals is not None
    
    def test_ar1_css(self):
        """Test AR(1) estimation using CSS"""
        factor = ARIMAFactor(ar_order=1, include_mean=False)
        estimator = ARIMAEstimator(factor)
        
        results = estimator.fit(self.ar1_data, method="css")
        
        # Check convergence
        assert results.converged
        
        # Check parameter estimate
        assert len(results.ar_params) == 1
        estimated_phi = results.ar_params[0]
        assert abs(estimated_phi - self.true_phi) < 0.15  # CSS may be less accurate
        
        # Check that we have fitted values and residuals
        assert results.fitted_values is not None
        assert results.residuals is not None
    
    def test_arma11_estimation(self):
        """Test ARMA(1,1) estimation"""
        # Generate ARMA(1,1) process
        np.random.seed(123)
        n = 300
        phi = 0.6
        theta = 0.4
        sigma = 1.0
        
        y = np.zeros(n)
        errors = np.random.normal(0, sigma, n)
        
        for t in range(1, n):
            y[t] = phi * y[t-1] + errors[t] + theta * errors[t-1]
        
        data = Serie(data=y)
        
        factor = ARIMAFactor(ar_order=1, ma_order=1, include_mean=False)
        estimator = ARIMAEstimator(factor)
        
        results = estimator.fit(data, method="css")
        
        # Check that we get reasonable estimates
        assert len(results.ar_params) == 1
        assert len(results.ma_params) == 1
        
        # Parameters should be in reasonable range
        assert abs(results.ar_params[0]) < 1.0
        assert abs(results.ma_params[0]) < 1.0
    
    def test_arima_with_differencing(self):
        """Test ARIMA with differencing"""
        # Generate I(1) process
        np.random.seed(456)
        n = 200
        sigma = 1.0
        
        # Random walk with drift
        drift = 0.1
        errors = np.random.normal(0, sigma, n)
        y = np.cumsum(errors) + np.arange(n) * drift
        
        data = Serie(data=y)
        
        factor = ARIMAFactor(ar_order=0, diff_order=1, ma_order=0, include_drift=True)
        estimator = ARIMAEstimator(factor)
        
        results = estimator.fit(data, method="css")
        
        # Should have differenced series
        assert results.differenced_series is not None
        
        # Drift estimate should be positive
        assert results.intercept is not None
        assert results.intercept > 0
    
    def test_seasonal_arima(self):
        """Test seasonal ARIMA estimation"""
        # Generate simple seasonal AR process
        np.random.seed(789)
        n = 120  # 10 years of monthly data
        seasonal_phi = 0.5
        sigma = 1.0
        season_length = 12
        
        y = np.zeros(n)
        errors = np.random.normal(0, sigma, n)
        
        for t in range(season_length, n):
            y[t] = seasonal_phi * y[t - season_length] + errors[t]
        
        data = Serie(data=y)
        
        factor = ARIMAFactor(seasonal_ar=1, season_length=12, include_mean=False)
        estimator = ARIMAEstimator(factor)
        
        results = estimator.fit(data, method="css")
        
        # Should have one seasonal AR parameter
        assert len(results.ar_params) == 1
        assert abs(results.ar_params[0]) < 1.0  # Should be stationary


class TestARIMAModel:
    """Test complete ARIMA model functionality"""
    
    def setup_method(self):
        """Create test data"""
        np.random.seed(42)
        n = 150
        
        # AR(1) process
        phi = 0.7
        sigma = 1.0
        y = np.zeros(n)
        errors = np.random.normal(0, sigma, n)
        
        for t in range(1, n):
            y[t] = phi * y[t-1] + errors[t]
        
        self.test_data = Serie(data=y)
    
    def test_arima_fit_and_summary(self):
        """Test ARIMA model fitting and summary"""
        factor = ARIMAFactor(ar_order=1, include_mean=False)
        model = ARIMA(factor)
        
        # Fit model
        results = model.fit(self.test_data, method="yule_walker")
        
        # Check that model has results
        assert model.results is not None
        assert model.results.converged
        
        # Test summary
        summary = model.summary()
        assert "ARIMA Model Results" in summary
        assert "ARIMA(1,0,0)" in summary
        assert "Yule-Walker" in summary.upper()
    
    def test_arima_forecasting(self):
        """Test ARIMA forecasting"""
        factor = ARIMAFactor(ar_order=1, include_mean=False)
        model = ARIMA(factor)
        
        # Fit model
        model.fit(self.test_data, method="yule_walker")
        
        # Generate forecasts
        steps = 10
        forecasts, lower, upper = model.forecast(steps)
        
        # Check forecast properties
        assert len(forecasts) == steps
        assert len(lower) == steps
        assert len(upper) == steps
        
        # Confidence intervals should make sense
        forecast_data = forecasts._data.to_numpy()
        lower_data = lower._data.to_numpy()
        upper_data = upper._data.to_numpy()
        
        # Remove any NaN values for comparison
        valid_idx = ~(np.isnan(forecast_data) | np.isnan(lower_data) | np.isnan(upper_data))
        
        if np.any(valid_idx):
            assert np.all(lower_data[valid_idx] <= forecast_data[valid_idx])
            assert np.all(forecast_data[valid_idx] <= upper_data[valid_idx])
    
    def test_arima_prediction(self):
        """Test ARIMA in-sample and out-of-sample prediction"""
        factor = ARIMAFactor(ar_order=1, include_mean=False)
        model = ARIMA(factor)
        
        # Fit model
        model.fit(self.test_data, method="css")
        
        # In-sample prediction
        fitted = model.predict(in_sample=True)
        assert fitted is not None
        
        # Out-of-sample prediction
        predictions = model.predict(in_sample=False)
        assert predictions is not None
        assert len(predictions) == 10  # Default 10 periods
    
    def test_model_comparison(self):
        """Test comparing different ARIMA specifications"""
        models = []
        aics = []
        
        # Test different AR orders
        for p in range(3):
            factor = ARIMAFactor(ar_order=p, include_mean=False)
            model = ARIMA(factor)
            
            try:
                results = model.fit(self.test_data, method="css")
                models.append(model)
                aics.append(results.aic)
            except:
                continue
        
        # Should have some successful fits
        assert len(models) > 0
        
        # AIC values should be finite for successful fits
        finite_aics = [aic for aic in aics if np.isfinite(aic)]
        assert len(finite_aics) > 0


class TestARIMAIntegration:
    """Test integration with other TOL Python components"""
    
    def test_with_dates(self):
        """Test ARIMA with dated time series"""
        # Create monthly data
        dates = [Date(f"y2020m{m:02d}d01") for m in range(1, 25)]  # 2 years
        
        # Generate AR(1) data
        np.random.seed(42)
        n = len(dates)
        phi = 0.6
        y = np.zeros(n)
        errors = np.random.normal(0, 1, n)
        
        for t in range(1, n):
            y[t] = phi * y[t-1] + errors[t]
        
        serie = Serie(data=y, first_date=dates[0], last_date=dates[-1])
        
        # Fit ARIMA model
        factor = ARIMAFactor(ar_order=1, include_mean=False)
        model = ARIMA(factor)
        results = model.fit(serie, method="yule_walker")
        
        # Should work with dated series
        assert results.converged
        assert results.original_series.is_stochastic()
        
        # Forecasting should produce dated forecasts
        forecasts, _, _ = model.forecast(6)  # 6 months ahead
        assert forecasts.is_stochastic()
    
    def test_missing_values(self):
        """Test ARIMA with missing values"""
        # Create data with missing values
        np.random.seed(42)
        n = 100
        phi = 0.7
        y = np.zeros(n)
        errors = np.random.normal(0, 1, n)
        
        for t in range(1, n):
            y[t] = phi * y[t-1] + errors[t]
        
        # Introduce missing values
        y[10:15] = np.nan
        y[50:55] = np.nan
        
        serie = Serie(data=y)
        
        # Should handle missing values gracefully
        factor = ARIMAFactor(ar_order=1, include_mean=False)
        model = ARIMA(factor)
        
        try:
            results = model.fit(serie, method="css")
            # If fit succeeds, it should have handled missing values
            assert results is not None
        except ValueError as e:
            # Or it should give a clear error about insufficient data
            assert "data" in str(e).lower()


def test_example_workflow():
    """Test a complete ARIMA modeling workflow"""
    # Generate realistic economic data (GDP-like)
    np.random.seed(42)
    n = 100
    
    # Generate I(1) process with drift (trending GDP)
    drift = 0.02  # 2% quarterly growth
    sigma = 0.01  # 1% standard deviation
    errors = np.random.normal(0, sigma, n)
    
    # Log GDP with unit root and AR component
    log_gdp = np.zeros(n)
    phi = 0.3  # Some persistence in growth rates
    
    for t in range(1, n):
        log_gdp[t] = log_gdp[t-1] + drift + phi * (log_gdp[t-1] - log_gdp[t-2] - drift) + errors[t]
    
    # Convert to levels (GDP)
    gdp = np.exp(log_gdp) * 1000  # Scale to realistic values
    
    serie = Serie(data=gdp)
    
    # Step 1: Test for unit root (would need ADF test, but check if differencing helps)
    gdp_growth = serie.diff()  # Growth rates
    
    # Step 2: Fit ARIMA(1,1,0) model
    factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=0, include_drift=True)
    model = ARIMA(factor)
    
    results = model.fit(serie, method="css")
    
    # Step 3: Check results
    assert results.converged
    assert results.intercept > 0  # Positive drift
    
    # Step 4: Generate forecasts
    forecasts, lower, upper = model.forecast(8)  # 2 years ahead
    
    # Forecasts should be reasonable
    forecast_values = forecasts._data.to_numpy()
    forecast_values = forecast_values[~np.isnan(forecast_values)]
    
    if len(forecast_values) > 0:
        # Growth should be positive (trending upward)
        assert np.all(np.diff(forecast_values) > -0.1)  # Allow for small declines
    
    # Step 5: Model summary
    summary = results.summary()
    assert "ARIMA(1,1,0)" in summary
    assert "drift" in summary
    
    print("ARIMA Workflow Test Results:")
    print(f"Model: {factor.get_model_string()}")
    print(f"AR parameter: {results.ar_params[0]:.3f}")
    print(f"Drift: {results.intercept:.4f}")
    print(f"Sigma²: {results.sigma2:.6f}")
    print(f"AIC: {results.aic:.2f}")


if __name__ == "__main__":
    # Run all tests
    print("Testing ARIMA implementation...")
    
    # Run basic tests
    test_factor = TestARIMAFactor()
    test_factor.test_basic_creation()
    test_factor.test_validation()
    test_factor.test_properties()
    print("✓ ARIMAFactor tests passed")
    
    test_estimation = TestARIMAEstimation()
    test_estimation.setup_method()
    test_estimation.test_ar1_yule_walker()
    test_estimation.test_ar1_css()
    print("✓ ARIMA estimation tests passed")
    
    test_model = TestARIMAModel()
    test_model.setup_method()
    test_model.test_arima_fit_and_summary()
    test_model.test_arima_forecasting()
    print("✓ ARIMA model tests passed")
    
    test_integration = TestARIMAIntegration()
    test_integration.test_with_dates()
    print("✓ ARIMA integration tests passed")
    
    # Run example workflow
    test_example_workflow()
    print("✓ Complete workflow test passed")
    
    print("\nAll ARIMA tests completed successfully! 🎉")
    print("ARIMA implementation is ready for production use.")