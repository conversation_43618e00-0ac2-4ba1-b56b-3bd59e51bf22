# TOL Python User Guide
## Complete Guide to High-Performance Time Series Analysis

### Table of Contents
1. [Introduction](#introduction)
2. [Installation & Setup](#installation--setup)
3. [Architecture & Performance](#architecture--performance)
4. [Core Concepts](#core-concepts)
5. [Basic Time Series Operations](#basic-time-series-operations)
6. [Statistical Analysis](#statistical-analysis)
7. [ARIMA Modeling](#arima-modeling)
8. [Bayesian Framework](#bayesian-framework)
9. [Advanced Bayesian ARIMA](#advanced-bayesian-arima)
10. [Model Comparison & Selection](#model-comparison--selection)
11. [Real-World Examples](#real-world-examples)
12. [Performance Guide](#performance-guide)
13. [TOL vs Python Comparison](#tol-vs-python-comparison)
14. [Troubleshooting](#troubleshooting)

---

## Introduction

TOL Python is a high-performance port of the TOL (Time-Oriented Language) statistical framework to Python, specifically designed for sophisticated time series analysis and Bayesian econometric modeling. The implementation has been extensively optimized to match C++ performance patterns while providing the ease of use of Python.

### What You'll Learn
- How to work with time series data using TOL's optimized Serie class
- High-performance TimeSet operations with caching and lazy evaluation
- Statistical analysis techniques (ACF, PACF, tests) with NumPy acceleration
- Classical and Bayesian ARIMA modeling with exact MLE
- Hierarchical Bayesian models with MCMC estimation
- Model comparison and automatic selection
- Production-ready econometric workflows with C++-level performance

### New in This Version (Corrected Implementation)
- **🚀 Performance**: TimeSet operations now use O(1) caching vs O(n) repeated computation
- **🏗️ Architecture**: Abstract base classes matching C++ TOL patterns
- **📊 Serie Storage**: NumPy arrays instead of dictionaries for C-level performance
- **⚡ Lazy Evaluation**: CalInd uses O(1) lazy evaluation vs O(n) pre-computation
- **💾 Memory Efficiency**: Constant memory usage for large date ranges
- **🔧 Algorithms**: Corrected Successor implementation using mathematical displacement

### Prerequisites
- Basic Python knowledge
- Understanding of time series concepts
- Familiarity with statistical inference (helpful but not required)
- NumPy/SciPy basic knowledge (for understanding performance optimizations)

---

## Installation & Setup

### Requirements
```python
# Core dependencies
import numpy as np
import scipy as sp
import matplotlib.pyplot as plt

# TOL Python modules - Corrected Implementation
from series import Serie
from series.corrected_serie import IndicatorSerie  # High-performance lazy evaluation
from core import Date, TimeSet, TimeSetBase  # Abstract base classes with caching
from core.dates import DayTimeSet, MonthTimeSet, YearTimeSet  # Optimized TimeSet operations
from stats import statistics, tests
from arima import ARIMA, ARIMAFactor
from bayesian import BayesianARIMA, MCMCConfig
from bayesian.priors import NormalPrior, InverseGammaPrior
```

### Verify Installation
```python
# Test basic functionality with corrected implementation
import numpy as np
from series.corrected_serie import Serie
from core.dates import Date, TimeSet

# Test optimized Serie creation
data = [1.2, 1.5, 1.3, 1.8, 1.6, 1.9, 1.7, 2.1]
serie = Serie(data=data, first_date=Date("y2023m01d01"), dating=TimeSet("daily"))
print(f"Serie created successfully: {serie.length()} observations")
print(f"Data stored as: {type(serie._data)}")
print(f"Using NumPy arrays: {isinstance(serie._data, np.ndarray)}")

# Test lazy CalInd
from series.operations import SerieOperations
indicator = SerieOperations.cal_ind(TimeSet("daily"), TimeSet("daily"), 
                                   Date("y2023m01d01"), Date("y2023m01d10"))
print(f"Lazy CalInd created: {type(indicator)}")
print(f"Test access: {indicator[Date('y2023m01d05')]}")
```

---

## Architecture & Performance

### Corrected Implementation Overview

TOL Python has been extensively rewritten to match the performance and architectural patterns of the original C++ TOL implementation. This section explains the key improvements.

#### TimeSet Architecture

**Before**: Simple inheritance without caching
**After**: Abstract base class with sophisticated caching system

```python
from core.dates import TimeSetBase, DayTimeSet, MonthTimeSet

# Create complex TimeSet operations
days_1_15 = DayTimeSet([1, 15])  # 1st and 15th of month
jan_jun_dec = MonthTimeSet([1, 6, 12])  # January, June, December

# Intersection with O(1) caching
complex_set = days_1_15 * jan_jun_dec

# Efficient range queries (cached results)
dates = complex_set.get_instants_between(
    Date("y2023m01d01"), 
    Date("y2023m12d31")
)
print(f"Found {len(dates)} matching dates")
```

#### Serie Storage Optimization

**Before**: Python dictionaries with O(1) but high memory overhead
**After**: NumPy arrays with C-contiguous memory and data compaction

```python
from series.corrected_serie import Serie
import numpy as np

# Large dataset with NumPy backend
size = 10000
data = np.random.randn(size)

# Efficient creation and access
serie = Serie(data=data, first_date=Date("y2020m01d01"), dating=TimeSet("daily"))

# O(1) data access with date-to-index conversion
test_date = Date("y2020m06d15")
value = serie[test_date]  # Microsecond access time
```

#### Lazy Evaluation for CalInd

**Before**: Pre-computes all values (O(n) memory and time)
**After**: Lazy evaluation with O(1) access

```python
from series.operations import SerieOperations
from series.corrected_serie import IndicatorSerie

# Create indicator for weekends over large range
weekends = TimeSet.wd([5, 6])  # Saturday, Sunday
dating = TimeSet("daily")

# Instant creation (no pre-computation)
indicator = SerieOperations.cal_ind(
    weekends, dating, 
    Date("y2020m01d01"), 
    Date("y2025m12d31")  # 6 years!
)

# Values computed on-demand
for test_date in [Date("y2022m06d15"), Date("y2024m03d20")]:
    value = indicator[test_date]  # Instant access
    print(f"{test_date}: {value}")
```

#### Performance Benchmarks

| Operation | Old Implementation | Corrected Implementation | Improvement |
|-----------|-------------------|---------------------------|-------------|
| TimeSet caching | O(n) repeated | O(1) cached | 10-100x |
| Serie data access | Dict lookup | NumPy array index | 2-5x |
| CalInd creation | O(n) pre-compute | O(1) lazy | 1000x |
| Memory usage | Linear scaling | Constant | Dramatic |
| Successor displacement | O(n) search | O(log n) binary search | 10x |

### Key Architectural Patterns

1. **Abstract Base Classes**: `TimeSetBase` and `SerieBase` provide framework structure
2. **Caching Systems**: `_hash_cache` and `_instants_cache` for repeated operations  
3. **Lazy Evaluation**: Values computed only when accessed
4. **NumPy Integration**: C-contiguous arrays for optimal memory layout
5. **Mathematical Algorithms**: Proper displacement logic matching C++ implementations

---

## Core Concepts

### The Serie Class
The Serie class is the foundation of TOL Python, representing time-indexed data with missing value support.

```python
# Creating Series
from series import Serie
from core import Date, TimeSet

# Method 1: Simple data array
data = [10.5, 11.2, 10.8, 12.1, 11.9]
serie = Serie(data=data)

# Method 2: With explicit dates
from datetime import datetime
start_date = Date(datetime(2023, 1, 1))
end_date = Date(datetime(2023, 1, 5))
serie_dated = Serie(data=data, first_date=start_date, last_date=end_date)

# Method 3: With time frequency
monthly_dating = TimeSet("monthly")
serie_monthly = Serie(data=data, dating=monthly_dating)

print(f"Serie length: {len(serie)}")
print(f"First value: {serie[0]}")
print(f"Data type: {type(serie._data)}")
```

### Table Output (C++ Compatible)
The `table()` method provides BDT (Bayes Data Table) format output compatible with TOL C++:

```python
# Single serie table
serie = Serie(data=[1.0, 2.5, 3.7, 4.2], first_date=Date("y2023m01d01"))
table_output = serie.table()
print(table_output)
# Output:
# daily    Serie1
# y2023m01d01    1
# y2023m01d02    2.5
# y2023m01d03    3.7
# y2023m01d04    4.2

# Display table with limited rows
print(serie.table(max_rows=10))  # Show first 10 rows
print(serie.table())              # Show all rows

# Multiple series table
serie1 = Serie(data=[1.0, 2.5, 3.7, 4.2], first_date=Date("y2023m01d01"))
serie2 = Serie(data=[10.0, 12.5, 13.7, 14.2], first_date=Date("y2023m01d01"))

from tol_python.series.corrected_serie import SerieTable
table = SerieTable()
table.add_serie(serie1, "Growth")
table.add_serie(serie2, "Revenue")

# Generate BDT format output
bdt_output = table.to_bdt_format()
print(bdt_output)
# Output:
# daily	Growth	Revenue
# y2023m01d01	1	10
# y2023m01d02	2.5	12.5
# y2023m01d03	3.7	13.7
# y2023m01d04	4.2	14.2

# Output directly to file or stdout
table.to_bdt_format("output.bdt")   # Save to file
table.to_bdt_format("Std")          # Print to stdout

# Save individual series to file using SerieIO
from tol_python.io import SerieIO
SerieIO.write_binary(serie, "output.tol")   # TOL binary format
SerieIO.write_json(serie, "output.json")    # JSON format
SerieIO.write_csv(serie, "output.csv")      # CSV format
```

### Working with Dates
TOL Python maintains full compatibility with TOL's date system:

```python
# TOL-style date creation
date1 = Date("y2023m01d15")  # January 15, 2023
date2 = Date("y2023m03")     # March 2023 (monthly)
date3 = Date("y2023")        # Year 2023

# Date arithmetic
next_day = date1.successor()
prev_day = date1.predecessor()
print(f"Next day: {next_day}")

# Date ranges
date_range = Date.range(date1, date2, TimeSet("daily"))
print(f"Days between: {len(date_range)}")
```

### TimeSet Operations (NEW)
TOL Python now supports set algebra operations on TimeSets, matching TOL's functionality:

```python
# Create TimeSets
daily = TimeSet("daily")
weekly = TimeSet("weekly")
monthly = TimeSet("monthly")

# Union: A + B (dates in either TimeSet)
combined_schedule = daily + weekly  # All dates that are in daily OR weekly

# Intersection: A * B (dates in both TimeSets)
common_dates = daily * monthly  # All dates that are in both daily AND monthly

# Difference: A - B (dates in A but not B)
daily_only = daily - weekly  # Dates that are daily but NOT weekly

# Complex expressions
complex_schedule = (daily + weekly) * monthly  # Union first, then intersect
filtered_dates = daily - (weekly + monthly)   # Daily excluding weekly or monthly

# Using method equivalents
union_ts = daily.union(weekly)          # Same as daily + weekly
inter_ts = daily.intersection(monthly)  # Same as daily * monthly
diff_ts = daily.difference(weekly)      # Same as daily - weekly

# Test if a date is included in a composite TimeSet
test_date = Date("y2023m06d15")
if complex_schedule.includes(test_date):
    print(f"{test_date} is in the schedule")

# Navigate composite TimeSets
next_date = complex_schedule.successor(test_date)
prev_date = complex_schedule.predecessor(test_date)
```

**Key Points:**
- **Union (+)**: Creates a TimeSet containing dates in either operand
- **Intersection (*)**: Creates a TimeSet containing dates in both operands  
- **Difference (-)**: Creates a TimeSet containing dates in first but not second
- **Composability**: Operations can be chained to create complex date patterns
- **TOL Compatibility**: Matches TOL's `ctms1 + ctms2`, `ctms1 * ctms2`, `ctms1 - ctms2`

### Advanced TimeSet Functions (NEW)
TOL Python now includes all TOL TimeSet functions for sophisticated date filtering:

```python
from tol_python.core import Date, TimeSet

# WORKING TimeSet Features:

# Specific dates within months
fifteenth_of_month = TimeSet.day(15)                    # 15th of every month
month_end = TimeSet.day([28, 29, 30, 31])             # End of month days
last_day = TimeSet.last_day_of_month()                # Last day of each month

# Specific months and years
summer_months = TimeSet.m([6, 7, 8])                   # June, July, August
leap_years = TimeSet.y([2020, 2024, 2028, 2032])      # Specific years

# Weekdays (0=Monday, 6=Sunday)
weekdays_only = TimeSet.wd([0, 1, 2, 3, 4])           # Monday-Friday
weekends = TimeSet.wd([5, 6])                          # Saturday-Sunday

# Note: Time-of-day filtering (hours, minutes, seconds) are not yet fully implemented
# These would be used like:
# business_hours = TimeSet.h(list(range(9, 18)))     # 9 AM - 5 PM (WIP - missing predecessor)
# quarter_hours = TimeSet.mi([0, 15, 30, 45])        # :00, :15, :30, :45 (WIP - missing predecessor)
# even_seconds = TimeSet.s(list(range(0, 60, 2)))    # Even seconds (WIP - missing predecessor)

# Periodic patterns - WIP (missing predecessor method)
# every_3_days = TimeSet.periodic(Date("y2023m06d01"), 3, "days")     # Every 3 days from June 1
# weekly_meetings = TimeSet.periodic(Date("y2023m06d01"), 1, "weeks") # Every week from June 1
# quarterly_reports = TimeSet.periodic(Date("y2023m01d01"), 3, "months") # Every quarter

# Date ranges with steps
june_2023 = TimeSet.range(Date("y2023m06d01"), Date("y2023m06d30")) # All of June
weekly_in_june = TimeSet.range(
    Date("y2023m06d01"), 
    Date("y2023m06d30"), 
    TimeSet("weekly")
)

# Complex business rules
month_end_weekdays = TimeSet.day([28, 29, 30, 31]) * weekdays_only
summer_fridays = summer_months * TimeSet.wd(4)  # Fridays in summer
quarter_end_business_days = (
    TimeSet.m([3, 6, 9, 12]) *       # Quarter end months
    TimeSet.day([28, 29, 30, 31]) *  # End of month
    weekdays_only                     # Business days only
)

# Get specific dates from any TimeSet
# Use get_instants_between to get dates in a range
start_date = Date("y2023m06d01")
end_date = Date("y2023m06d30")
june_business_days = weekdays_only.get_instants_between(start_date, end_date)
print(f"Business days in June: {len(june_business_days)}")

# Create a specific dates TimeSet from a list
from tol_python.core.dates import SpecificDatesTimeSet
specific_dates = SpecificDatesTimeSet([Date("y2023m06d15"), Date("y2023m07d15"), Date("y2023m08d15")])

# Combine with other TimeSets
summer_15th_weekdays = specific_dates * weekdays_only  # Only if 15th is a weekday

# Test if dates match patterns
test_date = Date("y2023m06d15")  # Thursday, June 15, 2023
print(f"Is {test_date} a business day? {weekdays_only.includes(test_date)}")
print(f"Is {test_date} month-end? {month_end.includes(test_date)}")
print(f"Is {test_date} summer? {summer_months.includes(test_date)}")

# Navigate complex schedules
next_month_end_weekday = month_end_weekdays.successor(test_date)
print(f"Next month-end business day: {next_month_end_weekday}")
```

**Complete Function Reference:**
- **`dates_of_set(start, n)`**: Get n dates from the TimeSet as a new TimeSet
- **`day(date, days)`**: Specific days of month (1-31)
- **`m(date, months)`**: Specific months (1-12)  
- **`y(years)`**: Specific years
- **`wd(weekdays)`**: Specific weekdays (0=Monday, 6=Sunday)
- **`h(date, hours)`**: Specific hours (0-23)
- **`mi(date, minutes)`**: Specific minutes (0-59)
- **`s(date, seconds)`**: Specific seconds (0-59)
- **`periodic(start, period, units)`**: Every n time units from start
- **`range(start, end, step)`**: Date range with optional step TimeSet
- **`ser_tms(serie)`**: TimeSet from a Serie's dates
```

### Missing Values
TOL Python handles missing values automatically using NumPy masked arrays:

```python
# Data with missing values (None or np.nan)
data_with_missing = [1.0, 2.0, None, 4.0, np.nan, 6.0]
serie = Serie(data=data_with_missing)

# Missing values are automatically handled
print(f"Valid observations: {serie.count_valid()}")
print(f"Has missing: {serie.has_missing()}")

# Statistical operations automatically exclude missing values
mean_value = serie.mean()
print(f"Mean (excluding missing): {mean_value}")
```

---

## Basic Time Series Operations

### Arithmetic Operations
```python
# Create sample series
ts1 = Serie(data=[1, 2, 3, 4, 5])
ts2 = Serie(data=[2, 4, 6, 8, 10])

# Basic arithmetic
sum_series = ts1 + ts2
diff_series = ts2 - ts1
product_series = ts1 * ts2
ratio_series = ts2 / ts1

print(f"Sum: {sum_series._data.to_numpy()}")
print(f"Ratio: {ratio_series._data.to_numpy()}")

# Scalar operations
scaled = ts1 * 2.5
shifted = ts1 + 10
print(f"Scaled: {scaled._data.to_numpy()}")
```

### Time Operations
```python
# Lag operations (fundamental for time series)
ts = Serie(data=[10, 15, 12, 18, 14, 20, 16])

# Create lags
lag1 = ts.lag(1)    # t-1
lag2 = ts.lag(2)    # t-2

# Differences (1-L operator in TOL)
diff1 = ts.diff(1)  # First difference
diff2 = ts.diff(2)  # Second difference

print(f"Original: {ts._data.to_numpy()}")
print(f"Lag 1: {lag1._data.to_numpy()}")
print(f"Diff 1: {diff1._data.to_numpy()}")

# Moving averages
ma3 = ts.moving_average(3)  # 3-period moving average
print(f"MA(3): {ma3._data.to_numpy()}")
```

### Subseries Extraction
```python
# Extract portions of series (TOL's SubSer equivalent)
full_series = Serie(data=list(range(100)))

# Extract by index
subset1 = full_series.subseries(10, 50)

# Extract by date (if series has dates)
start_date = Date("y2023m01d01")
end_date = Date("y2023m12d31")
daily_series = Serie(
    data=np.random.randn(365), 
    first_date=start_date,
    dating=TimeSet("daily")
)

# Extract Q1 2023
q1_start = Date("y2023m01d01")
q1_end = Date("y2023m03d31")
q1_data = daily_series.subseries_by_date(q1_start, q1_end)

print(f"Q1 observations: {len(q1_data)}")
```

---

## Statistical Analysis

### Descriptive Statistics
```python
from stats import statistics

# Generate sample AR(1) data
np.random.seed(42)
n = 200
phi = 0.7
data = np.zeros(n)
for t in range(1, n):
    data[t] = phi * data[t-1] + np.random.normal(0, 1)

ts = Serie(data=data)

# Basic statistics
mean = statistics.mean(ts)
variance = statistics.variance(ts)
std_dev = statistics.std_dev(ts)
skewness = statistics.skewness(ts)
kurtosis = statistics.kurtosis(ts)

print(f"Mean: {mean:.4f}")
print(f"Std Dev: {std_dev:.4f}")
print(f"Skewness: {skewness:.4f}")
print(f"Kurtosis: {kurtosis:.4f}")

# Quantiles
median = statistics.quantile(ts, 0.5)
q25 = statistics.quantile(ts, 0.25)
q75 = statistics.quantile(ts, 0.75)
print(f"Median: {median:.4f}")
print(f"IQR: [{q25:.4f}, {q75:.4f}]")
```

### Autocorrelation Analysis
```python
# Autocorrelation function
acf_result = statistics.autocorr_function(ts, max_lag=20)
print(f"ACF lags 1-5: {acf_result[:5]}")

# Partial autocorrelation function  
pacf_result = statistics.partial_autocorr_function(ts, max_lag=20)
print(f"PACF lags 1-5: {pacf_result[:5]}")

# Cross-correlation between two series
ts2 = Serie(data=np.random.randn(200))
ccf_result = statistics.cross_correlation(ts, ts2, max_lag=10)
print(f"CCF at lag 0: {ccf_result[10]}")  # Middle element is lag 0
```

### Statistical Tests
```python
from stats import tests

# Normality tests
jb_stat, jb_pvalue = tests.jarque_bera_test(ts)
print(f"Jarque-Bera test: statistic={jb_stat:.4f}, p-value={jb_pvalue:.4f}")

# Serial correlation tests
lb_stat, lb_pvalue = tests.ljung_box_test(ts, lags=10)
print(f"Ljung-Box test: statistic={lb_stat:.4f}, p-value={lb_pvalue:.4f}")

# Unit root test
adf_stat, adf_pvalue = tests.augmented_dickey_fuller_test(ts)
print(f"ADF test: statistic={adf_stat:.4f}, p-value={adf_pvalue:.4f}")
if adf_pvalue < 0.05:
    print("Series appears to be stationary")
else:
    print("Series appears to have a unit root")

# Durbin-Watson test for autocorrelation
dw_stat = tests.durbin_watson_test(ts)
print(f"Durbin-Watson: {dw_stat:.4f}")
```

---

## ARIMA Modeling

### Classical ARIMA Estimation
```python
from arima import ARIMA, ARIMAFactor

# Define ARIMA model specification
# ARIMA(1,1,1) model
factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1)

# Create and fit model
arima_model = ARIMA(factor)
results = arima_model.fit(ts, method="mle")  # Maximum likelihood estimation

# Display results
print(results.summary())

# Get parameter estimates
print(f"AR coefficient: {results.ar_params[0]:.4f}")
print(f"MA coefficient: {results.ma_params[0]:.4f}")
print(f"Sigma-squared: {results.sigma2:.4f}")

# Information criteria
print(f"AIC: {results.aic:.4f}")
print(f"BIC: {results.bic:.4f}")
```

### Seasonal ARIMA
```python
# SARIMA(1,1,1)(1,1,1)[12] model for monthly data
seasonal_factor = ARIMAFactor(
    ar_order=1, diff_order=1, ma_order=1,
    seasonal_ar_order=1, seasonal_diff_order=1, seasonal_ma_order=1,
    season_length=12
)

# Generate monthly data
monthly_data = np.random.randn(120)  # 10 years of monthly data
monthly_ts = Serie(data=monthly_data, dating=TimeSet("monthly"))

sarima_model = ARIMA(seasonal_factor)
seasonal_results = sarima_model.fit(monthly_ts)

print(f"Seasonal model: {seasonal_factor.get_model_string()}")
print(f"Regular AR: {seasonal_results.ar_params}")
print(f"Seasonal AR: {seasonal_results.seasonal_ar_params if hasattr(seasonal_results, 'seasonal_ar_params') else 'N/A'}")
```

### Forecasting
```python
# Generate forecasts
forecast_horizon = 12
forecasts, lower_bounds, upper_bounds = arima_model.forecast(forecast_horizon)

print(f"12-step ahead forecasts:")
for i in range(forecast_horizon):
    print(f"  Step {i+1}: {forecasts[i]:.4f} [{lower_bounds[i]:.4f}, {upper_bounds[i]:.4f}]")

# Plot forecasts (if matplotlib available)
try:
    import matplotlib.pyplot as plt
    
    plt.figure(figsize=(12, 6))
    
    # Plot original data (last 50 points)
    original_data = ts._data.to_numpy()[-50:]
    plt.plot(range(len(original_data)), original_data, 'b-', label='Observed', linewidth=2)
    
    # Plot forecasts
    forecast_start = len(original_data)
    forecast_range = range(forecast_start, forecast_start + forecast_horizon)
    plt.plot(forecast_range, forecasts, 'r-', label='Forecast', linewidth=2)
    plt.fill_between(forecast_range, lower_bounds, upper_bounds, 
                     alpha=0.3, color='red', label='95% CI')
    
    plt.legend()
    plt.title('ARIMA Forecast')
    plt.xlabel('Time')
    plt.ylabel('Value')
    plt.grid(True, alpha=0.3)
    plt.show()
    
except ImportError:
    print("Matplotlib not available for plotting")
```

### Model Diagnostics
```python
# Residual analysis
residuals = results.residuals
print(f"Residual diagnostics:")

# Test residuals for remaining autocorrelation
lb_residuals = tests.ljung_box_test(residuals, lags=10)
print(f"  Ljung-Box on residuals: p-value = {lb_residuals[1]:.4f}")

# Test for normality
jb_residuals = tests.jarque_bera_test(residuals)
print(f"  Jarque-Bera on residuals: p-value = {jb_residuals[1]:.4f}")

# Plot residual ACF
residual_acf = statistics.autocorr_function(residuals, max_lag=20)
print(f"  Residual ACF (lags 1-5): {residual_acf[:5]}")
```

---

## Bayesian Framework

### Introduction to Bayesian Analysis
The Bayesian framework provides a complete probabilistic approach to parameter estimation with uncertainty quantification.

```python
from bayesian import BayesianARIMA, MCMCConfig
from bayesian.priors import NormalPrior, InverseGammaPrior, HierarchicalPrior

# Basic Bayesian workflow
print("=== BAYESIAN ARIMA ESTIMATION ===")
```

### Prior Specification
```python
# 1. Define priors for ARIMA parameters
# For AR(1) model: y_t = φ * y_{t-1} + ε_t, ε_t ~ N(0, σ²)

# Prior for AR coefficient: φ ~ N(0, 0.5²)
ar_prior = NormalPrior(mean=0.0, variance=0.25, name="AR_coefficient")

# Prior for error variance: σ² ~ InverseGamma(3, 2)
variance_prior = InverseGammaPrior(shape=3.0, scale=2.0, name="error_variance")

# Combine in dictionary
custom_priors = {
    'ar_0': ar_prior,
    'sigma2': variance_prior
}

print("Prior distributions specified:")
print(f"  AR coefficient: {ar_prior.name}")
print(f"  Error variance: {variance_prior.name}")
```

### Hierarchical Priors
```python
# Hierarchical prior example: AR coefficients with common variance
# φ_i ~ N(0, τ²), τ² ~ InverseGamma(3, 2)

# Hyperprior for common variance
hyperprior = InverseGammaPrior(shape=3.0, scale=2.0, name="AR_common_variance")

# Individual AR coefficient priors
ar1_hierarchical = HierarchicalPrior(
    parameter_prior=NormalPrior(0.0, 1.0, name="AR1"),
    hyperprior=hyperprior,
    name="AR1_hierarchical"
)

hierarchical_priors = {
    'ar_0': ar1_hierarchical,
    'sigma2': variance_prior
}

print("Hierarchical prior structure defined")
```

### MCMC Configuration
```python
# Configure MCMC sampling (following TOL's BysMcmc structure)
mcmc_config = MCMCConfig(
    # Core MCMC settings
    mcmc_burnin=1000,           # Burn-in iterations
    mcmc_sample_length=5000,    # Post-burn-in samples
    mcmc_cache_length=500,      # Cache size
    
    # Convergence settings
    convergence_tolerance=1e-6,
    max_iterations=50000,
    
    # Diagnostic settings (TOL-style)
    raftery_diag_q=0.025,       # Quantile of interest
    raftery_diag_r=0.007,       # Desired margin of error
    raftery_diag_s=0.950,       # Probability of achieving precision
    acf_max_lag=20,             # Max lag for autocorrelation
    
    # Reproducibility
    random_seed=12345
)

print("MCMC configuration:")
print(f"  Burn-in: {mcmc_config.mcmc_burnin}")
print(f"  Samples: {mcmc_config.mcmc_sample_length}")
print(f"  Random seed: {mcmc_config.random_seed}")
```

---

## Advanced Bayesian ARIMA

### Bayesian ARIMA Estimation
```python
# Create Bayesian ARIMA model
factor = ARIMAFactor(1, 0, 0)  # AR(1) specification

# Option 1: Use default priors
bayesian_arima = BayesianARIMA(factor, config=mcmc_config)

# Option 2: Use custom priors
bayesian_arima_custom = BayesianARIMA(
    factor=factor, 
    priors=custom_priors, 
    config=mcmc_config
)

# Fit the model to data
print("Fitting Bayesian ARIMA model...")
bayesian_results = bayesian_arima_custom.fit(ts)

print("Bayesian estimation completed!")
print(f"Posterior samples: {bayesian_results.n_samples}")
print(f"Burn-in: {bayesian_results.burn_in}")
```

### Posterior Analysis
```python
# Examine posterior distributions
print("\n=== POSTERIOR ANALYSIS ===")

# AR coefficient posterior
ar_posterior_mean = bayesian_results.ar_posterior_mean[0]
ar_posterior_std = bayesian_results.ar_posterior_std[0]
ar_credible_interval = bayesian_results.ar_credible_intervals

print(f"AR Coefficient:")
print(f"  Posterior mean: {ar_posterior_mean:.4f}")
print(f"  Posterior std:  {ar_posterior_std:.4f}")
print(f"  95% Credible interval: [{ar_credible_interval[0][0]:.4f}, {ar_credible_interval[1][0]:.4f}]")

# Error variance posterior
variance_posterior_mean = bayesian_results.variance_posterior_mean
variance_posterior_std = bayesian_results.variance_posterior_std
variance_credible_interval = bayesian_results.variance_credible_interval

print(f"Error Variance:")
print(f"  Posterior mean: {variance_posterior_mean:.4f}")
print(f"  Posterior std:  {variance_posterior_std:.4f}")
print(f"  95% Credible interval: [{variance_credible_interval[0]:.4f}, {variance_credible_interval[1]:.4f}]")
```

### MCMC Diagnostics
```python
# Check MCMC convergence
print("\n=== MCMC DIAGNOSTICS ===")

if bayesian_results.mcmc_diagnostics:
    for param_name, diagnostics in bayesian_results.mcmc_diagnostics.items():
        print(f"\n{param_name}:")
        
        # Effective sample size
        if 'effective_sample_size' in diagnostics:
            ess = diagnostics['effective_sample_size']
            print(f"  Effective Sample Size: {ess:.1f}")
        
        # Geweke diagnostic
        if 'geweke' in diagnostics:
            geweke = diagnostics['geweke']
            print(f"  Geweke Z-score: {geweke['z_score']:.3f}")
            print(f"  Geweke p-value: {geweke['p_value']:.3f}")
            
            if geweke['p_value'] > 0.05:
                print("  ✓ No evidence of non-convergence")
            else:
                print("  ⚠ Potential convergence issues")

# Effective sample size summary
if bayesian_results.effective_sample_sizes:
    print(f"\nEffective Sample Sizes:")
    for param, ess in bayesian_results.effective_sample_sizes.items():
        efficiency = ess / bayesian_results.n_samples
        print(f"  {param}: {ess:.1f} ({efficiency:.1%} efficiency)")
```

### Bayesian Forecasting
```python
# Generate Bayesian forecasts with uncertainty
print("\n=== BAYESIAN FORECASTING ===")

forecast_steps = 10
forecast_mean, forecast_lower, forecast_upper = bayesian_arima_custom.forecast(
    steps=forecast_steps, 
    alpha=0.05  # 95% credible intervals
)

print(f"Bayesian forecasts ({forecast_steps} steps ahead):")
for i in range(forecast_steps):
    ci_width = forecast_upper[i] - forecast_lower[i]
    print(f"  Step {i+1}: {forecast_mean[i]:7.4f} ± {ci_width/2:6.4f} [{forecast_lower[i]:7.4f}, {forecast_upper[i]:7.4f}]")

# Compare uncertainty over time
print(f"\nUncertainty evolution:")
print(f"  1-step CI width: {forecast_upper[0] - forecast_lower[0]:.4f}")
print(f"  5-step CI width: {forecast_upper[4] - forecast_lower[4]:.4f}")
print(f"  10-step CI width: {forecast_upper[9] - forecast_lower[9]:.4f}")
```

### Advanced Model Structures
```python
# Multi-lag ARMA model with hierarchical priors
factor_arma = ARIMAFactor(ar_order=2, diff_order=0, ma_order=1)

# Hierarchical priors for AR coefficients
ar_variance_prior = InverseGammaPrior(shape=3.0, scale=2.0, name="AR_variance")

arma_priors = {
    'ar_0': HierarchicalPrior(
        NormalPrior(0.0, 1.0, name="AR1"),
        ar_variance_prior,
        name="AR1_hierarchical"
    ),
    'ar_1': HierarchicalPrior(
        NormalPrior(0.0, 1.0, name="AR2"), 
        ar_variance_prior,
        name="AR2_hierarchical"
    ),
    'ma_0': NormalPrior(0.0, 0.5, name="MA1"),
    'sigma2': InverseGammaPrior(3.0, 2.0, name="error_variance")
}

# Fit hierarchical ARMA model
bayesian_arma = BayesianARIMA(factor_arma, priors=arma_priors, config=mcmc_config)
print("Fitting hierarchical ARMA(2,0,1) model...")
# arma_results = bayesian_arma.fit(ts)  # Uncomment to run
```

---

## Model Comparison & Selection

### Information Criteria
```python
from bayesian.model_selection import ModelComparisonCriteria, ARIMAModelSelection

# Compare multiple models using Bayesian criteria
print("\n=== MODEL COMPARISON ===")

# Fit several models for comparison
models_to_compare = [
    ARIMAFactor(1, 0, 0),  # AR(1)
    ARIMAFactor(2, 0, 0),  # AR(2)
    ARIMAFactor(1, 0, 1),  # ARMA(1,1)
]

comparison_results = []

for factor in models_to_compare:
    print(f"Fitting {factor.get_model_string()}...")
    
    # Use shorter MCMC for comparison
    quick_config = MCMCConfig(
        mcmc_burnin=500,
        mcmc_sample_length=1000,
        random_seed=42
    )
    
    bayes_model = BayesianARIMA(factor, config=quick_config)
    results = bayes_model.fit(ts)
    
    # Store results for comparison
    comparison_results.append({
        'model': factor.get_model_string(),
        'dic': results.dic if results.dic else float('inf'),
        'log_marginal_likelihood': results.log_marginal_likelihood if results.log_marginal_likelihood else -float('inf'),
        'n_params': len(factor.get_polynomial_orders()[0]) + len(factor.get_polynomial_orders()[1]) + 1,
        'results': results
    })

# Display comparison
print(f"\nModel Comparison Results:")
print(f"{'Model':>12s} {'DIC':>8s} {'Log ML':>8s} {'Params':>7s}")
print("-" * 40)

for result in comparison_results:
    dic_str = f"{result['dic']:.2f}" if result['dic'] != float('inf') else "N/A"
    ml_str = f"{result['log_marginal_likelihood']:.2f}" if result['log_marginal_likelihood'] != -float('inf') else "N/A"
    print(f"{result['model']:>12s} {dic_str:>8s} {ml_str:>8s} {result['n_params']:>7d}")

# Find best model by DIC (lower is better)
valid_results = [r for r in comparison_results if r['dic'] != float('inf')]
if valid_results:
    best_model = min(valid_results, key=lambda x: x['dic'])
    print(f"\nBest model by DIC: {best_model['model']} (DIC = {best_model['dic']:.2f})")
```

### Automatic Model Selection
```python
# Automatic ARIMA model selection
print("\n=== AUTOMATIC MODEL SELECTION ===")

# Configure automatic selection
auto_selector = ARIMAModelSelection(
    max_p=3,           # Maximum AR order
    max_d=2,           # Maximum differencing
    max_q=2,           # Maximum MA order
    seasonal=False,    # No seasonal components
)

# For demonstration, we'll manually test a few models
# (Full automatic selection would be computationally intensive)

candidate_models = [
    ARIMAFactor(1, 0, 0),
    ARIMAFactor(1, 1, 0), 
    ARIMAFactor(1, 0, 1),
]

print("Testing candidate models...")
best_dic = float('inf')
best_model_result = None

for factor in candidate_models:
    try:
        quick_config = MCMCConfig(
            mcmc_burnin=300,
            mcmc_sample_length=500,
            random_seed=123
        )
        
        bayes_model = BayesianARIMA(factor, config=quick_config)
        results = bayes_model.fit(ts)
        
        if results.dic and results.dic < best_dic:
            best_dic = results.dic
            best_model_result = (factor, results)
            
        print(f"  {factor.get_model_string()}: DIC = {results.dic:.2f if results.dic else 'N/A'}")
        
    except Exception as e:
        print(f"  {factor.get_model_string()}: Failed ({str(e)[:50]}...)")

if best_model_result:
    best_factor, best_results = best_model_result
    print(f"\nSelected model: {best_factor.get_model_string()}")
    print(f"DIC: {best_dic:.2f}")
```

### Bayes Factors
```python
# Compute Bayes factors for model comparison
from bayesian.model_selection import BayesFactorComparison

# Example: Compare AR(1) vs AR(2)
# (Using mock marginal likelihoods for demonstration)

# In practice, these would come from actual model fits
ml_ar1 = -150.5  # Log marginal likelihood for AR(1)
ml_ar2 = -152.1  # Log marginal likelihood for AR(2)

bf_result = BayesFactorComparison.compute_bayes_factor(ml_ar1, ml_ar2)

print(f"\nBayes Factor Analysis:")
print(f"Log Bayes Factor (AR1 vs AR2): {bf_result['log_bayes_factor']:.3f}")
print(f"Bayes Factor: {bf_result['bayes_factor']:.3f}")
print(f"Evidence: {bf_result['evidence_strength']}")
print(f"Interpretation: {bf_result['interpretation']}")
```

---

## Real-World Examples

### Example 1: GDP Growth Analysis
```python
print("\n" + "="*60)
print("REAL-WORLD EXAMPLE 1: GDP GROWTH ANALYSIS")
print("="*60)

# Simulate quarterly GDP data
np.random.seed(2023)
n_quarters = 80  # 20 years of quarterly data

# GDP growth with trend and cyclical components
trend = 0.005  # 0.5% quarterly growth trend
cyclical_component = np.sin(np.linspace(0, 4*np.pi, n_quarters)) * 0.01
noise = np.random.normal(0, 0.015, n_quarters)

gdp_growth = trend + cyclical_component + noise

# Add some persistence (AR structure)
for t in range(1, n_quarters):
    gdp_growth[t] += 0.3 * gdp_growth[t-1]

# Create Serie with quarterly dating
from datetime import datetime
start_date = Date(datetime(2004, 1, 1))
gdp_serie = Serie(
    data=gdp_growth,
    first_date=start_date,
    dating=TimeSet("quarterly")
)

print(f"GDP Growth Data: {n_quarters} quarters from 2004 Q1")
print(f"Mean growth: {gdp_serie.mean():.4f} ({gdp_serie.mean()*100:.2f}%)")
print(f"Volatility: {statistics.std_dev(gdp_serie):.4f}")

# Statistical analysis
print(f"\nStatistical Analysis:")
adf_stat, adf_pvalue = tests.augmented_dickey_fuller_test(gdp_serie)
print(f"ADF test: statistic={adf_stat:.3f}, p-value={adf_pvalue:.3f}")
print(f"Stationarity: {'Yes' if adf_pvalue < 0.05 else 'No'}")

acf_gdp = statistics.autocorr_function(gdp_serie, max_lag=8)
print(f"ACF(1-4): {acf_gdp[:4]}")

# Fit Bayesian AR model
factor_gdp = ARIMAFactor(ar_order=2, diff_order=0, ma_order=0)

gdp_priors = {
    'ar_0': NormalPrior(0.0, 0.5, name="AR1_GDP"),
    'ar_1': NormalPrior(0.0, 0.5, name="AR2_GDP"),
    'intercept': NormalPrior(0.005, 0.01, name="GDP_trend"),  # Prior around 0.5% growth
    'sigma2': InverseGammaPrior(3.0, 0.0001, name="GDP_volatility")
}

gdp_config = MCMCConfig(
    mcmc_burnin=1000,
    mcmc_sample_length=2000,
    random_seed=2023
)

print(f"\nFitting Bayesian AR(2) model to GDP growth...")
bayesian_gdp = BayesianARIMA(factor_gdp, priors=gdp_priors, config=gdp_config)
gdp_results = bayesian_gdp.fit(gdp_serie)

print(f"Bayesian AR(2) Results:")
print(f"  AR1 coefficient: {gdp_results.ar_posterior_mean[0]:.4f} ± {gdp_results.ar_posterior_std[0]:.4f}")
print(f"  AR2 coefficient: {gdp_results.ar_posterior_mean[1]:.4f} ± {gdp_results.ar_posterior_std[1]:.4f}")
print(f"  Trend (intercept): {gdp_results.intercept_posterior_mean:.4f} ± {gdp_results.intercept_posterior_std:.4f}")
print(f"  Volatility (σ): {np.sqrt(gdp_results.variance_posterior_mean):.4f}")

# Economic interpretation
ar1_coef = gdp_results.ar_posterior_mean[0]
ar2_coef = gdp_results.ar_posterior_mean[1]
persistence = ar1_coef + ar2_coef
print(f"\nEconomic Interpretation:")
print(f"  Persistence: {persistence:.4f}")
print(f"  Half-life: {np.log(0.5)/np.log(persistence):.1f} quarters" if persistence > 0 else "  N/A")

# Forecast GDP growth
gdp_forecast, gdp_lower, gdp_upper = bayesian_gdp.forecast(steps=8)  # 2 years ahead
print(f"\nGDP Growth Forecasts (next 8 quarters):")
for i in range(8):
    quarter = f"Q{(i%4)+1}"
    year = 2024 + i//4
    print(f"  {year} {quarter}: {gdp_forecast[i]*100:.2f}% [{gdp_lower[i]*100:.2f}%, {gdp_upper[i]*100:.2f}%]")
```

### Example 2: Inflation Modeling with Regime Changes
```python
print("\n" + "="*60)
print("REAL-WORLD EXAMPLE 2: INFLATION MODELING")
print("="*60)

# Simulate monthly inflation data with regime changes
np.random.seed(1979)
n_months = 360  # 30 years of monthly data

# Create inflation with different regimes
inflation = np.zeros(n_months)

# Regime 1: High inflation period (first 120 months)
for t in range(120):
    if t == 0:
        inflation[t] = 0.08  # Start at 8% annual
    else:
        # High persistence, mean reverting to 8%
        inflation[t] = 0.02 + 0.95 * inflation[t-1] + np.random.normal(0, 0.01)

# Regime 2: Disinflation period (months 120-240)
for t in range(120, 240):
    # Gradual decline
    target = 0.02 * (1 - (t-120)/120) + 0.02  # Target moves from 8% to 2%
    inflation[t] = target + 0.7 * (inflation[t-1] - target) + np.random.normal(0, 0.005)

# Regime 3: Low inflation period (months 240+)
for t in range(240, n_months):
    # Low, stable inflation
    inflation[t] = 0.02 + 0.5 * (inflation[t-1] - 0.02) + np.random.normal(0, 0.003)

# Convert to monthly rates (from annual)
inflation = inflation / 12

inflation_serie = Serie(data=inflation, dating=TimeSet("monthly"))

print(f"Inflation Data: {n_months} months (30 years)")
print(f"Mean inflation: {inflation_serie.mean()*1200:.2f}% annual")
print(f"Volatility: {statistics.std_dev(inflation_serie)*1200:.2f}% annual")

# Test for structural breaks (simplified)
period1 = inflation_serie.subseries(0, 120)
period2 = inflation_serie.subseries(120, 240) 
period3 = inflation_serie.subseries(240, 360)

print(f"\nRegime Analysis:")
print(f"  Period 1 (1980s): {period1.mean()*1200:.2f}% annual")
print(f"  Period 2 (1990s): {period2.mean()*1200:.2f}% annual") 
print(f"  Period 3 (2000s): {period3.mean()*1200:.2f}% annual")

# Fit different models to different periods
print(f"\nComparing models across regimes...")

# Model for high inflation period
factor_high = ARIMAFactor(ar_order=1, diff_order=0, ma_order=0)
high_inflation_priors = {
    'ar_0': NormalPrior(0.8, 0.2, name="High_inflation_AR"),  # High persistence
    'intercept': NormalPrior(0.005, 0.002, name="High_inflation_mean"),
    'sigma2': InverseGammaPrior(3.0, 0.00001, name="High_inflation_vol")
}

quick_config = MCMCConfig(mcmc_burnin=500, mcmc_sample_length=1000, random_seed=1979)

bayesian_high = BayesianARIMA(factor_high, priors=high_inflation_priors, config=quick_config)
high_results = bayesian_high.fit(period1)

print(f"High Inflation Period AR(1):")
print(f"  Persistence: {high_results.ar_posterior_mean[0]:.4f}")
print(f"  Mean level: {high_results.intercept_posterior_mean*1200:.2f}% annual")

# Model for low inflation period
low_inflation_priors = {
    'ar_0': NormalPrior(0.3, 0.2, name="Low_inflation_AR"),  # Lower persistence
    'intercept': NormalPrior(0.0015, 0.001, name="Low_inflation_mean"),
    'sigma2': InverseGammaPrior(3.0, 0.000005, name="Low_inflation_vol")
}

bayesian_low = BayesianARIMA(factor_high, priors=low_inflation_priors, config=quick_config)
low_results = bayesian_low.fit(period3)

print(f"Low Inflation Period AR(1):")
print(f"  Persistence: {low_results.ar_posterior_mean[0]:.4f}")
print(f"  Mean level: {low_results.intercept_posterior_mean*1200:.2f}% annual")

print(f"\nKey Insights:")
print(f"  Regime change evident in persistence and mean levels")
print(f"  High inflation regime shows higher persistence")
print(f"  Bayesian approach captures uncertainty in regime parameters")
```

### Example 3: Financial Returns with Volatility Clustering
```python
print("\n" + "="*60)
print("REAL-WORLD EXAMPLE 3: FINANCIAL RETURNS ANALYSIS")
print("="*60)

# Simulate daily stock returns with volatility clustering
np.random.seed(2008)
n_days = 1000  # ~4 years of daily data

# GARCH-like volatility process
returns = np.zeros(n_days)
volatility = np.zeros(n_days)
volatility[0] = 0.02  # Initial volatility (2% daily)

# Simulate with volatility clustering
for t in range(1, n_days):
    # Volatility follows GARCH(1,1)-like process
    volatility[t] = 0.001 + 0.05 * (returns[t-1]**2) + 0.9 * volatility[t-1]
    
    # Returns with time-varying volatility
    returns[t] = np.random.normal(0.0005, volatility[t])  # 0.05% daily expected return

returns_serie = Serie(data=returns, dating=TimeSet("daily"))

print(f"Financial Returns: {n_days} daily observations")
print(f"Mean return: {returns_serie.mean()*252:.2f}% annual")
print(f"Volatility: {statistics.std_dev(returns_serie)*np.sqrt(252):.2f}% annual")
print(f"Sharpe ratio: {returns_serie.mean()/statistics.std_dev(returns_serie)*np.sqrt(252):.2f}")

# Test for serial correlation in returns and squared returns
returns_acf = statistics.autocorr_function(returns_serie, max_lag=10)
squared_returns = Serie(data=returns**2)
squared_acf = statistics.autocorr_function(squared_returns, max_lag=10)

print(f"\nSerial Correlation Analysis:")
print(f"  Returns ACF(1): {returns_acf[0]:.4f}")
print(f"  Squared returns ACF(1): {squared_acf[0]:.4f}")

# Test for normality and excess kurtosis
jb_stat, jb_pvalue = tests.jarque_bera_test(returns_serie)
skew = statistics.skewness(returns_serie)
kurt = statistics.kurtosis(returns_serie)

print(f"  Skewness: {skew:.4f}")
print(f"  Excess kurtosis: {kurt-3:.4f}")
print(f"  Jarque-Bera p-value: {jb_pvalue:.4f}")

# Fit ARMA model to returns (should be close to white noise)
factor_returns = ARIMAFactor(ar_order=1, diff_order=0, ma_order=1)

returns_priors = {
    'ar_0': NormalPrior(0.0, 0.1, name="Returns_AR"),
    'ma_0': NormalPrior(0.0, 0.1, name="Returns_MA"),
    'intercept': NormalPrior(0.0005, 0.001, name="Mean_return"),
    'sigma2': InverseGammaPrior(3.0, 0.00001, name="Return_variance")
}

print(f"\nFitting ARMA(1,1) to returns...")
bayesian_returns = BayesianARIMA(factor_returns, priors=returns_priors, config=quick_config)
returns_results = bayesian_returns.fit(returns_serie)

print(f"Returns Model Results:")
print(f"  AR coefficient: {returns_results.ar_posterior_mean[0]:.4f} ± {returns_results.ar_posterior_std[0]:.4f}")
print(f"  MA coefficient: {returns_results.ma_posterior_mean[0]:.4f} ± {returns_results.ma_posterior_std[0]:.4f}")
print(f"  Mean return: {returns_results.intercept_posterior_mean*252:.3f}% annual")

# Risk analysis
var_95 = statistics.quantile(returns_serie, 0.05)
print(f"\nRisk Measures:")
print(f"  5% VaR (daily): {var_95*100:.2f}%")
print(f"  5% VaR (annual): {var_95*np.sqrt(252)*100:.2f}%")

# Forecast returns with uncertainty
returns_forecast, returns_lower, returns_upper = bayesian_returns.forecast(steps=22)  # 1 month
print(f"\nReturn Forecasts (next 22 trading days):")
print(f"  Expected return: {np.mean(returns_forecast)*100:.3f}% daily")
print(f"  Forecast volatility: {np.std(returns_forecast)*100:.3f}% daily")
print(f"  95% CI width: {(np.mean(returns_upper) - np.mean(returns_lower))*100:.3f}% daily")
```

---

## Performance Guide

### Optimizing TOL Python Performance

This section provides guidance on getting the best performance from the corrected TOL Python implementation.

#### TimeSet Performance Tips

```python
# Good: Use caching for repeated operations
complex_set = DayTimeSet([1, 15]) * MonthTimeSet([1, 6, 12])
dates_2020 = complex_set.get_instants_between(Date("y2020m01d01"), Date("y2020m12d31"))
dates_2021 = complex_set.get_instants_between(Date("y2021m01d01"), Date("y2021m12d31"))  # Uses cache

# Avoid: Recreating TimeSet objects unnecessarily
# for year in range(2020, 2025):
#     temp_set = DayTimeSet([1, 15]) * MonthTimeSet([1, 6, 12])  # Inefficient
```

#### Serie Storage Best Practices

```python
# Good: Use corrected Serie with NumPy arrays
from series.corrected_serie import Serie
large_data = np.random.randn(100000)
serie = Serie(data=large_data, first_date=Date("y2000m01d01"), dating=TimeSet("daily"))

# Good: Batch operations when possible
values = [serie[date] for date in dates_2020]  # Efficient with NumPy backend

# Avoid: Original Serie with dictionary storage (if you still have it)
# from series.serie import Serie  # Old implementation
```

#### CalInd and Indicator Performance

```python
# Excellent: Use lazy evaluation for large ranges
from series.operations import SerieOperations

# This is instant regardless of range size
large_indicator = SerieOperations.cal_ind(
    TimeSet.wd([5, 6]),  # Weekends
    TimeSet("daily"),
    Date("y1990m01d01"),  # 35 years!
    Date("y2025m12d31")
)

# Values computed only when accessed
weekend_2020_01_04 = large_indicator[Date("y2020m01d04")]  # Saturday -> 1.0
weekday_2020_01_06 = large_indicator[Date("y2020m01d06")]  # Monday -> 0.0

# Memory usage remains constant regardless of date range
```

#### Memory Management

```python
import psutil
import os

def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

# Demonstrate constant memory usage
print(f"Initial memory: {get_memory_usage():.1f} MB")

indicators = []
for years in [1, 5, 10, 20, 35]:  # Increasing date ranges
    end_year = 1990 + years
    indicator = SerieOperations.cal_ind(
        TimeSet.wd([0]),  # Mondays
        TimeSet("daily"),
        Date("y1990m01d01"),
        Date(f"y{end_year}m12d31")
    )
    indicators.append(indicator)
    print(f"{years:2d} years range: {get_memory_usage():.1f} MB")

# Memory usage should remain nearly constant!
```

#### Successor Operations

```python
# The corrected implementation uses mathematical displacement
base_set = MonthTimeSet([1])  # January
daily = TimeSet("daily")

# This is now O(log n) instead of O(n)
successor_set = TimeSet.successor_tol(base_set, 15, daily)

# Test performance
import time
test_dates = [Date(f"y2020m01d{day:02d}") for day in range(1, 32)]

start_time = time.time()
results = [successor_set.includes(date) for date in test_dates]
end_time = time.time()

print(f"Successor operations: {end_time - start_time:.6f}s for {len(test_dates)} tests")
print(f"Average: {(end_time - start_time) / len(test_dates) * 1000000:.2f} microseconds per test")
```

#### Performance Comparison Summary

| Component | Old Performance | New Performance | Typical Improvement |
|-----------|----------------|------------------|--------------------|
| TimeSet caching | O(n) per query | O(1) cached | 10-100x |
| CalInd creation | O(n) memory/time | O(1) lazy | 100-1000x |
| Serie access | Dict overhead | NumPy array | 2-5x |
| Successor algorithm | O(n) search | O(log n) math | 5-20x |
| Memory usage | Linear scaling | Constant | Dramatic |

#### Benchmarking Your Code

```python
# Use this template to benchmark your operations
import time
import numpy as np

def benchmark_operation(operation, *args, **kwargs):
    """Benchmark an operation with timing"""
    start_time = time.time()
    result = operation(*args, **kwargs)
    end_time = time.time()
    return result, end_time - start_time

# Example: Benchmark TimeSet operations
result, elapsed = benchmark_operation(
    lambda: complex_set.get_instants_between(Date("y2020m01d01"), Date("y2020m12d31"))
)
print(f"TimeSet operation: {elapsed:.6f}s, found {len(result)} dates")

# Example: Benchmark Serie creation
data = np.random.randn(10000)
result, elapsed = benchmark_operation(
    Serie, data=data, first_date=Date("y2020m01d01"), dating=TimeSet("daily")
)
print(f"Serie creation: {elapsed:.6f}s for {len(data)} points")
```

---

## TOL vs Python Comparison

### Syntax Comparison

#### Creating Time Series
```python
# TOL Syntax
"""
Serie GDP = ReadCSV("gdp_data.csv", "Date", "GDP");
Serie GDPGrowth = Diff(GDP, 1) / Lag(GDP, 1);
Matrix ACF_result = ACF(GDPGrowth, 20);
"""

# Python Equivalent
import pandas as pd
from series import Serie
from stats import statistics

# Read data
df = pd.read_csv("gdp_data.csv")
gdp_serie = Serie(data=df['GDP'].values)

# Compute growth rate
gdp_lagged = gdp_serie.lag(1)
gdp_growth = (gdp_serie - gdp_lagged) / gdp_lagged

# Autocorrelation function
acf_result = statistics.autocorr_function(gdp_growth, max_lag=20)
```

#### ARIMA Modeling
```python
# TOL Syntax
"""
ARIMAFactor factor = ARIMAFactor(1, 1, 1);
ARIMA model = ARIMA(factor);
ARIMAResults results = model.Fit(GDPGrowth, "MLE");
WriteModel(results, "arima_results.txt");
"""

# Python Equivalent
from arima import ARIMA, ARIMAFactor

factor = ARIMAFactor(ar_order=1, diff_order=1, ma_order=1)
model = ARIMA(factor)
results = model.fit(gdp_growth, method="mle")
print(results.summary())
```

#### Bayesian Estimation
```python
# TOL Syntax (BysMcmc framework)
"""
BysMcmc::@Config config = [[
  Real mcmc.burnin = 1000;
  Real mcmc.sampleLength = 5000;
  Real mcmc.cacheLength = 500;
]];

NameBlock priors = [[
  Real ar_prior = Normal(0.0, 0.5);
  Real sigma_prior = InverseGamma(3.0, 2.0);
]];

BayesianARIMA bayes_model = BayesianARIMA(factor, priors, config);
BayesianResults bayes_results = bayes_model.Fit(GDPGrowth);
"""

# Python Equivalent
from bayesian import BayesianARIMA, MCMCConfig
from bayesian.priors import NormalPrior, InverseGammaPrior

config = MCMCConfig(
    mcmc_burnin=1000,
    mcmc_sample_length=5000,
    mcmc_cache_length=500
)

priors = {
    'ar_0': NormalPrior(mean=0.0, variance=0.25),
    'sigma2': InverseGammaPrior(shape=3.0, scale=2.0)
}

bayes_model = BayesianARIMA(factor, priors=priors, config=config)
bayes_results = bayes_model.fit(gdp_growth)
```

### Feature Comparison Table

| Feature | TOL | Python Implementation | Notes |
|---------|-----|----------------------|-------|
| **Time Series Creation** | `Serie` | `Serie` | Same class name and concept |
| **Date Handling** | `Date`, `TimeSet` | `Date`, `TimeSet` | Full compatibility |
| **Missing Values** | `BMissing` | `numpy.ma` | Automatic handling |
| **Basic Operations** | `+`, `-`, `*`, `/` | `+`, `-`, `*`, `/` | Operator overloading |
| **Lags/Differences** | `Lag()`, `Diff()` | `.lag()`, `.diff()` | Method-based |
| **Statistics** | Built-in functions | `stats.statistics` | Module organization |
| **Statistical Tests** | Built-in | `stats.tests` | Comprehensive suite |
| **ARIMA Estimation** | `ARIMA` class | `ARIMA` class | Same interface |
| **Bayesian MCMC** | `BysMcmc` | `bayesian.mcmc` | Enhanced diagnostics |
| **Model Selection** | Manual comparison | `ARIMAModelSelection` | Automated selection |

### Migration Guide

#### Converting TOL Scripts to Python

1. **Import Mapping**
```python
# TOL includes become Python imports
# TOL: #Include "Statistics.tol"
# Python:
from stats import statistics, tests

# TOL: #Include "ARIMA.tol"  
# Python:
from arima import ARIMA, ARIMAFactor

# TOL: #Include "BysMcmc.tol"
# Python:
from bayesian import BayesianARIMA, MCMCConfig
```

2. **Function Name Changes**
```python
# ACF computation
# TOL: Matrix acf = ACF(series, 20);
# Python:
acf = statistics.autocorr_function(series, max_lag=20)

# Statistical tests
# TOL: Set jb_test = JarqueBera(series);
# Python:
jb_stat, jb_pvalue = tests.jarque_bera_test(series)
```

3. **Configuration Objects**
```python
# TOL NameBlock configuration
# TOL:
"""
BysMcmc::@Config config = [[
  Real mcmc.burnin = 1000;
  Real mcmc.sampleLength = 5000;
]];
"""

# Python equivalent
config = MCMCConfig(
    mcmc_burnin=1000,
    mcmc_sample_length=5000
)
```

4. **Error Handling**
```python
# TOL: Automatic error propagation
# Python: Explicit try-catch for robust code
try:
    results = model.fit(series)
    if results.converged:
        print("Model fitted successfully")
    else:
        print("Convergence issues detected")
except Exception as e:
    print(f"Model fitting failed: {e}")
```

### Hierarchical ARIMAX with Input Variables

The framework supports sophisticated hierarchical Bayesian ARIMAX models where you can add input variables and define custom hierarchical priors:

```python
from bayesian import BayesianARIMA, MCMCConfig
from bayesian.priors import NormalPrior, InverseGammaPrior, HierarchicalPrior
from example_hierarchical_arimax import HierarchicalBayesianARIMAX

# Create input variables as Serie objects
unemployment_serie = Serie(data=unemployment_data, dates=dates)
oil_price_serie = Serie(data=oil_data, dates=dates)
interest_rate_serie = Serie(data=interest_data, dates=dates)

input_variables = {
    'unemployment': unemployment_serie,
    'oil_prices': oil_price_serie,
    'interest_rates': interest_rate_serie
}

# Define multi-level hierarchical priors
hierarchical_priors = {
    # Level 2: Group variance hyperpriors
    'tau2_ar': InverseGammaPrior(shape=3.0, scale=2.0, name="AR_group_variance"),
    'tau2_macro': InverseGammaPrior(shape=4.0, scale=1.0, name="MACRO_group_variance"),
    'tau2_financial': InverseGammaPrior(shape=3.0, scale=0.5, name="FINANCIAL_group_variance"),
    
    # Level 1: Parameter priors with hierarchical structure
    'ar_0': HierarchicalPrior(
        parameter_prior=NormalPrior(0.3, 1.0, name="AR1"),
        hyperprior=InverseGammaPrior(3.0, 2.0, name="AR_variance"),
        name="AR1_hierarchical"
    ),
    
    # Economic variables with informative priors
    'beta_unemployment': HierarchicalPrior(
        parameter_prior=NormalPrior(-0.5, 1.0, name="BETA_unemployment"),  # Okun's law
        hyperprior=InverseGammaPrior(4.0, 1.0, name="MACRO_variance"),
        name="BETA_unemployment_hierarchical"
    ),
    
    'beta_oil_prices': HierarchicalPrior(
        parameter_prior=NormalPrior(-0.0001, 1.0, name="BETA_oil"),
        hyperprior=InverseGammaPrior(4.0, 1.0, name="MACRO_variance"),
        name="BETA_oil_hierarchical"
    ),
    
    'beta_interest_rates': HierarchicalPrior(
        parameter_prior=NormalPrior(-0.2, 1.0, name="BETA_interest"),
        hyperprior=InverseGammaPrior(3.0, 0.5, name="FINANCIAL_variance"),
        name="BETA_interest_hierarchical"
    ),
    
    # Other parameters
    'intercept': NormalPrior(0.005, 0.01, name="GDP_trend"),
    'sigma2': InverseGammaPrior(shape=5.0, scale=0.00005, name="error_variance")
}

# ARIMAX model: AR(1) with input variables
factor = ARIMAFactor(ar_order=1, diff_order=0, ma_order=0)

# Advanced MCMC configuration
config = MCMCConfig(
    mcmc_burnin=2000,
    mcmc_sample_length=8000,
    mcmc_cache_length=1000,
    adaptation_period=1000,
    compute_diagnostics=True,
    random_seed=2024
)

# Create and fit hierarchical ARIMAX
hierarchical_arimax = HierarchicalBayesianARIMAX(
    factor=factor,
    input_variables=input_variables,
    priors=hierarchical_priors,
    config=config
)

# Fit to target series (e.g., GDP growth)
results = hierarchical_arimax.fit_data(gdp_growth_serie)

# Run MCMC sampling
from bayesian.mcmc import GibbsSampler
sampler = GibbsSampler(hierarchical_arimax, config)
mcmc_results = sampler.run_chain()
```

#### Analyzing Hierarchical Results

```python
# Extract parameter estimates
parameter_store = mcmc_results['parameter_store']

# Level 1: Individual parameter effects
ar_samples = parameter_store.get_history('ar_0')
unemployment_samples = parameter_store.get_history('beta_unemployment')
oil_samples = parameter_store.get_history('beta_oil_prices')
interest_samples = parameter_store.get_history('beta_interest_rates')

# Posterior summaries
print("Parameter Estimates:")
print(f"AR(1) coefficient: {np.mean(ar_samples):.4f} ± {np.std(ar_samples):.4f}")
print(f"Unemployment effect: {np.mean(unemployment_samples):.4f} ± {np.std(unemployment_samples):.4f}")
print(f"Oil price effect: {np.mean(oil_samples):.6f} ± {np.std(oil_samples):.6f}")
print(f"Interest rate effect: {np.mean(interest_samples):.4f} ± {np.std(interest_samples):.4f}")

# Level 2: Group-level variances (reveal parameter heterogeneity)
ar_group_var = parameter_store.get_history('tau2_ar')
macro_group_var = parameter_store.get_history('tau2_macro')
financial_group_var = parameter_store.get_history('tau2_financial')

print("\nHierarchical Group Variances:")
print(f"AR group variance: {np.mean(ar_group_var):.4f}")
print(f"Macro variables variance: {np.mean(macro_group_var):.4f}")
print(f"Financial variables variance: {np.mean(financial_group_var):.4f}")

# Economic interpretation
unemployment_effect = np.mean(unemployment_samples)
oil_effect_per_dollar = np.mean(oil_samples)

print("\nEconomic Interpretation:")
print(f"1pp unemployment increase → {unemployment_effect*100:.2f}% GDP growth change")
print(f"$10 oil price increase → {oil_effect_per_dollar*10*100:.3f}% GDP growth change")

# Check for significant effects (95% credible intervals)
for param_name, samples in [
    ('Unemployment', unemployment_samples),
    ('Oil prices', oil_samples),
    ('Interest rates', interest_samples)
]:
    ci_lower, ci_upper = np.quantile(samples, [0.025, 0.975])
    significant = not (ci_lower <= 0 <= ci_upper)
    direction = "positive" if np.mean(samples) > 0 else "negative"
    
    print(f"{param_name}: {direction} effect, {'significant' if significant else 'not significant'}")
```

#### Scenario-Based Forecasting

```python
# Define economic scenarios
scenarios = {
    'Baseline': {
        'unemployment_change': 0.0,
        'oil_price_change': 0.0,
        'interest_rate_change': 0.0,
        'description': 'Current conditions continue'
    },
    'Economic Expansion': {
        'unemployment_change': -1.0,  # 1pp decrease
        'oil_price_change': 5.0,      # $5 increase
        'interest_rate_change': 0.5,  # 50bp increase
        'description': 'Strong growth with policy tightening'
    },
    'Recession Risk': {
        'unemployment_change': 2.0,   # 2pp increase
        'oil_price_change': -15.0,    # $15 decrease
        'interest_rate_change': -1.0, # 100bp decrease
        'description': 'Economic downturn with policy easing'
    }
}

# Generate forecasts for each scenario
forecast_horizon = 8  # 2 years
scenario_forecasts = {}

for scenario_name, scenario in scenarios.items():
    forecasts = []
    n_samples = 200
    
    for i in range(n_samples):
        # Sample parameters from posterior
        ar_coef = ar_samples[i]
        unemp_coef = unemployment_samples[i]
        oil_coef = oil_samples[i]
        rate_coef = interest_samples[i]
        intercept = parameter_store.get_history('intercept')[i]
        sigma = np.sqrt(parameter_store.get_history('sigma2')[i])
        
        # Generate forecast path under scenario
        forecast_path = []
        current_gdp = gdp_growth_serie._data.to_numpy()[-1]
        
        for h in range(forecast_horizon):
            # Scenario effects
            unemp_effect = unemp_coef * scenario['unemployment_change']
            oil_effect = oil_coef * scenario['oil_price_change']
            rate_effect = rate_coef * scenario['interest_rate_change']
            
            # Forecast
            forecast = (intercept + ar_coef * current_gdp + 
                       unemp_effect + oil_effect + rate_effect +
                       np.random.normal(0, sigma))
            
            forecast_path.append(forecast)
            current_gdp = forecast
        
        forecasts.append(forecast_path)
    
    # Compute forecast statistics
    forecasts = np.array(forecasts)
    scenario_forecasts[scenario_name] = {
        'mean': np.mean(forecasts, axis=0),
        'lower': np.quantile(forecasts, 0.05, axis=0),
        'upper': np.quantile(forecasts, 0.95, axis=0)
    }

# Display scenario comparison
print("Scenario-Based Forecasts (Annual GDP Growth):")
for scenario_name, forecast in scenario_forecasts.items():
    annual_growth = np.mean(forecast['mean'][:4]) * 400  # Annualized
    annual_lower = np.mean(forecast['lower'][:4]) * 400
    annual_upper = np.mean(forecast['upper'][:4]) * 400
    
    print(f"{scenario_name}: {annual_growth:.2f}% [{annual_lower:.2f}%, {annual_upper:.2f}%]")
```

#### Key Features of Hierarchical ARIMAX:

1. **Flexible Input Variables**: Add any Serie objects as predictors
2. **Multi-Level Priors**: Define group-level variances for parameter shrinkage
3. **Economic Constraints**: Incorporate domain knowledge through informative priors
4. **Automatic Scaling**: Priors automatically adjust to data characteristics
5. **Full Uncertainty**: Complete posterior distributions for all parameters
6. **Model Comparison**: Compare specifications with different input sets
7. **Scenario Analysis**: Generate forecasts under alternative assumptions

This framework enables sophisticated econometric modeling where:
- **Parameter borrowing** occurs across similar variables
- **Economic theory** guides prior specification
- **Uncertainty** is properly quantified at all levels
- **Policy scenarios** can be systematically analyzed

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Convergence Problems
```python
# Problem: MCMC chain not converging
# Symptoms: Low effective sample size, high autocorrelation

# Solution 1: Increase burn-in and samples
config = MCMCConfig(
    mcmc_burnin=2000,      # Increase burn-in
    mcmc_sample_length=10000,  # More samples
    thin_interval=2        # Thin samples
)

# Solution 2: Adjust priors
# Use more informative priors
better_priors = {
    'ar_0': NormalPrior(0.0, 0.3),  # Tighter prior
    'sigma2': InverseGammaPrior(5.0, 3.0)  # More informative
}

# Solution 3: Check for model identification
# Ensure model is not over-parameterized
factor = ARIMAFactor(1, 0, 1)  # Simpler model

# Diagnostic check
def check_convergence(results):
    if results.effective_sample_sizes:
        min_ess = min(results.effective_sample_sizes.values())
        if min_ess < 100:
            print(f"Warning: Low ESS ({min_ess:.1f})")
            return False
    return True
```

#### 2. Numerical Issues
```python
# Problem: Numerical instability, infinite values

# Solution 1: Data preprocessing
def preprocess_series(serie):
    # Remove extreme outliers
    data = serie._data.to_numpy()
    q1, q3 = np.quantile(data[~np.isnan(data)], [0.01, 0.99])
    
    # Winsorize extreme values
    data = np.clip(data, q1, q3)
    return Serie(data=data)

# Solution 2: Robust parameter bounds
# Add constraints to prevent extreme parameter values
model.add_constraint('box', ['ar_0'], bounds=(-0.99, 0.99))
model.add_constraint('positive', ['sigma2'])

# Solution 3: Alternative parameterization
# Use log-normal for variance instead of inverse-gamma
log_sigma_prior = NormalPrior(-2.0, 1.0, name="log_sigma")  # log(σ²)
```

#### 3. Performance Issues
```python
# Problem: Slow MCMC sampling

# Solution 1: Reduce sample size for testing
test_config = MCMCConfig(
    mcmc_burnin=200,
    mcmc_sample_length=500
)

# Solution 2: Use simpler models
# Start with AR(1) before trying complex models
simple_factor = ARIMAFactor(1, 0, 0)

# Solution 3: Block sampling
# Ensure related parameters are sampled together
model.add_parameter_block("ar_block", ["ar_0", "ar_1"], "ar_coefficients")

# Solution 4: Progress monitoring
def progress_callback(iteration, total):
    if iteration % 100 == 0:
        print(f"Progress: {iteration}/{total} ({100*iteration/total:.1f}%)")
```

#### 4. Model Selection Issues
```python
# Problem: All models have similar fit

# Solution 1: Use cross-validation
def time_series_cv(serie, factor, n_folds=5):
    n = len(serie)
    fold_size = n // n_folds
    scores = []
    
    for i in range(n_folds):
        # Split data
        train_end = n - (n_folds - i) * fold_size
        train_data = serie.subseries(0, train_end)
        test_data = serie.subseries(train_end, train_end + fold_size)
        
        # Fit and predict
        model = ARIMA(factor)
        results = model.fit(train_data)
        forecasts, _, _ = model.forecast(len(test_data))
        
        # Compute score
        test_values = test_data._data.to_numpy()
        mse = np.mean((forecasts - test_values)**2)
        scores.append(mse)
    
    return np.mean(scores)

# Solution 2: Information criteria differences
# Only trust IC differences > 2
dic_diff = best_dic - second_best_dic
if dic_diff < 2:
    print("Models are essentially equivalent")

# Solution 3: Economic interpretation
# Choose model that makes economic sense
```

### Debugging Tools

#### MCMC Diagnostics
```python
def detailed_diagnostics(results):
    """Comprehensive MCMC diagnostic report"""
    print("=== DETAILED MCMC DIAGNOSTICS ===")
    
    # Effective sample sizes
    if results.effective_sample_sizes:
        print("\nEffective Sample Sizes:")
        for param, ess in results.effective_sample_sizes.items():
            efficiency = ess / results.n_samples
            status = "✓" if efficiency > 0.1 else "⚠" if efficiency > 0.05 else "✗"
            print(f"  {param}: {ess:.1f} ({efficiency:.1%}) {status}")
    
    # Convergence tests
    if results.mcmc_diagnostics:
        print("\nConvergence Tests:")
        for param, diag in results.mcmc_diagnostics.items():
            if 'geweke' in diag:
                geweke = diag['geweke']
                status = "✓" if geweke['p_value'] > 0.05 else "⚠"
                print(f"  {param} Geweke: Z={geweke['z_score']:.3f}, p={geweke['p_value']:.3f} {status}")
    
    # Parameter summaries
    print(f"\nPosterior Summaries:")
    if results.ar_posterior_mean is not None:
        for i, (mean, std) in enumerate(zip(results.ar_posterior_mean, results.ar_posterior_std)):
            print(f"  AR{i+1}: {mean:.4f} ± {std:.4f}")

# Usage
detailed_diagnostics(bayesian_results)
```

#### Parameter Trace Plots
```python
def plot_traces(results):
    """Plot MCMC traces for visual inspection"""
    try:
        import matplotlib.pyplot as plt
        
        if results.ar_samples is not None:
            n_params = results.ar_samples.shape[1]
            fig, axes = plt.subplots(n_params, 1, figsize=(10, 2*n_params))
            
            if n_params == 1:
                axes = [axes]
            
            for i in range(n_params):
                axes[i].plot(results.ar_samples[:, i])
                axes[i].set_title(f'AR{i+1} Coefficient Trace')
                axes[i].set_ylabel('Value')
                axes[i].grid(True, alpha=0.3)
            
            axes[-1].set_xlabel('Iteration')
            plt.tight_layout()
            plt.show()
            
    except ImportError:
        print("Matplotlib not available for plotting")

# Usage  
plot_traces(bayesian_results)
```

### Performance Benchmarks

#### Expected Performance
```python
# Typical performance benchmarks
benchmarks = {
    "Serie creation (1000 obs)": "< 1ms",
    "Basic arithmetic operations": "< 1ms", 
    "ACF computation (20 lags)": "< 10ms",
    "Classical ARIMA(1,1,1) fit": "< 100ms",
    "Bayesian ARIMA (1000 samples)": "1-5 seconds",
    "Model selection (5 models)": "5-30 seconds"
}

print("Performance Benchmarks:")
for operation, expected_time in benchmarks.items():
    print(f"  {operation}: {expected_time}")
```

#### Memory Usage
```python
def memory_usage_estimate(n_observations, n_mcmc_samples):
    """Estimate memory usage for Bayesian ARIMA"""
    
    # Serie storage
    serie_memory = n_observations * 8  # 8 bytes per float64
    
    # MCMC samples (assuming 3 parameters)
    mcmc_memory = n_mcmc_samples * 3 * 8
    
    # Diagnostic storage
    diagnostic_memory = n_mcmc_samples * 8  # ACF storage
    
    total_mb = (serie_memory + mcmc_memory + diagnostic_memory) / (1024**2)
    
    print(f"Estimated memory usage:")
    print(f"  Data: {n_observations} observations")
    print(f"  MCMC: {n_mcmc_samples} samples")
    print(f"  Total: {total_mb:.1f} MB")
    
    return total_mb

# Example
memory_usage_estimate(1000, 5000)
```

---

## Conclusion

This guide provides a comprehensive introduction to TOL Python's Bayesian time series analysis capabilities. You now have the tools to:

✅ **Create and manipulate time series** with full TOL compatibility  
✅ **Perform statistical analysis** with professional-grade tests and diagnostics  
✅ **Fit classical ARIMA models** with maximum likelihood estimation  
✅ **Implement sophisticated Bayesian models** with hierarchical priors  
✅ **Use MCMC sampling** with comprehensive convergence diagnostics  
✅ **Compare and select models** using information criteria and Bayes factors  
✅ **Generate forecasts** with proper uncertainty quantification  

### Next Steps

1. **Practice with your data**: Start with the basic examples and gradually move to more complex models
2. **Experiment with priors**: Understanding prior sensitivity is crucial for Bayesian analysis
3. **Validate results**: Always check convergence diagnostics and model adequacy
4. **Extend the framework**: The modular design allows for easy extension to new models

### Getting Help

- **Documentation**: Refer to individual module docstrings for detailed API reference
- **Examples**: Use the test suite as additional examples of usage patterns
- **Community**: Share experiences and best practices with other users

The TOL Python framework provides a powerful foundation for modern econometric analysis, combining the proven methodology of TOL with the flexibility and ecosystem of Python.

---

*Happy modeling! 📈🔬*