"""
Complex-valued time series implementation
Extends Serie to support complex numbers, matching TOL's BCTimeSeries
"""

import numpy as np
import numpy.ma as ma
from typing import Union, Optional, Tuple, List
from ..series import Serie
from ..core import Date, TimeSet, Data, DatingType


class ComplexData(Data):
    """
    Data container for complex-valued time series
    Extends Data class to support complex numbers
    """
    
    def __init__(self, size: Optional[int] = None, data: Optional[np.ndarray] = None):
        if data is not None:
            if isinstance(data, list):
                # Convert list to complex array, preserving None as NaN
                complex_data = []
                for x in data:
                    if x is None:
                        complex_data.append(complex(np.nan, np.nan))
                    elif isinstance(x, complex):
                        complex_data.append(x)
                    elif isinstance(x, (int, float)):
                        complex_data.append(complex(x, 0))
                    else:
                        # Try to convert to complex
                        try:
                            complex_data.append(complex(x))
                        except (TypeError, ValueError):
                            complex_data.append(complex(np.nan, np.nan))
                
                self._data = ma.array(complex_data, dtype=complex)
            else:
                # Ensure complex dtype
                if data.dtype != complex:
                    data = data.astype(complex)
                self._data = ma.array(data)
        elif size is not None:
            # Create empty complex array
            self._data = ma.empty(size, dtype=complex)
            self._data[:] = ma.masked
        else:
            self._data = ma.array([], dtype=complex)
    
    def real_part(self) -> Data:
        """Extract real part as regular Data object"""
        real_data = Data()
        real_values = np.real(self._data.filled(complex(np.nan, np.nan)))
        real_data._data = ma.array(real_values)
        # Preserve mask
        real_data._data.mask = self._data.mask
        return real_data
    
    def imag_part(self) -> Data:
        """Extract imaginary part as regular Data object"""
        imag_data = Data()
        imag_values = np.imag(self._data.filled(complex(np.nan, np.nan)))
        imag_data._data = ma.array(imag_values)
        # Preserve mask
        imag_data._data.mask = self._data.mask
        return imag_data
    
    def magnitude(self) -> Data:
        """Calculate magnitude (absolute value) as regular Data object"""
        mag_data = Data()
        mag_values = np.abs(self._data.filled(complex(np.nan, np.nan)))
        mag_data._data = ma.array(mag_values)
        # Preserve mask
        mag_data._data.mask = self._data.mask
        return mag_data
    
    def phase(self) -> Data:
        """Calculate phase angle as regular Data object"""
        phase_data = Data()
        phase_values = np.angle(self._data.filled(complex(np.nan, np.nan)))
        phase_data._data = ma.array(phase_values)
        # Preserve mask
        phase_data._data.mask = self._data.mask
        return phase_data
    
    def conjugate(self) -> 'ComplexData':
        """Calculate complex conjugate"""
        conj_data = ComplexData()
        conj_data._data = ma.conjugate(self._data)
        return conj_data


class ComplexSerie(Serie):
    """
    Complex-valued time series
    
    Extends Serie to support complex numbers, providing all the functionality
    of regular series plus complex-specific operations like magnitude, phase,
    and complex arithmetic.
    
    Equivalent to TOL's BCTimeSeries for complex-time-indexed series.
    """
    
    def __init__(self, 
                 data: Optional[Union[List, np.ndarray, ComplexData]] = None,
                 real_data: Optional[Union[List, np.ndarray]] = None,
                 imag_data: Optional[Union[List, np.ndarray]] = None,
                 first_date: Optional[Union[Date, str]] = None,
                 last_date: Optional[Union[Date, str]] = None,
                 dating: Optional[TimeSet] = None,
                 dating_type: DatingType = DatingType.FIXED):
        """
        Initialize a ComplexSerie
        
        Args:
            data: Complex data (list, numpy array, or ComplexData object)
            real_data: Real part data (if providing real/imag separately)
            imag_data: Imaginary part data (if providing real/imag separately)
            first_date: First date of the series
            last_date: Last date of the series
            dating: TimeSet defining the dating pattern
            dating_type: Whether dating is fixed or volatile
        """
        # Handle different input formats
        if data is not None:
            if isinstance(data, ComplexData):
                complex_data = data
            else:
                complex_data = ComplexData(data=data)
        elif real_data is not None or imag_data is not None:
            # Combine real and imaginary parts
            real_array = np.array(real_data) if real_data is not None else np.zeros(len(imag_data))
            imag_array = np.array(imag_data) if imag_data is not None else np.zeros(len(real_data))
            
            if len(real_array) != len(imag_array):
                raise ValueError("Real and imaginary parts must have same length")
            
            combined = real_array + 1j * imag_array
            complex_data = ComplexData(data=combined)
        else:
            complex_data = ComplexData()
        
        # Initialize base Serie with complex data
        super().__init__(data=None, first_date=first_date, last_date=last_date, 
                        dating=dating, dating_type=dating_type)
        
        # Replace the regular data with complex data
        self._data = complex_data
    
    @classmethod
    def from_real_imag(cls, real_serie: Serie, imag_serie: Serie) -> 'ComplexSerie':
        """
        Create ComplexSerie from separate real and imaginary series
        
        Args:
            real_serie: Serie containing real parts
            imag_serie: Serie containing imaginary parts
            
        Returns:
            ComplexSerie with combined data
        """
        # Ensure both series have same date range
        first = real_serie.first_date if real_serie.first_date > imag_serie.first_date else imag_serie.first_date
        last = real_serie.last_date if real_serie.last_date < imag_serie.last_date else imag_serie.last_date
        
        # Extract overlapping data
        real_data = []
        imag_data = []
        current = first
        while current <= last:
            real_val = real_serie[current] if current >= real_serie.first_date and current <= real_serie.last_date else 0
            imag_val = imag_serie[current] if current >= imag_serie.first_date and current <= imag_serie.last_date else 0
            
            real_data.append(real_val if real_val is not None else 0)
            imag_data.append(imag_val if imag_val is not None else 0)
            
            current = real_serie.dating.successor(current)
        
        return cls(real_data=real_data, imag_data=imag_data,
                  first_date=first, last_date=last, dating=real_serie.dating)
    
    @classmethod
    def from_polar(cls, magnitude_serie: Serie, phase_serie: Serie) -> 'ComplexSerie':
        """
        Create ComplexSerie from magnitude and phase series
        
        Args:
            magnitude_serie: Serie containing magnitudes
            phase_serie: Serie containing phase angles (radians)
            
        Returns:
            ComplexSerie in rectangular form
        """
        # Ensure both series have same date range
        first = magnitude_serie.first_date if magnitude_serie.first_date > phase_serie.first_date else phase_serie.first_date
        last = magnitude_serie.last_date if magnitude_serie.last_date < phase_serie.last_date else phase_serie.last_date
        
        # Convert polar to rectangular
        complex_data = []
        current = first
        while current <= last:
            mag = magnitude_serie[current] if current >= magnitude_serie.first_date and current <= magnitude_serie.last_date else 0
            phase = phase_serie[current] if current >= phase_serie.first_date and current <= phase_serie.last_date else 0
            
            if mag is None or phase is None:
                complex_data.append(None)
            else:
                complex_val = mag * np.exp(1j * phase)
                complex_data.append(complex_val)
            
            current = magnitude_serie.dating.successor(current)
        
        return cls(data=complex_data, first_date=first, last_date=last, dating=magnitude_serie.dating)
    
    def real(self) -> Serie:
        """Extract real part as a regular Serie"""
        real_data = self._data.real_part()
        return Serie(data=real_data, first_date=self.first_date, last_date=self.last_date,
                    dating=self.dating, dating_type=self._dating_type)
    
    def imag(self) -> Serie:
        """Extract imaginary part as a regular Serie"""
        imag_data = self._data.imag_part()
        return Serie(data=imag_data, first_date=self.first_date, last_date=self.last_date,
                    dating=self.dating, dating_type=self._dating_type)
    
    def abs(self) -> Serie:
        """Calculate magnitude (absolute value) as a regular Serie"""
        mag_data = self._data.magnitude()
        return Serie(data=mag_data, first_date=self.first_date, last_date=self.last_date,
                    dating=self.dating, dating_type=self._dating_type)
    
    def angle(self) -> Serie:
        """Calculate phase angle as a regular Serie"""
        phase_data = self._data.phase()
        return Serie(data=phase_data, first_date=self.first_date, last_date=self.last_date,
                    dating=self.dating, dating_type=self._dating_type)
    
    def conjugate(self) -> 'ComplexSerie':
        """Calculate complex conjugate"""
        conj_data = self._data.conjugate()
        return ComplexSerie(data=conj_data, first_date=self.first_date, last_date=self.last_date,
                           dating=self.dating, dating_type=self._dating_type)
    
    def __add__(self, other: Union['ComplexSerie', Serie, complex, float]) -> 'ComplexSerie':
        """Addition with another ComplexSerie, Serie, or scalar"""
        if isinstance(other, ComplexSerie):
            # Complex + Complex
            return self._complex_arithmetic(other, np.add)
        elif isinstance(other, Serie):
            # Complex + Real (convert Real to Complex)
            other_complex = ComplexSerie.from_real_imag(other, Serie(data=np.zeros(len(other))))
            return self._complex_arithmetic(other_complex, np.add)
        else:
            # Complex + Scalar
            return self._scalar_arithmetic(complex(other), np.add)
    
    def __sub__(self, other: Union['ComplexSerie', Serie, complex, float]) -> 'ComplexSerie':
        """Subtraction"""
        if isinstance(other, ComplexSerie):
            return self._complex_arithmetic(other, np.subtract)
        elif isinstance(other, Serie):
            other_complex = ComplexSerie.from_real_imag(other, Serie(data=np.zeros(len(other))))
            return self._complex_arithmetic(other_complex, np.subtract)
        else:
            return self._scalar_arithmetic(complex(other), np.subtract)
    
    def __mul__(self, other: Union['ComplexSerie', Serie, complex, float]) -> 'ComplexSerie':
        """Multiplication"""
        if isinstance(other, ComplexSerie):
            return self._complex_arithmetic(other, np.multiply)
        elif isinstance(other, Serie):
            other_complex = ComplexSerie.from_real_imag(other, Serie(data=np.zeros(len(other))))
            return self._complex_arithmetic(other_complex, np.multiply)
        else:
            return self._scalar_arithmetic(complex(other), np.multiply)
    
    def __truediv__(self, other: Union['ComplexSerie', Serie, complex, float]) -> 'ComplexSerie':
        """Division"""
        if isinstance(other, ComplexSerie):
            return self._complex_arithmetic(other, np.divide)
        elif isinstance(other, Serie):
            other_complex = ComplexSerie.from_real_imag(other, Serie(data=np.zeros(len(other))))
            return self._complex_arithmetic(other_complex, np.divide)
        else:
            return self._scalar_arithmetic(complex(other), np.divide)
    
    def _complex_arithmetic(self, other: 'ComplexSerie', operation) -> 'ComplexSerie':
        """Perform arithmetic operation between two ComplexSeries"""
        # Find common date range
        first = self.first_date if self.first_date > other.first_date else other.first_date
        last = self.last_date if self.last_date < other.last_date else other.last_date
        
        if first > last:
            return ComplexSerie()  # Empty series
        
        # Perform operation
        result_data = []
        current = first
        while current <= last:
            val1 = self[current]
            val2 = other[current]
            
            if val1 is None or val2 is None:
                result_data.append(None)
            else:
                try:
                    result = operation(val1, val2)
                    result_data.append(result)
                except (ZeroDivisionError, ValueError):
                    result_data.append(None)
            
            current = self.dating.successor(current)
        
        return ComplexSerie(data=result_data, first_date=first, last_date=last,
                           dating=self.dating, dating_type=self._dating_type)
    
    def _scalar_arithmetic(self, scalar: complex, operation) -> 'ComplexSerie':
        """Perform arithmetic operation with a scalar"""
        result_data = []
        current = self.first_date
        while current <= self.last_date:
            val = self[current]
            if val is None:
                result_data.append(None)
            else:
                try:
                    result = operation(val, scalar)
                    result_data.append(result)
                except (ZeroDivisionError, ValueError):
                    result_data.append(None)
            
            current = self.dating.successor(current)
        
        return ComplexSerie(data=result_data, first_date=self.first_date, last_date=self.last_date,
                           dating=self.dating, dating_type=self._dating_type)
    
    def power(self, exponent: Union[complex, float, int]) -> 'ComplexSerie':
        """Raise to a power"""
        return self._scalar_arithmetic(complex(exponent), np.power)
    
    def exp(self) -> 'ComplexSerie':
        """Complex exponential"""
        result_data = []
        current = self.first_date
        while current <= self.last_date:
            val = self[current]
            if val is None:
                result_data.append(None)
            else:
                result_data.append(np.exp(val))
            current = self.dating.successor(current)
        
        return ComplexSerie(data=result_data, first_date=self.first_date, last_date=self.last_date,
                           dating=self.dating, dating_type=self._dating_type)
    
    def log(self) -> 'ComplexSerie':
        """Complex logarithm"""
        result_data = []
        current = self.first_date
        while current <= self.last_date:
            val = self[current]
            if val is None or val == 0:
                result_data.append(None)
            else:
                result_data.append(np.log(val))
            current = self.dating.successor(current)
        
        return ComplexSerie(data=result_data, first_date=self.first_date, last_date=self.last_date,
                           dating=self.dating, dating_type=self._dating_type)
    
    def to_rectangular(self) -> Tuple[Serie, Serie]:
        """
        Convert to rectangular form (real, imaginary)
        
        Returns:
            Tuple of (real_serie, imag_serie)
        """
        return self.real(), self.imag()
    
    def to_polar(self) -> Tuple[Serie, Serie]:
        """
        Convert to polar form (magnitude, phase)
        
        Returns:
            Tuple of (magnitude_serie, phase_serie)
        """
        return self.abs(), self.angle()
    
    def copy(self) -> 'ComplexSerie':
        """Create a deep copy of the ComplexSerie"""
        return ComplexSerie(data=self._data.copy(),
                           first_date=self.first_date,
                           last_date=self.last_date,
                           dating=self.dating,
                           dating_type=self._dating_type)
    
    def __repr__(self) -> str:
        if self.is_empty():
            return "ComplexSerie(empty)"
        elif self.is_stochastic():
            return f"ComplexSerie({self.first_date} to {self.last_date}, {self.length} obs)"
        else:
            return f"ComplexSerie({self.length} obs)"
    
    def __str__(self) -> str:
        """String representation showing some complex data"""
        if self.is_empty():
            return "Empty ComplexSerie"
        
        lines = [f"ComplexSerie from {self.first_date} to {self.last_date}"]
        lines.append(f"Dating: {self._dating.frequency}")
        lines.append(f"Length: {self.length}")
        
        # Show first few values
        if self.is_stochastic():
            lines.append("\nFirst values:")
            current = self.first_date
            for i in range(min(5, self.length)):
                value = self[current]
                if value is None:
                    lines.append(f"  {current}: <missing>")
                else:
                    # Format complex number nicely
                    real_part = np.real(value)
                    imag_part = np.imag(value)
                    if imag_part >= 0:
                        lines.append(f"  {current}: {real_part:.3f}+{imag_part:.3f}j")
                    else:
                        lines.append(f"  {current}: {real_part:.3f}{imag_part:.3f}j")
                current = self.dating.successor(current)
            
            if self.length > 5:
                lines.append("  ...")
        
        return "\n".join(lines)