"""
Statistics module for TOL Python
This provides the 'statistics' namespace used in the user guide
"""

# Import all statistical functions
from stats import (
    mean, variance, std_dev, standard_deviation,
    covariance, correlation,
    autocorrelation, autocorr_function,
    partial_autocorrelation, partial_autocorr_function,
    cross_correlation,
    skewness, kurtosis,
    quantile, median, interquartile_range,
    SerieStatistics
)

__all__ = [
    'mean', 'variance', 'std_dev', 'standard_deviation',
    'covariance', 'correlation',
    'autocorrelation', 'autocorr_function',
    'partial_autocorrelation', 'partial_autocorr_function',
    'cross_correlation',
    'skewness', 'kurtosis',
    'quantile', 'median', 'interquartile_range',
    'SerieStatistics'
]