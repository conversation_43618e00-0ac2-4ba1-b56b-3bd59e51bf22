# TOL Python - Time Series Implementation

A Python port of the TOL (Time-Oriented Language) Serie class and its ecosystem, providing time series functionality with TOL's unique features.

## Features

- **TOL-Compatible Serie Class**: Full implementation of time series with flexible dating
- **Date/Time System**: TOL's date format (y2023m01d15) with Begin/End sentinels
- **Flexible Dating**: Support for daily, weekly, monthly, quarterly, and yearly frequencies
- **Missing Values**: Native support for missing data
- **Operations**: Arithmetic, lag/lead, differences, moving averages
- **I/O Formats**: Binary (TOL-compatible), CSV, JSON
- **Integration**: NumPy arrays internally, pandas conversion

## Installation

```bash
# Clone and navigate to the directory
cd tol_python

# Install dependencies (optional, for pandas integration)
pip install numpy pandas
```

## Quick Start

```python
from tol_python import Serie, Date, TimeSet

# Create a simple daily series
s = Serie(data=[100, 102, 105, 103, 107],
          first_date="y2023m01d01",
          last_date="y2023m01d05")

# Access values by date
print(s["y2023m01d03"])  # 105

# Arithmetic operations
s2 = s * 1.1  # Scale by 10%
s3 = s + s2   # Add two series

# Time operations
s_lag = s.lag(1)          # Lag by one period
s_diff = s.diff()         # First difference
s_ma = s.moving_average(3) # 3-period moving average

# Aggregations
print(s.sum())   # Sum of all values
print(s.mean())  # Mean value

# I/O Operations
s.to_json("myserie.json")
s_loaded = Serie.from_json("myserie.json")
```

## TOL Compatibility Examples

### Creating Series (TOL vs Python)

TOL:
```tol
Serie A = SubSer(Rand(1,2,C), y2000, y2001);
Real sum_a = SumS(A);
```

Python:
```python
import numpy as np
A = Serie(data=np.random.uniform(1, 2, 365),
          first_date="y2000m01d01", 
          last_date="y2000m12d31")
sum_a = A.sum()
```

### Operations

TOL:
```tol
Serie C = A + B;
Serie D = (1-B) * A;  // First difference of A
```

Python:
```python
C = A + B
D = A.diff()  # First difference of A
```

## Architecture

The implementation mirrors TOL's C++ architecture:

```
tol_python/
├── core/           # Basic types (Date, TimeSet, Data)
├── series/         # Serie class and operations
├── io/             # Input/output functionality
└── tests/          # Test suite
```

### Key Classes

- **Date**: TOL-compatible date with Begin/End sentinels
- **TimeSet**: Dating patterns (daily, monthly, etc.)
- **Serie**: Main time series class
- **Data**: Efficient data storage with missing value support

## Advanced Usage

### Monthly Series with Missing Values

```python
# Create monthly serie with gaps
monthly = Serie(data=[100, 102, None, 106, 110],
                first_date="y2023m01d01",
                last_date="y2023m05d01",
                dating=TimeSet("monthly"))

# Operations handle missing values
print(monthly.mean())  # Ignores missing values
```

### Custom Date Ranges

```python
# Extract subseries
sub = s.sub_ser("y2023m01d02", "y2023m01d04")

# Access by index
first_value = s[0]
last_value = s[-1]
```

### Pandas Integration

```python
# Convert to pandas
df = s.to_pandas()

# Create from pandas (with daily frequency)
import pandas as pd
dates = pd.date_range('2023-01-01', periods=5)
values = [1, 2, 3, 4, 5]
s = Serie(data=values, 
          first_date="y2023m01d01",
          last_date="y2023m01d05")
```

## Performance Considerations

- Uses NumPy arrays internally for efficient computation
- Lazy evaluation where possible
- Memory-efficient storage of sparse series

## Differences from TOL

1. **Memory Management**: Uses Python's garbage collection instead of TOL's reference counting
2. **Syntax**: Pythonic method names (e.g., `sum()` instead of `SumS()`)
3. **Type System**: Dynamic typing vs TOL's static types
4. **Integration**: Native Python/NumPy/pandas integration

## Future Enhancements

- [ ] Complex series support (BCTimeSeries)
- [ ] ARIMA modeling functions
- [ ] Database connectivity
- [ ] Parallel operations
- [ ] More statistical functions

## License

This implementation follows the same license as the original TOL project.