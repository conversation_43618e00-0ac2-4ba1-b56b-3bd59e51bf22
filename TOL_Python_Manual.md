# TOL Python Manual

This manual provides examples and use cases for the tol_python package, mapping functionality from the original TOL language to the Python implementation.

## Table of Contents

1. [Syntax](#1-syntax)
2. [Sets and Collections](#2-sets-and-collections)  
3. [Statistics](#3-statistics)
4. [Time Variables](#4-time-variables)
5. [Work in Progress (WIP) Features](#5-work-in-progress-wip-features)

## 1. Syntax

### Comments and Basic Operations

In Python, comments use `#` instead of TOL's `//` and `/* */`:

```python
# This creates a variable 'sum' with the result of adding 1+1
sum_value = 1 + 1
```

### Variable Assignment

Python uses `=` for assignment, similar to TOL:

```python
a = 1.5
b = "Hello"
```

### Reassignment

Python allows direct reassignment without special operators:

```python
a = 1
a = 2  # Reassigns 'a'
```

### Numbers (Real)

Python handles real numbers natively:

```python
import numpy as np

a = 1
b = 0.25
c = np.sqrt(3)
d = -1/2
```

### Unknown/Missing Values

In tol_python, missing values are represented using NumPy's NaN:

```python
import numpy as np

n = np.nan  # Equivalent to TOL's ?
result1 = np.sqrt(-1)  # Returns NaN in real arithmetic
result2 = np.log(0)    # Returns -inf
result3 = np.arcsin(3) # Returns NaN
result4 = 0/0          # Returns NaN
```

### Mathematical Operations

```python
import numpy as np

# Generate a random number between 0 and 1000
x = np.random.uniform(0, 1000)

# Find integer and decimal parts
n = np.floor(x)
d = x - n

# Common approximation: log(1+x) ≈ x when x is small
log_1_plus_d = np.log(1 + d)
relative_error = np.abs(log_1_plus_d - d) / log_1_plus_d * 100  # in %

# Calculate Pi
my_pi = 4 * np.arctan(1)
```

### Complex Numbers

```python
import numpy as np

# Create complex number
z = 3 - 2j  # or complex(3, -2)

# Real and imaginary parts
z_re = z.real  # 3
z_im = z.imag  # -2

# Modulus and argument
z_mod = np.abs(z)    # 5
z_arg = np.angle(z)  # 0.9272952180016122

# Conjugate
z = 3 + 2j
u = z / np.abs(z)
uC = np.conj(u)  # or u.conjugate()

# Complex functions
c1 = np.sqrt(-1+0j)    # 1j
c2 = np.log(-0.5+0j)   # (-0.693147180559945+3.14159265358979j)
```

### Text/Strings

```python
# String with quotes
text1 = 'The word "city" contains all vowels'
text2 = "There are two types of letters:\n * Uppercase.\n * Lowercase."

# Print messages
print("Hello world")

# String operations
string = "language:es"
position = string.find(":")
length = len(string)
language = string[position+1:length]
print(f"Language configuration: '{language}'.")
```

### Functions

```python
import numpy as np

def integer_part_length(number):
    """Returns the number of digits in the integer part of a real number"""
    unsigned_integer_part = np.floor(np.abs(number))
    # Determine number of digits using base 10
    return np.floor(np.log10(unsigned_integer_part)) + 1

def print_hello():
    """Prints a greeting"""
    print("Hello!")
    return None  # Python functions can return None
```

### Local Scope

```python
scope = "Global"

def calculate_value():
    scope = "Local"
    print(f"Scope 1: {scope}")
    phi = np.random.uniform(0, np.pi)
    return np.sin(phi)**2 + np.cos(phi)**2

value = calculate_value()  # Returns 1
print(f"Scope 2: {scope}")  # Prints "Global"
```

## 2. Sets and Collections

### Lists (Similar to TOL Sets)

```python
# Create lists
digits = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
even_numbers = list(range(2, 21, 2))
vowels = ["a", "e", "i", "o", "u"]

# Access elements
first_color = colors[0]  # 0-indexed in Python

# Basic operations
s1 = ["A", "E", "I", "O", "U"]
s2 = ["Y", "W"]

# Concatenate
s = s1 + s2

# Remove last element
s_size = len(s)
s.pop()  # or del s[-1]

# Append elements
s.extend(["J", "K"])  # or s += ["J", "K"]
s_size = len(s)
print(f"Final size of 's': {s_size}")

# Empty list
my_elements = []

# Set operations (using Python sets)
c1 = set("conjunto")
c2 = set("disjunto")

# Remove duplicates
s1 = set(c1)  # Automatically removes duplicates
s2 = set(c2)

# Set algebra
union = s1 | s2         # or s1.union(s2)
intersection = s1 & s2  # or s1.intersection(s2)
s1_minus_s2 = s1 - s2  # or s1.difference(s2)
s2_minus_s1 = s2 - s1

# Indexed collections (using dictionaries)
weekdays = {
    "monday": 101,
    "tuesday": 102,
    "wednesday": 103,
    "thursday": 104,
    "friday": 105,
    "saturday": 106,
    "sunday": 107
}

# Access by name
friday_value = weekdays["friday"]  # 105
```

### Control Flow

```python
# Conditional (if)
result = 1 if 3+4==7 else 0

# Logical operations
import numpy as np

a = round(np.random.rand())
b = np.random.uniform(0, 6)
c = np.random.uniform(0, 9)

condition = (a == 1) and (b > 3 or c <= 5)
not_condition = not condition

# While loop
print_count = 0
rand_number = int(np.random.uniform(0, 100))

while print_count < 100 and rand_number != 0:
    print(f"{rand_number:2d}")
    print_count += 1
    rand_number = int(np.random.uniform(0, 100))

# For loop
table = [[n, n**2] for n in range(1, 6)]

# Operations on collections
chars = list("Hello world")
asciis = [ord(t) for t in chars]

# Selection
sample = [np.random.normal(0, 1) for _ in range(1000)]
subsample = [x for x in sample if x > -1]

# Sorting
chars = list("conjunto")
sorted_chars = sorted(chars)  # Alphabetically

# Classification/Grouping
from itertools import groupby

chars = list("conjunto")
groups = {k: list(v) for k, v in groupby(sorted(chars))}
for char, group in groups.items():
    print(f"Group '{char}' has {len(group)} elements")
```

## 3. Statistics

### Descriptive Statistics

Using the tol_python statistics module:

```python
from tol_python import statistics as stats
import numpy as np

# Basic statistics (handles missing values)
values = [2, 3, np.nan, 4, np.nan]
sum_val = stats.sum(values)  # 9
avg_val = stats.mean(values)  # 3

# Statistics on arrays
values = np.array([1.0, -0.3, 0, 2.1, 0.9])
mu = stats.mean(values)
sigma2 = stats.variance(values)
```

### Linear Models

**WIP**: Linear regression models (`LinReg`), Logit, and Probit models are not yet implemented in tol_python.

### Virtual Matrices

```python
import numpy as np
from tol_python import statistics as stats

# Generate Gaussian matrix
a = np.random.normal(0, 0.5, (100, 1))
a_mu = stats.mean(a)
a_sigma = stats.std(a)
```

## 4. Time Variables

### Dates

Using the tol_python Date class:

```python
from tol_python.core.dates import Date

# Create dates
d1 = Date("y1992m07d25")    # 25/07/1992
d2 = Date("y2000")          # 01/01/2000
d3 = Date("y2007m03")       # 01/03/2007

# Dates from datetime objects
from datetime import datetime
d4 = Date(datetime(2011, 11, 11))           # 11/11/2011 00:00:00
d5 = Date(datetime(2000, 2, 4, 6, 8, 10))   # 04/02/2000 06:08:10
d6 = Date(datetime(2012, 9, 10, 13))        # 10/09/2012 13:00:00

# Date arithmetic
# Add days
future_date = d1.add_days(10)
past_date = d1.add_days(-5)

# Add months (handles day overflow correctly)
next_month = d1.add_months(1)
next_year = d1.add_months(12)
```

### TimeSets

Using tol_python TimeSet classes:

```python
from tol_python.core.dates import *

# Basic timesets
daily = DayTimeSet()
monthly = MonthTimeSet()
yearly = YearTimeSet()

# Date operations
mar_2012 = Date("y2012m03")
prev_day = daily.predecessor(mar_2012)  # 2012-02-29 (leap year)

# Time algebra
# Custom quarterly
my_quarterly = TimeSetUnion([
    TimeSetIntersection([DayTimeSet(1), MonthTimeSet(1)]),
    TimeSetIntersection([DayTimeSet(1), MonthTimeSet(4)]),
    TimeSetIntersection([DayTimeSet(1), MonthTimeSet(7)]),
    TimeSetIntersection([DayTimeSet(1), MonthTimeSet(10)])
])

# Monday to Friday
monday_to_friday = TimeSetDifference(
    daily, 
    TimeSetUnion([WeekDayTimeSet(6), WeekDayTimeSet(7)])
)

# Range of dates
range_2000_2012 = RangeTimeSet(Date("y2000"), Date("y2012m12"), monthly)

# Periodic dates (e.g., all Mondays) - Note: PeriodicTimeSet is WIP (missing predecessor)
# mondays = PeriodicTimeSet(Date("y2012m09d10"), 7, daily)

# Last days of month
last_days = LastDayOfMonthTimeSet()
```

### Time Series (Serie)

Using tol_python Serie class:

```python
from tol_python.series import Serie
from tol_python.core.dates import *
import numpy as np

# Create unlimited series
pulse_2012 = Serie.pulse(Date("y2012"), yearly)

# Series for first day of each month
day1 = Serie.cal_ind(monthly, daily)

# Number of days in each month
num_days = Serie.cal_var(daily, monthly)

# Limited series
limited_pulse = Serie.pulse(Date("y2012"), yearly).sub_ser(Date("y2000"), Date("y2020"))

# Create series from data
data = np.array([
    [1.2, 2.4, 1.5, 1.0, 3.1, 2.0],
    [0.8, 7.1, 1.1, 4.2, 5.1, 2.2]
])
series_set = Serie.from_matrix(data, yearly, Date("y2000"))

# Series operations
zeros = Serie.cal_ind(0, yearly).sub_ser(Date("y2000"), Date("y2008"))
ones = Serie.cal_ind(1, yearly).sub_ser(Date("y2005"), Date("y2012"))

# Concatenation (with different priorities)
cc1 = zeros >> ones  # Right priority
cc2 = zeros << ones  # Left priority

# Replace missing values
series_data = [1, 2, np.nan, 1, 3, np.nan, 4, 2]
series = Serie.from_matrix([series_data], yearly, Date("y2000"))[0]
filled = series.fillna(0)  # Replace NaN with 0

# Statistics on series
from tol_python import statistics as stats

gaussian_series = Serie.from_matrix(
    np.random.normal(0, 1, (156, 1)), 
    monthly, 
    Date("y2000")
)[0].sub_ser(Date("y2000"), Date("y2012m12"))

mean = stats.mean(gaussian_series)
variance_2011 = stats.variance(gaussian_series, Date("y2011"), Date("y2011m12"))
```

### Lag Operations and Polynomials

**WIP**: Polynomial lag operations (`Polyn`, `B` operator) are not yet implemented in tol_python. Basic lag operations can be performed using Serie methods:

```python
# Basic lag operation
x_t = Serie.from_list(range(1, 9), yearly, Date("y2001"))
x_t_minus_1 = x_t.shift(1)  # Lag by 1 period
diff_x_t = x_t - x_t_minus_1  # First difference
```

### Time Series Modeling

ARIMA models are implemented in tol_python:

```python
from tol_python.arima import ARIMA, AutoARIMA

# Create ARIMA model
model = ARIMA(order=(2, 0, 0))  # ARIMA(2,0,0)

# Fit model
results = model.fit(series_data)

# Automatic ARIMA selection
auto_model = AutoARIMA(max_p=5, max_q=5, seasonal=False)
best_model = auto_model.fit(series_data)

# Forecast
forecast = results.forecast(steps=12)

# Model diagnostics
print(f"AIC: {results.aic}")
print(f"BIC: {results.bic}")
residuals = results.resid
```

### Bayesian ARIMA

```python
from tol_python.bayesian import BayesianARIMA
from tol_python.bayesian.priors import NormalPrior

# Create Bayesian ARIMA
bayes_model = BayesianARIMA(
    order=(2, 0, 0),
    ar_prior=NormalPrior(0, 1),
    sigma2_prior=InverseGammaPrior(2, 1)
)

# Fit with MCMC
results = bayes_model.fit(series_data, n_iter=10000, n_burnin=2000)

# Posterior analysis
posterior_mean = results.posterior_mean()
credible_intervals = results.credible_interval(0.95)
```

## 5. Work in Progress (WIP) Features

The following TOL features are not yet implemented in tol_python:

### 1. General Data Types
- **Matrix and VMatrix**: Full matrix algebra operations
- **Polynomial and PolMat**: Polynomial operations
- **Ratio**: Ratio of polynomials
- **NameBlock**: Namespace/object structures
- **Struct**: Custom structure definitions

### 2. Mathematical Operations
- **Polynomial arithmetic**: Operations with B (backshift) operator
- **Matrix operations**: Advanced linear algebra beyond NumPy
- **Sparse matrix support**: VMatrix equivalent

### 3. Statistical Models
- **LinReg**: Linear regression models
- **Logit/Probit**: Generalized linear models
- **BSR**: Bayesian State-space Regression

### 4. Advanced Time Series
- **SARIMA**: Full seasonal ARIMA support
- **DifEq**: Difference equation solving
- **Transfer functions**: Input-output models

### 5. Database and I/O
- **Database connectivity**: SQL support
- **OIS**: Object Information System
- **Advanced file formats**: TOL native format

### 6. Other Features
- **WriteLn**: Direct console output formatting
- **Sleep**: Execution delays
- **Code evaluation**: Dynamic code execution
- **Set algebra**: Full set operations beyond lists

---

## Summary

The tol_python package provides a robust implementation of TOL's core time series functionality, including:
- Flexible date and time handling
- Comprehensive time series operations
- Statistical analysis tools
- ARIMA modeling (classical and Bayesian)
- Frequency domain analysis

While some advanced features from TOL are not yet implemented, the package offers a solid foundation for time series analysis in Python with TOL-like syntax and semantics where possible.

For the latest updates and contributions, please refer to the project repository.