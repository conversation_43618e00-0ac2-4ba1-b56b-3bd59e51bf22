"""
FFT and frequency domain operations
Implements TOL's FFTW-based frequency operations using NumPy/SciPy
"""

import numpy as np
import numpy.fft as fft
from typing import Tuple, Optional, Union
from scipy import signal
import warnings

from ..series import Serie
from ..complex import ComplexSerie
from ..complex.complex_serie import ComplexData
from ..core import Date, TimeSet


class FrequencyDomain:
    """
    Fast Fourier Transform and frequency domain operations
    
    Provides FFT operations similar to TOL's FFTW integration,
    using NumPy's optimized FFT implementation.
    """
    
    @staticmethod
    def fft(serie: Serie, n: Optional[int] = None, 
            norm: Optional[str] = None) -> ComplexSerie:
        """
        Compute Fast Fourier Transform of a time series
        
        Args:
            serie: Input time series
            n: Length of FFT (zero-padded if needed)
            norm: Normalization mode ("backward", "ortho", "forward")
            
        Returns:
            ComplexSerie containing FFT coefficients
        """
        # Extract values, handling missing data
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)  # Zero-pad missing values
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) == 0:
            return ComplexSerie()
        
        # Perform FFT
        values_array = np.array(values)
        fft_result = fft.fft(values_array, n=n, norm=norm)
        
        # Create frequency-domain serie
        # FFT output has same length as input (or n if specified)
        result_serie = ComplexSerie()
        result_serie._data = ComplexData(data=fft_result)
        result_serie._length = len(fft_result)
        return result_serie
    
    @staticmethod
    def ifft(complex_serie: ComplexSerie, n: Optional[int] = None,
             norm: Optional[str] = None) -> Serie:
        """
        Compute Inverse Fast Fourier Transform
        
        Args:
            complex_serie: ComplexSerie with frequency domain data
            n: Length of IFFT
            norm: Normalization mode
            
        Returns:
            Serie containing time domain signal
        """
        # Extract complex values directly from data
        if len(complex_serie._data._data) == 0:
            return Serie()
        
        values_array = complex_serie._data._data.filled(complex(0, 0))
        
        # Perform IFFT
        ifft_result = fft.ifft(values_array, n=n, norm=norm)
        
        # Take real part (should be real if input was real)
        real_result = np.real(ifft_result)
        
        return Serie(data=real_result)
    
    @staticmethod
    def rfft(serie: Serie, n: Optional[int] = None,
             norm: Optional[str] = None) -> ComplexSerie:
        """
        Compute FFT for real-valued input (more efficient)
        
        Returns only positive frequencies (negative frequencies are redundant)
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) == 0:
            return ComplexSerie()
        
        # Perform real FFT
        values_array = np.array(values)
        rfft_result = fft.rfft(values_array, n=n, norm=norm)
        
        result_serie = ComplexSerie()
        result_serie._data = ComplexData(data=rfft_result)
        result_serie._length = len(rfft_result)
        return result_serie
    
    @staticmethod
    def irfft(complex_serie: ComplexSerie, n: Optional[int] = None,
              norm: Optional[str] = None) -> Serie:
        """
        Compute inverse FFT for real-valued output
        """
        # Extract complex values directly from data
        if len(complex_serie._data._data) == 0:
            return Serie()
        
        values_array = complex_serie._data._data.filled(complex(0, 0))
        irfft_result = fft.irfft(values_array, n=n, norm=norm)
        
        return Serie(data=irfft_result)
    
    @staticmethod
    def fftfreq(n: int, d: float = 1.0) -> Serie:
        """
        Generate frequency coordinates for FFT
        
        Args:
            n: Number of samples
            d: Sample spacing (inverse of sample rate)
            
        Returns:
            Serie containing frequency values
        """
        freqs = fft.fftfreq(n, d)
        return Serie(data=freqs)
    
    @staticmethod
    def rfftfreq(n: int, d: float = 1.0) -> Serie:
        """
        Generate frequency coordinates for real FFT
        """
        freqs = fft.rfftfreq(n, d)
        return Serie(data=freqs)
    
    @staticmethod
    def periodogram(serie: Serie, window: str = 'hann',
                   detrend: str = 'linear') -> Tuple[Serie, Serie]:
        """
        Compute periodogram (power spectral density estimate)
        
        Args:
            serie: Input time series
            window: Window function to apply
            detrend: Detrending method ('linear', 'constant', None)
            
        Returns:
            Tuple of (frequencies, power_spectrum)
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) < 2:
            return Serie(), Serie()
        
        values_array = np.array(values)
        
        # Compute periodogram
        freqs, power = signal.periodogram(values_array, window=window, detrend=detrend)
        
        return Serie(data=freqs), Serie(data=power)
    
    @staticmethod
    def welch_psd(serie: Serie, nperseg: Optional[int] = None,
                  window: str = 'hann', overlap: Optional[int] = None) -> Tuple[Serie, Serie]:
        """
        Compute power spectral density using Welch's method
        
        Args:
            serie: Input time series
            nperseg: Length of each segment
            window: Window function
            overlap: Number of points to overlap between segments
            
        Returns:
            Tuple of (frequencies, power_spectral_density)
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) < 2:
            return Serie(), Serie()
        
        values_array = np.array(values)
        
        # Set default parameters
        if nperseg is None:
            nperseg = min(256, len(values) // 4)
        if overlap is None:
            overlap = nperseg // 2
        
        # Compute Welch PSD
        freqs, psd = signal.welch(values_array, nperseg=nperseg, 
                                 window=window, noverlap=overlap)
        
        return Serie(data=freqs), Serie(data=psd)
    
    @staticmethod
    def spectrogram(serie: Serie, nperseg: Optional[int] = None,
                   window: str = 'hann') -> Tuple[Serie, Serie, np.ndarray]:
        """
        Compute spectrogram (time-frequency representation)
        
        Args:
            serie: Input time series
            nperseg: Length of each segment
            window: Window function
            
        Returns:
            Tuple of (frequencies, times, spectrogram_matrix)
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) < 2:
            return Serie(), Serie(), np.array([[]])
        
        values_array = np.array(values)
        
        # Set default parameters
        if nperseg is None:
            nperseg = min(256, len(values) // 8)
        
        # Compute spectrogram
        freqs, times, Sxx = signal.spectrogram(values_array, nperseg=nperseg, window=window)
        
        return Serie(data=freqs), Serie(data=times), Sxx
    
    @staticmethod
    def cross_spectrum(serie1: Serie, serie2: Serie,
                      nperseg: Optional[int] = None) -> Tuple[Serie, ComplexSerie]:
        """
        Compute cross power spectral density between two series
        
        Args:
            serie1, serie2: Input time series
            nperseg: Length of each segment
            
        Returns:
            Tuple of (frequencies, cross_spectrum)
        """
        # Extract values from both series
        # Find common date range
        first = serie1.first_date if serie1.first_date > serie2.first_date else serie2.first_date
        last = serie1.last_date if serie1.last_date < serie2.last_date else serie2.last_date
        
        if first > last:
            return Serie(), ComplexSerie()
        
        values1, values2 = [], []
        current = first
        while current <= last:
            val1 = serie1[current] if current >= serie1.first_date and current <= serie1.last_date else 0
            val2 = serie2[current] if current >= serie2.first_date and current <= serie2.last_date else 0
            
            values1.append(val1 if val1 is not None else 0.0)
            values2.append(val2 if val2 is not None else 0.0)
            
            current = serie1.dating.successor(current)
        
        if len(values1) < 2:
            return Serie(), ComplexSerie()
        
        values1_array = np.array(values1)
        values2_array = np.array(values2)
        
        # Set default parameters
        if nperseg is None:
            nperseg = min(256, len(values1) // 4)
        
        # Compute cross spectrum
        freqs, Pxy = signal.csd(values1_array, values2_array, nperseg=nperseg)
        
        cross_spec = ComplexSerie()
        cross_spec._data = ComplexData(data=Pxy)
        cross_spec._length = len(Pxy)
        
        return Serie(data=freqs), cross_spec
    
    @staticmethod
    def coherence(serie1: Serie, serie2: Serie,
                 nperseg: Optional[int] = None) -> Tuple[Serie, Serie]:
        """
        Compute magnitude squared coherence between two series
        
        Args:
            serie1, serie2: Input time series
            nperseg: Length of each segment
            
        Returns:
            Tuple of (frequencies, coherence)
        """
        # Extract values (same logic as cross_spectrum)
        first = serie1.first_date if serie1.first_date > serie2.first_date else serie2.first_date
        last = serie1.last_date if serie1.last_date < serie2.last_date else serie2.last_date
        
        if first > last:
            return Serie(), Serie()
        
        values1, values2 = [], []
        current = first
        while current <= last:
            val1 = serie1[current] if current >= serie1.first_date and current <= serie1.last_date else 0
            val2 = serie2[current] if current >= serie2.first_date and current <= serie2.last_date else 0
            
            values1.append(val1 if val1 is not None else 0.0)
            values2.append(val2 if val2 is not None else 0.0)
            
            current = serie1.dating.successor(current)
        
        if len(values1) < 2:
            return Serie(), Serie()
        
        values1_array = np.array(values1)
        values2_array = np.array(values2)
        
        # Set default parameters
        if nperseg is None:
            nperseg = min(256, len(values1) // 4)
        
        # Compute coherence
        freqs, Cxy = signal.coherence(values1_array, values2_array, nperseg=nperseg)
        
        return Serie(data=freqs), Serie(data=Cxy)