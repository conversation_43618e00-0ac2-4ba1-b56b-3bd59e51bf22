"""
Frequency domain filtering operations
Implements various filters for time series analysis
"""

import numpy as np
from typing import Tuple, Optional, Union, List
from scipy import signal
from scipy.optimize import minimize_scalar
import warnings

from ..series import Serie
from ..core import Date


class FrequencyFilters:
    """
    Frequency domain and time domain filtering operations
    
    Provides filtering capabilities commonly used in econometric analysis,
    including the Hodrick-Prescott filter and various digital filters.
    """
    
    @staticmethod
    def hodrick_prescott(serie: Serie, lambda_param: float = 1600) -> Tuple[Serie, Serie]:
        """
        Hodrick-<PERSON> filter for trend-cycle decomposition
        
        Decomposes a time series into trend and cyclical components by solving:
        min sum((y_t - tau_t)^2) + lambda * sum((tau_{t+1} - tau_t) - (tau_t - tau_{t-1}))^2
        
        Args:
            serie: Input time series
            lambda_param: Smoothing parameter (1600 for quarterly, 14400 for annual, 129600 for monthly)
            
        Returns:
            Tuple of (trend_serie, cycle_serie)
        """
        # Extract values
        values = []
        dates = []
        current = serie.first_date
        
        while current <= serie.last_date:
            value = serie[current]
            if value is not None and not np.isnan(value):
                values.append(float(value))
                dates.append(current)
            current = serie.dating.successor(current)
        
        if len(values) < 4:
            # Need at least 4 observations for HP filter
            trend_data = [np.nan] * len(values)
            cycle_data = [np.nan] * len(values)
        else:
            values_array = np.array(values)
            n = len(values_array)
            
            # Build the filter matrix
            # The HP filter solves: (I + lambda * K'K) * trend = y
            # where K is the second difference operator
            
            # Create second difference matrix
            K = np.zeros((n-2, n))
            for i in range(n-2):
                K[i, i] = 1
                K[i, i+1] = -2
                K[i, i+2] = 1
            
            # Create identity matrix
            I = np.eye(n)
            
            # Solve for trend: trend = (I + lambda * K'K)^(-1) * y
            A = I + lambda_param * K.T @ K
            trend_values = np.linalg.solve(A, values_array)
            cycle_values = values_array - trend_values
            
            trend_data = trend_values.tolist()
            cycle_data = cycle_values.tolist()
        
        # Create result series with original dates
        trend_serie = Serie(data=trend_data,
                           first_date=serie.first_date,
                           last_date=serie.last_date,
                           dating=serie.dating)
        
        cycle_serie = Serie(data=cycle_data,
                           first_date=serie.first_date,
                           last_date=serie.last_date,
                           dating=serie.dating)
        
        return trend_serie, cycle_serie
    
    @staticmethod
    def bandpass_filter(serie: Serie, low_freq: float, high_freq: float,
                       filter_type: str = 'butterworth', order: int = 4) -> Serie:
        """
        Apply bandpass filter to retain frequencies in specified range
        
        Args:
            serie: Input time series
            low_freq: Lower cutoff frequency (normalized, 0 < low_freq < 1)
            high_freq: Higher cutoff frequency (normalized, low_freq < high_freq < 1)
            filter_type: Type of filter ('butterworth', 'chebyshev1', 'ellip')
            order: Filter order
            
        Returns:
            Filtered time series
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)  # Zero-pad missing values
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) < 2 * order:
            warnings.warn("Insufficient data for filtering")
            return serie.copy()
        
        values_array = np.array(values)
        
        # Design bandpass filter
        if filter_type.lower() == 'butterworth':
            sos = signal.butter(order, [low_freq, high_freq], btype='band', output='sos')
        elif filter_type.lower() == 'chebyshev1':
            sos = signal.cheby1(order, 1, [low_freq, high_freq], btype='band', output='sos')
        elif filter_type.lower() == 'ellip':
            sos = signal.ellip(order, 1, 40, [low_freq, high_freq], btype='band', output='sos')
        else:
            raise ValueError(f"Unknown filter type: {filter_type}")
        
        # Apply filter
        filtered_values = signal.sosfilt(sos, values_array)
        
        return Serie(data=filtered_values,
                    first_date=serie.first_date,
                    last_date=serie.last_date,
                    dating=serie.dating)
    
    @staticmethod
    def lowpass_filter(serie: Serie, cutoff_freq: float,
                      filter_type: str = 'butterworth', order: int = 4) -> Serie:
        """
        Apply lowpass filter to remove high frequencies
        
        Args:
            serie: Input time series
            cutoff_freq: Cutoff frequency (normalized, 0 < cutoff_freq < 1)
            filter_type: Type of filter
            order: Filter order
            
        Returns:
            Filtered time series
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) < 2 * order:
            warnings.warn("Insufficient data for filtering")
            return serie.copy()
        
        values_array = np.array(values)
        
        # Design lowpass filter
        if filter_type.lower() == 'butterworth':
            sos = signal.butter(order, cutoff_freq, btype='low', output='sos')
        elif filter_type.lower() == 'chebyshev1':
            sos = signal.cheby1(order, 1, cutoff_freq, btype='low', output='sos')
        elif filter_type.lower() == 'ellip':
            sos = signal.ellip(order, 1, 40, cutoff_freq, btype='low', output='sos')
        else:
            raise ValueError(f"Unknown filter type: {filter_type}")
        
        # Apply filter
        filtered_values = signal.sosfilt(sos, values_array)
        
        return Serie(data=filtered_values,
                    first_date=serie.first_date,
                    last_date=serie.last_date,
                    dating=serie.dating)
    
    @staticmethod
    def highpass_filter(serie: Serie, cutoff_freq: float,
                       filter_type: str = 'butterworth', order: int = 4) -> Serie:
        """
        Apply highpass filter to remove low frequencies
        
        Args:
            serie: Input time series
            cutoff_freq: Cutoff frequency (normalized, 0 < cutoff_freq < 1)
            filter_type: Type of filter
            order: Filter order
            
        Returns:
            Filtered time series
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) < 2 * order:
            warnings.warn("Insufficient data for filtering")
            return serie.copy()
        
        values_array = np.array(values)
        
        # Design highpass filter
        if filter_type.lower() == 'butterworth':
            sos = signal.butter(order, cutoff_freq, btype='high', output='sos')
        elif filter_type.lower() == 'chebyshev1':
            sos = signal.cheby1(order, 1, cutoff_freq, btype='high', output='sos')
        elif filter_type.lower() == 'ellip':
            sos = signal.ellip(order, 1, 40, cutoff_freq, btype='high', output='sos')
        else:
            raise ValueError(f"Unknown filter type: {filter_type}")
        
        # Apply filter
        filtered_values = signal.sosfilt(sos, values_array)
        
        return Serie(data=filtered_values,
                    first_date=serie.first_date,
                    last_date=serie.last_date,
                    dating=serie.dating)
    
    @staticmethod
    def moving_average_filter(serie: Serie, window: int, 
                             window_type: str = 'boxcar') -> Serie:
        """
        Apply moving average filter (convolution-based)
        
        Args:
            serie: Input time series
            window: Window length
            window_type: Type of window ('boxcar', 'hann', 'hamming', 'blackman')
            
        Returns:
            Filtered time series
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) < window:
            warnings.warn("Window larger than data length")
            return serie.copy()
        
        values_array = np.array(values)
        
        # Create window
        if window_type == 'boxcar':
            w = np.ones(window) / window
        elif window_type == 'hann':
            w = signal.windows.hann(window)
            w = w / np.sum(w)
        elif window_type == 'hamming':
            w = signal.windows.hamming(window)
            w = w / np.sum(w)
        elif window_type == 'blackman':
            w = signal.windows.blackman(window)
            w = w / np.sum(w)
        else:
            raise ValueError(f"Unknown window type: {window_type}")
        
        # Apply convolution
        filtered_values = np.convolve(values_array, w, mode='same')
        
        return Serie(data=filtered_values,
                    first_date=serie.first_date,
                    last_date=serie.last_date,
                    dating=serie.dating)
    
    @staticmethod
    def savitzky_golay_filter(serie: Serie, window_length: int, polyorder: int,
                             deriv: int = 0) -> Serie:
        """
        Apply Savitzky-Golay filter for smoothing
        
        Args:
            serie: Input time series
            window_length: Length of filter window (must be odd)
            polyorder: Order of polynomial used for fitting
            deriv: Order of derivative to compute (0 for smoothing)
            
        Returns:
            Filtered time series
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                # For S-G filter, we need to handle missing values more carefully
                values.append(np.nan)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        if len(values) < window_length:
            warnings.warn("Window larger than data length")
            return serie.copy()
        
        values_array = np.array(values)
        
        # Handle NaN values by interpolation or exclusion
        valid_mask = ~np.isnan(values_array)
        if np.sum(valid_mask) < window_length:
            warnings.warn("Too many missing values for Savitzky-Golay filter")
            return serie.copy()
        
        # Simple approach: interpolate NaN values
        if np.any(~valid_mask):
            values_array = np.interp(np.arange(len(values_array)), 
                                   np.arange(len(values_array))[valid_mask],
                                   values_array[valid_mask])
        
        # Apply Savitzky-Golay filter
        filtered_values = signal.savgol_filter(values_array, window_length, polyorder, deriv=deriv)
        
        return Serie(data=filtered_values,
                    first_date=serie.first_date,
                    last_date=serie.last_date,
                    dating=serie.dating)
    
    @staticmethod
    def baxter_king_filter(serie: Serie, low_period: float, high_period: float,
                          k: Optional[int] = None) -> Serie:
        """
        Baxter-King bandpass filter for business cycle analysis
        
        Args:
            serie: Input time series
            low_period: Lower bound of periods to retain (e.g., 6 quarters)
            high_period: Upper bound of periods to retain (e.g., 32 quarters)
            k: Lead-lag length of filter (if None, use rule of thumb)
            
        Returns:
            Filtered time series (business cycle component)
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        n = len(values)
        if n < 10:
            warnings.warn("Insufficient data for Baxter-King filter")
            return serie.copy()
        
        # Set default k based on high period
        if k is None:
            k = max(3, min(int(high_period), n // 4))
        
        values_array = np.array(values)
        
        # Convert periods to frequencies
        omega_low = 2 * np.pi / high_period
        omega_high = 2 * np.pi / low_period
        
        # Construct Baxter-King filter weights
        bk_weights = np.zeros(2*k + 1)
        
        # Center weight
        bk_weights[k] = (omega_high - omega_low) / np.pi
        
        # Symmetric weights
        for j in range(1, k + 1):
            bk_weights[k + j] = (np.sin(j * omega_high) - np.sin(j * omega_low)) / (j * np.pi)
            bk_weights[k - j] = bk_weights[k + j]
        
        # Apply filter using convolution
        filtered_values = np.convolve(values_array, bk_weights, mode='same')
        
        # Set boundary observations to NaN (they are less reliable)
        filtered_values[:k] = np.nan
        filtered_values[-k:] = np.nan
        
        return Serie(data=filtered_values,
                    first_date=serie.first_date,
                    last_date=serie.last_date,
                    dating=serie.dating)
    
    @staticmethod
    def christiano_fitzgerald_filter(serie: Serie, low_period: float, high_period: float) -> Serie:
        """
        Christiano-Fitzgerald random walk filter
        
        Asymmetric filter that doesn't lose observations at endpoints
        
        Args:
            serie: Input time series
            low_period: Lower bound of periods to retain
            high_period: Upper bound of periods to retain
            
        Returns:
            Filtered time series
        """
        # Extract values
        values = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or np.isnan(value):
                values.append(0.0)
            else:
                values.append(float(value))
            current = serie.dating.successor(current)
        
        n = len(values)
        if n < 6:
            warnings.warn("Insufficient data for Christiano-Fitzgerald filter")
            return serie.copy()
        
        values_array = np.array(values)
        
        # Convert periods to frequencies
        omega_low = 2 * np.pi / high_period
        omega_high = 2 * np.pi / low_period
        
        # Initialize filtered series
        filtered_values = np.zeros(n)
        
        # For each time point, construct appropriate filter
        for t in range(n):
            # Determine lead and lag lengths
            lead_length = min(t, n - 1 - t, int(high_period / 2))
            
            if lead_length < 2:
                filtered_values[t] = values_array[t]  # No filtering for endpoints
                continue
            
            # Construct weights for this time point
            weights = np.zeros(2 * lead_length + 1)
            
            # Center weight
            weights[lead_length] = (omega_high - omega_low) / np.pi
            
            # Symmetric weights
            for j in range(1, lead_length + 1):
                weight = (np.sin(j * omega_high) - np.sin(j * omega_low)) / (j * np.pi)
                weights[lead_length + j] = weight
                weights[lead_length - j] = weight
            
            # Normalize weights to sum to zero (for stationarity)
            weights = weights - np.mean(weights)
            
            # Apply weights
            start_idx = max(0, t - lead_length)
            end_idx = min(n, t + lead_length + 1)
            weight_start = max(0, lead_length - t)
            weight_end = weight_start + (end_idx - start_idx)
            
            filtered_values[t] = np.sum(values_array[start_idx:end_idx] * 
                                      weights[weight_start:weight_end])
        
        return Serie(data=filtered_values,
                    first_date=serie.first_date,
                    last_date=serie.last_date,
                    dating=serie.dating)