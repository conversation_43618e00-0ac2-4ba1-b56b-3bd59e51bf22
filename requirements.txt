# Core dependencies for TOL Python
numpy>=1.18.0
scipy>=1.5.0

# Optional but recommended for full functionality
pandas>=1.0.0
matplotlib>=3.0.0

# Development dependencies (install with: pip install -e .[dev])
# pytest>=6.0
# pytest-cov
# black
# mypy  
# flake8

# Documentation dependencies (install with: pip install -e .[docs])
# sphinx
# sphinx-rtd-theme

# Full functionality (install with: pip install -e .[full])
# numba
# cvxpy
# statsmodels