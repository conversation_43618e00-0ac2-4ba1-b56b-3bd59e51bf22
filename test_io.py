#!/usr/bin/env python3
"""
Test Serie I/O operations - saving and loading in various formats
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tol_python import Serie, Date, TimeSet
import tempfile
import json


def test_csv_io():
    """Test CSV input/output"""
    print("=== CSV I/O Test ===\n")
    
    # Create a serie with some missing values
    s = Serie(data=[100, 102, None, 105, 108, None, 112],
              first_date="y2023m01d01",
              last_date="y2023m01d07")
    
    print("Original serie:")
    print(s)
    
    # Write to CSV
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        csv_file = f.name
        s.to_csv(f)
    
    # Read it back
    s_loaded = Serie.from_csv(csv_file)
    
    print("\nLoaded from CSV:")
    print(s_loaded)
    
    # Verify values match
    match = True
    current = s.first_date
    while current <= s.last_date:
        orig = s[current]
        loaded = s_loaded[current]
        if (orig is None and loaded is not None) or \
           (orig is not None and loaded is None) or \
           (orig is not None and loaded is not None and abs(orig - loaded) > 1e-10):
            match = False
            break
        current = s.dating.successor(current)
    
    print(f"\nValues match: {match}")
    
    # Clean up
    os.unlink(csv_file)


def test_json_io():
    """Test JSON input/output"""
    print("\n\n=== JSON I/O Test ===\n")
    
    # Create a monthly serie
    s = Serie(data=[1000, 1050, 1100, 1080, 1150, 1200],
              first_date="y2023m01d01",
              last_date="y2023m06d01",
              dating=TimeSet("monthly"))
    
    print("Original serie:")
    print(s)
    
    # Write to JSON
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json_file = f.name
        s.to_json(f)
    
    # Show JSON content
    with open(json_file, 'r') as f:
        json_content = json.load(f)
    print("\nJSON representation:")
    print(json.dumps(json_content, indent=2)[:300] + "...")
    
    # Read it back
    s_loaded = Serie.from_json(json_file)
    
    print("\nLoaded from JSON:")
    print(s_loaded)
    
    # Clean up
    os.unlink(json_file)


def test_binary_io():
    """Test binary input/output"""
    print("\n\n=== Binary I/O Test ===\n")
    
    # Create a serie with many values
    import numpy as np
    data = np.sin(np.linspace(0, 2*np.pi, 100)) + np.random.normal(0, 0.1, 100)
    
    s = Serie(data=data,
              first_date="y2023m01d01",
              last_date="y2023m04d10")
    
    print(f"Original serie: {len(s)} observations")
    print(f"First 5 values: {[s[s._index_to_date(i)] for i in range(5)]}")
    
    # Write to binary
    with tempfile.NamedTemporaryFile(suffix='.tols', delete=False) as f:
        binary_file = f.name
        s.to_binary(f)
    
    # Check file size
    file_size = os.path.getsize(binary_file)
    print(f"\nBinary file size: {file_size} bytes")
    print(f"Expected: ~{4 + 2 + 1 + 8 + 8 + 4 + 100*8} bytes")
    
    # Read it back
    s_loaded = Serie.from_binary(binary_file)
    
    print(f"\nLoaded serie: {len(s_loaded)} observations")
    print(f"First 5 values: {[s_loaded[s_loaded._index_to_date(i)] for i in range(5)]}")
    
    # Verify all values
    match = True
    for i in range(len(s)):
        date = s._index_to_date(i)
        orig = s[date]
        loaded = s_loaded[date]
        if abs(orig - loaded) > 1e-10:
            match = False
            break
    
    print(f"All values match: {match}")
    
    # Clean up
    os.unlink(binary_file)


def test_roundtrip_with_operations():
    """Test that operations work after save/load"""
    print("\n\n=== Roundtrip with Operations Test ===\n")
    
    # Create two series
    s1 = Serie(data=[1, 2, 3, 4, 5],
               first_date="y2023m01d01",
               last_date="y2023m01d05")
    
    s2 = Serie(data=[10, 20, 30, 40, 50],
               first_date="y2023m01d01",
               last_date="y2023m01d05")
    
    # Save to JSON
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json1 = f.name
        s1.to_json(f)
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json2 = f.name
        s2.to_json(f)
    
    # Load them back
    s1_loaded = Serie.from_json(json1)
    s2_loaded = Serie.from_json(json2)
    
    # Test operations
    s_sum = s1_loaded + s2_loaded
    s_mult = s1_loaded * 2
    
    print(f"S1 loaded: {list(s1_loaded.to_dict().values())}")
    print(f"S2 loaded: {list(s2_loaded.to_dict().values())}")
    print(f"S1 + S2: {list(s_sum.to_dict().values())}")
    print(f"S1 * 2: {list(s_mult.to_dict().values())}")
    
    print(f"\nSum of S1: {s1_loaded.sum()}")
    print(f"Mean of S2: {s2_loaded.mean()}")
    
    # Clean up
    os.unlink(json1)
    os.unlink(json2)


if __name__ == "__main__":
    test_csv_io()
    test_json_io()
    test_binary_io()
    test_roundtrip_with_operations()