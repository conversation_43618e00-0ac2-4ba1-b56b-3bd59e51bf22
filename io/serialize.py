"""
Serie I/O operations - saving and loading series
Provides TOL-compatible binary format and modern formats
"""

import struct
import json
import csv
from typing import Union, BinaryIO, TextIO, Dict, Any
from pathlib import Path
import numpy as np

from ..series import Serie
from ..core import Date, TimeSet


class SerieIO:
    """
    Input/Output operations for Serie objects
    Supports TOL binary format and modern interchange formats
    """
    
    @staticmethod
    def write_binary(serie: Serie, file: Union[str, Path, BinaryIO]) -> None:
        """
        Write serie in TOL-compatible binary format
        Format:
        - Magic number (4 bytes): 'TOLS'
        - Version (2 bytes): 0x0001
        - Dating type (1 byte): 0=daily, 1=weekly, 2=monthly, 3=quarterly, 4=yearly
        - First date (8 bytes): timestamp
        - Last date (8 bytes): timestamp  
        - Data length (4 bytes): number of values
        - Data values (8 bytes each): float64 values, NaN for missing
        """
        if isinstance(file, (str, Path)):
            with open(file, 'wb') as f:
                SerieIO.write_binary(serie, f)
            return
        
        # Write magic number
        file.write(b'TOLS')
        
        # Write version
        file.write(struct.pack('<H', 1))
        
        # Write dating type
        dating_map = {'daily': 0, 'weekly': 1, 'monthly': 2, 'quarterly': 3, 'yearly': 4}
        dating_byte = dating_map.get(serie.dating.frequency, 0)
        file.write(struct.pack('B', dating_byte))
        
        # Write dates as timestamps
        first_ts = serie.first_date.to_datetime().timestamp() if serie.first_date.is_normal() else -1
        last_ts = serie.last_date.to_datetime().timestamp() if serie.last_date.is_normal() else -1
        file.write(struct.pack('<d', first_ts))
        file.write(struct.pack('<d', last_ts))
        
        # Write data
        data_list = []
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or (isinstance(value, float) and np.isnan(value)):
                data_list.append(np.nan)
            else:
                data_list.append(float(value))
            current = serie.dating.successor(current)
        
        file.write(struct.pack('<I', len(data_list)))
        for value in data_list:
            file.write(struct.pack('<d', value))
    
    @staticmethod
    def read_binary(file: Union[str, Path, BinaryIO]) -> Serie:
        """Read serie from TOL-compatible binary format"""
        if isinstance(file, (str, Path)):
            with open(file, 'rb') as f:
                return SerieIO.read_binary(f)
        
        # Read and verify magic number
        magic = file.read(4)
        if magic != b'TOLS':
            raise ValueError("Not a valid TOL Serie binary file")
        
        # Read version
        version = struct.unpack('<H', file.read(2))[0]
        if version != 1:
            raise ValueError(f"Unsupported version: {version}")
        
        # Read dating type
        dating_byte = struct.unpack('B', file.read(1))[0]
        dating_map = {0: 'daily', 1: 'weekly', 2: 'monthly', 3: 'quarterly', 4: 'yearly'}
        frequency = dating_map.get(dating_byte, 'daily')
        
        # Read dates
        first_ts = struct.unpack('<d', file.read(8))[0]
        last_ts = struct.unpack('<d', file.read(8))[0]
        
        from datetime import datetime
        first_date = Date(datetime.fromtimestamp(first_ts)) if first_ts >= 0 else Date.begin()
        last_date = Date(datetime.fromtimestamp(last_ts)) if last_ts >= 0 else Date.end()
        
        # Read data
        data_length = struct.unpack('<I', file.read(4))[0]
        data = []
        for _ in range(data_length):
            value = struct.unpack('<d', file.read(8))[0]
            if np.isnan(value):
                data.append(None)
            else:
                data.append(value)
        
        return Serie(data=data,
                    first_date=first_date,
                    last_date=last_date,
                    dating=TimeSet(frequency))
    
    @staticmethod
    def write_csv(serie: Serie, file: Union[str, Path, TextIO], 
                  include_header: bool = True) -> None:
        """
        Write serie to CSV format
        Format: date,value
        """
        if isinstance(file, (str, Path)):
            with open(file, 'w', newline='') as f:
                SerieIO.write_csv(serie, f, include_header)
            return
        
        writer = csv.writer(file)
        
        if include_header:
            writer.writerow(['date', 'value'])
        
        current = serie.first_date
        while current <= serie.last_date:
            value = serie[current]
            if value is None or (isinstance(value, float) and np.isnan(value)):
                value_str = ''
            else:
                value_str = str(value)
            
            writer.writerow([str(current), value_str])
            current = serie.dating.successor(current)
    
    @staticmethod
    def read_csv(file: Union[str, Path, TextIO], 
                 has_header: bool = True,
                 date_column: Union[str, int] = 0,
                 value_column: Union[str, int] = 1,
                 frequency: str = 'daily') -> Serie:
        """
        Read serie from CSV format
        """
        if isinstance(file, (str, Path)):
            with open(file, 'r') as f:
                return SerieIO.read_csv(f, has_header, date_column, value_column, frequency)
        
        reader = csv.reader(file)
        
        if has_header:
            header = next(reader)
            if isinstance(date_column, str):
                date_column = header.index(date_column)
            if isinstance(value_column, str):
                value_column = header.index(value_column)
        
        dates = []
        values = []
        
        for row in reader:
            try:
                date_str = row[date_column]
                date = Date(date_str)
                dates.append(date)
                
                value_str = row[value_column].strip()
                if value_str == '':
                    values.append(None)
                else:
                    values.append(float(value_str))
            except (ValueError, IndexError):
                continue
        
        if dates:
            return Serie(data=values,
                        first_date=dates[0],
                        last_date=dates[-1],
                        dating=TimeSet(frequency))
        else:
            return Serie()
    
    @staticmethod
    def write_json(serie: Serie, file: Union[str, Path, TextIO], 
                   indent: int = 2) -> None:
        """
        Write serie to JSON format
        """
        data = {
            'first_date': str(serie.first_date),
            'last_date': str(serie.last_date),
            'frequency': serie.dating.frequency,
            'values': serie.to_dict()
        }
        
        # Convert numpy types to native Python types
        def convert_types(obj):
            if isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, (np.integer, np.floating)):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj
        
        data = convert_types(data)
        
        if isinstance(file, (str, Path)):
            with open(file, 'w') as f:
                json.dump(data, f, indent=indent)
        else:
            json.dump(data, file, indent=indent)
    
    @staticmethod
    def read_json(file: Union[str, Path, TextIO]) -> Serie:
        """Read serie from JSON format"""
        if isinstance(file, (str, Path)):
            with open(file, 'r') as f:
                data = json.load(f)
        else:
            data = json.load(file)
        
        # Extract values in order
        values_dict = data['values']
        dates = []
        values = []
        
        for date_str, value in values_dict.items():
            dates.append(Date(date_str))
            values.append(value)
        
        return Serie(data=values,
                    first_date=data['first_date'],
                    last_date=data['last_date'],
                    dating=TimeSet(data['frequency']))


# Add I/O methods to Serie class
def _add_io_to_serie():
    """Add I/O methods to Serie class"""
    
    Serie.to_csv = lambda self, file, **kwargs: SerieIO.write_csv(self, file, **kwargs)
    Serie.to_json = lambda self, file, **kwargs: SerieIO.write_json(self, file, **kwargs)
    Serie.to_binary = lambda self, file: SerieIO.write_binary(self, file)
    
    # Class methods for reading
    Serie.from_csv = classmethod(lambda cls, file, **kwargs: SerieIO.read_csv(file, **kwargs))
    Serie.from_json = classmethod(lambda cls, file: SerieIO.read_json(file))
    Serie.from_binary = classmethod(lambda cls, file: SerieIO.read_binary(file))


# Auto-add I/O methods when module is imported
_add_io_to_serie()