"""
Integration of statistical functions with Serie class
Adds statistical methods directly to Serie for convenience
"""

try:
    # Try relative imports first (when used as package)
    from .statistics import SerieStatistics
    from .tests import StatisticalTests
except ImportError:
    # Fallback to absolute imports (when used directly)
    from stats.statistics import SerieStatistics
    from stats.tests import StatisticalTests


def add_stats_methods_to_serie():
    """Add statistical methods to Serie class for convenience"""
    
    # Import Serie class
    try:
        from ..series.serie import Serie
    except ImportError:
        from series.serie import Serie
    
    # Basic statistics
    Serie.variance = lambda self, ddof=1: SerieStatistics.variance(self, ddof)
    Serie.std = lambda self, ddof=1: SerieStatistics.standard_deviation(self, ddof)
    Serie.skewness = lambda self: SerieStatistics.skewness(self)
    Serie.kurtosis = lambda self, excess=True: SerieStatistics.kurtosis(self, excess)
    Serie.median = lambda self: SerieStatistics.median(self)
    Serie.quantile = lambda self, q: SerieStatistics.quantile(self, q)
    Serie.iqr = lambda self: SerieStatistics.interquartile_range(self)
    
    # Correlation functions
    Serie.autocorr = lambda self, max_lags=20: SerieStatistics.autocorrelation(self, max_lags)
    Serie.pacf = lambda self, max_lags=20: SerieStatistics.partial_autocorrelation(self, max_lags)
    Serie.correlate_with = lambda self, other: SerieStatistics.correlation(self, other)
    Serie.covariance_with = lambda self, other, ddof=1: SerieStatistics.covariance(self, other, ddof)
    
    # Statistical tests (as methods)
    Serie.ljung_box_test = lambda self, lags=10: StatisticalTests.box_pierce_ljung_test(self, lags, "ljung-box")
    Serie.box_pierce_test = lambda self, lags=10: StatisticalTests.box_pierce_ljung_test(self, lags, "box-pierce")
    Serie.jarque_bera_test = lambda self: StatisticalTests.jarque_bera_test(self)
    Serie.adf_test = lambda self, regression="c", max_lags=None: StatisticalTests.augmented_dickey_fuller_test(self, regression, max_lags)
    Serie.durbin_watson_test = lambda self: StatisticalTests.durbin_watson_test(self)
    Serie.runs_test = lambda self: StatisticalTests.runs_test(self)
    
    # Class methods for cross-operations
    Serie.cross_correlation = classmethod(lambda cls, serie1, serie2, max_lags=20: 
                                         SerieStatistics.cross_correlation(serie1, serie2, max_lags))


# Auto-add methods when module is imported
add_stats_methods_to_serie()