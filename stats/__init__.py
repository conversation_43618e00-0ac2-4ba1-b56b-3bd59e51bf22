"""Advanced statistical functions for TOL Python time series"""

try:
    # Try relative imports first (when used as package)
    from .statistics import (
        SerieStatistics, 
        mean, variance, std_dev, standard_deviation,
        covariance, correlation, 
        autocorrelation, autocorr_function,
        partial_autocorrelation, partial_autocorr_function,
        cross_correlation,
        skewness, kurtosis,
        quantile, median, interquartile_range
    )
    from .tests import StatisticalTests
    from .test_functions import (
        jarque_bera_test, ljung_box_test, augmented_dickey_fuller_test,
        durbin_watson_test, runs_test
    )
    # Create a tests module namespace
    class tests:
        jarque_bera_test = jarque_bera_test
        ljung_box_test = ljung_box_test
        augmented_dickey_fuller_test = augmented_dickey_fuller_test
        durbin_watson_test = durbin_watson_test
        runs_test = runs_test
    from .integration import add_stats_methods_to_serie
except ImportError:
    # Fallback to absolute imports (when used directly)
    from stats.statistics import (
        SerieStatistics,
        mean, variance, std_dev, standard_deviation,
        covariance, correlation,
        autocorrelation, autocorr_function,
        partial_autocorrelation, partial_autocorr_function,
        cross_correlation,
        skewness, kurtosis,
        quantile, median, interquartile_range
    )
    from stats.tests import StatisticalTests
    from stats.test_functions import (
        jarque_bera_test, ljung_box_test, augmented_dickey_fuller_test,
        durbin_watson_test, runs_test
    )
    # Create a tests module namespace
    class tests:
        jarque_bera_test = jarque_bera_test
        ljung_box_test = ljung_box_test
        augmented_dickey_fuller_test = augmented_dickey_fuller_test
        durbin_watson_test = durbin_watson_test
        runs_test = runs_test
    from stats.integration import add_stats_methods_to_serie

__all__ = [
    'SerieStatistics', 'StatisticalTests', 'tests',
    # Basic statistics
    'mean', 'variance', 'std_dev', 'standard_deviation',
    # Correlation functions
    'covariance', 'correlation',
    'autocorrelation', 'autocorr_function',
    'partial_autocorrelation', 'partial_autocorr_function', 
    'cross_correlation',
    # Distribution statistics
    'skewness', 'kurtosis',
    'quantile', 'median', 'interquartile_range'
]