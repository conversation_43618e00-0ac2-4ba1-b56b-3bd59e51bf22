"""
Statistical tests for time series analysis
Implements TOL's comprehensive testing framework
"""

import numpy as np
import numpy.ma as ma
from typing import Dict, <PERSON><PERSON>, Optional, Union
from scipy import stats
from scipy.stats import chi2
import warnings

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from .statistics import SerieStatistics
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from stats.statistics import SerieStatistics


class StatisticalTests:
    """
    Statistical tests for time series analysis
    Implements key tests from TOL's statistical framework
    """
    
    @staticmethod
    def box_pierce_ljung_test(serie: Serie, lags: int = 10, 
                             test_type: str = "ljung-box") -> Dict[str, float]:
        """
        Box-Pierce and Ljung-Box tests for serial independence
        
        Tests the null hypothesis that residuals are independently distributed
        (no serial correlation up to lag h)
        
        Args:
            serie: Input time series (often residuals from a fitted model)
            lags: Number of lags to include in test
            test_type: "box-pierce" or "ljung-box"
            
        Returns:
            Dictionary with test statistic, p-value, degrees of freedom
        """
        # Get autocorrelations
        acf_serie = SerieStatistics.autocorrelation(serie, lags)
        acf_values = []
        
        for i in range(1, lags + 1):  # Skip lag 0 (which is 1.0)
            acf_val = acf_serie._data[i]  # Access data directly
            if np.isnan(acf_val):
                return {"statistic": np.nan, "p_value": np.nan, "df": lags,
                       "test_type": test_type, "critical_value": np.nan}
            acf_values.append(acf_val)
        
        # Count non-missing observations using direct array access
        data = serie._data.to_numpy()
        valid_data = data[~np.isnan(data)]
        n = len(valid_data)
        
        if n <= lags:
            return {"statistic": np.nan, "p_value": np.nan, "df": lags,
                   "test_type": test_type, "critical_value": np.nan}
        
        # Calculate test statistic
        if test_type.lower() == "box-pierce":
            # Box-Pierce statistic: Q = n * sum(rho_k^2)
            statistic = n * sum(rho**2 for rho in acf_values)
        
        elif test_type.lower() == "ljung-box":
            # Ljung-Box statistic: Q* = n(n+2) * sum(rho_k^2 / (n-k))
            statistic = 0.0
            for k, rho in enumerate(acf_values, 1):
                if n - k > 0:
                    statistic += (rho**2) / (n - k)
            statistic *= n * (n + 2)
        
        else:
            raise ValueError(f"Unknown test type: {test_type}")
        
        # Calculate p-value using chi-squared distribution
        p_value = 1 - chi2.cdf(statistic, df=lags)
        critical_value = chi2.ppf(0.95, df=lags)  # 5% critical value
        
        return {
            "statistic": statistic,
            "p_value": p_value,
            "df": lags,
            "test_type": test_type,
            "critical_value": critical_value,
            "n_observations": n
        }
    
    @staticmethod
    def jarque_bera_test(serie: Serie) -> Dict[str, float]:
        """
        Jarque-Bera test for normality
        
        Tests the null hypothesis that the data is normally distributed
        
        Args:
            serie: Input time series
            
        Returns:
            Dictionary with test statistic, p-value
        """
        # Use direct array access to avoid sentinel date issues
        data = serie._data.to_numpy()
        values = data[~np.isnan(data)]
        
        if len(values) < 8:  # Minimum sample size for reliable test
            return {"statistic": np.nan, "p_value": np.nan, "n_observations": len(values)}
        n = len(values)
        
        # Calculate sample skewness and kurtosis
        skew = stats.skew(values)
        kurt = stats.kurtosis(values, fisher=True)  # Excess kurtosis
        
        # Jarque-Bera statistic: JB = (n/6) * [S^2 + (1/4)*K^2]
        statistic = (n / 6) * (skew**2 + (kurt**2) / 4)
        
        # p-value from chi-squared distribution with 2 degrees of freedom
        p_value = 1 - chi2.cdf(statistic, df=2)
        
        return {
            "statistic": statistic,
            "p_value": p_value,
            "skewness": skew,
            "kurtosis": kurt,
            "n_observations": n,
            "df": 2
        }
    
    @staticmethod
    def augmented_dickey_fuller_test(serie: Serie, regression: str = "c", 
                                   max_lags: Optional[int] = None) -> Dict[str, float]:
        """
        Augmented Dickey-Fuller test for unit roots
        
        Tests the null hypothesis that the series has a unit root (non-stationary)
        
        Args:
            serie: Input time series
            regression: Type of regression ("n", "c", "ct")
                       "n" = no constant, "c" = constant, "ct" = constant + trend
            max_lags: Maximum lags to include (auto-selected if None)
            
        Returns:
            Dictionary with test statistic, p-value, critical values
        """
        # Use direct array access to avoid sentinel date issues
        data = serie._data.to_numpy()
        values = data[~np.isnan(data)]
        
        n = len(values)
        if n < 10:
            return {"statistic": np.nan, "p_value": np.nan, 
                   "critical_values": {}, "n_observations": n}
        
        # Determine number of lags
        if max_lags is None:
            max_lags = int(12 * (n / 100)**(1/4))  # Rule of thumb
        
        max_lags = min(max_lags, n // 3)  # Ensure we have enough observations
        
        # Perform ADF regression: Δy_t = α + βt + γy_{t-1} + Σδ_i Δy_{t-i} + ε_t
        
        # Create difference series and lagged values
        dy = np.diff(values)  # First differences
        y_lag = values[:-1]   # Lagged levels
        
        # Prepare regression matrices
        if max_lags > 0:
            # Add lagged differences
            X_lags = []
            for i in range(1, max_lags + 1):
                if len(dy) > i:
                    lag_diff = dy[:-i] if i > 0 else dy
                    # Pad with NaN to match dimensions
                    padded = np.full(len(dy), np.nan)
                    padded[i:] = lag_diff
                    X_lags.append(padded)
            
            # Find valid observations (no NaN in any series)
            valid_idx = max_lags
            y_reg = dy[valid_idx:]
            y_lag_reg = y_lag[valid_idx:]
            
            # Build design matrix
            X = [y_lag_reg]  # Lagged level
            
            if regression in ["c", "ct"]:
                X.append(np.ones(len(y_reg)))  # Constant
            
            if regression == "ct":
                X.append(np.arange(1, len(y_reg) + 1))  # Time trend
            
            # Add lagged differences
            for lag_diff in X_lags:
                if len(lag_diff) > valid_idx:
                    X.append(lag_diff[valid_idx:])
            
            X = np.column_stack(X)
        else:
            # No lagged differences
            y_reg = dy
            y_lag_reg = y_lag
            
            X = [y_lag_reg]
            if regression in ["c", "ct"]:
                X.append(np.ones(len(y_reg)))
            if regression == "ct":
                X.append(np.arange(1, len(y_reg) + 1))
            
            X = np.column_stack(X)
        
        # OLS regression
        try:
            beta = np.linalg.lstsq(X, y_reg, rcond=None)[0]
            gamma = beta[0]  # Coefficient on lagged level
            
            # Calculate residuals and standard error
            residuals = y_reg - X @ beta
            mse = np.mean(residuals**2)
            
            # Standard error of gamma coefficient
            XtX_inv = np.linalg.inv(X.T @ X)
            se_gamma = np.sqrt(mse * XtX_inv[0, 0])
            
            # ADF statistic (t-statistic for gamma coefficient)
            adf_statistic = gamma / se_gamma
            
            # Approximate critical values (MacKinnon, 1996)
            critical_values = StatisticalTests._adf_critical_values(n, regression)
            
            # Approximate p-value (very rough approximation)
            if adf_statistic < critical_values["1%"]:
                p_value = 0.01
            elif adf_statistic < critical_values["5%"]:
                p_value = 0.05
            elif adf_statistic < critical_values["10%"]:
                p_value = 0.10
            else:
                p_value = 0.20  # Rough approximation
            
            return {
                "statistic": adf_statistic,
                "p_value": p_value,
                "critical_values": critical_values,
                "n_observations": len(y_reg),
                "lags_used": max_lags,
                "regression_type": regression
            }
            
        except np.linalg.LinAlgError:
            return {"statistic": np.nan, "p_value": np.nan,
                   "critical_values": {}, "n_observations": n}
    
    @staticmethod
    def _adf_critical_values(n: int, regression: str) -> Dict[str, float]:
        """
        Approximate critical values for ADF test
        Based on MacKinnon (1996) response surface estimates
        """
        if regression == "n":  # No constant
            cv_1 = -2.66
            cv_5 = -1.95
            cv_10 = -1.60
        elif regression == "c":  # Constant
            cv_1 = -3.75
            cv_5 = -2.99
            cv_10 = -2.63
        elif regression == "ct":  # Constant + trend
            cv_1 = -4.38
            cv_5 = -3.60
            cv_10 = -3.24
        else:
            raise ValueError(f"Unknown regression type: {regression}")
        
        # Finite sample adjustment (very rough)
        if n < 100:
            adjustment = -0.1
            cv_1 += adjustment
            cv_5 += adjustment
            cv_10 += adjustment
        
        return {"1%": cv_1, "5%": cv_5, "10%": cv_10}
    
    @staticmethod
    def durbin_watson_test(residuals: Serie) -> float:
        """
        Durbin-Watson test for first-order serial correlation
        
        Args:
            residuals: Residual series from regression
            
        Returns:
            Durbin-Watson statistic (values near 2 indicate no correlation)
        """
        values = []
        current = residuals.first_date
        while current <= residuals.last_date:
            value = residuals[current]
            if value is not None and not np.isnan(value):
                values.append(value)
            current = residuals.dating.successor(current)
        
        if len(values) < 3:
            return np.nan
        
        values = np.array(values)
        n = len(values)
        
        # DW = sum((e_t - e_{t-1})^2) / sum(e_t^2)
        diff_squared = np.sum(np.diff(values)**2)
        sum_squared = np.sum(values**2)
        
        if sum_squared == 0:
            return np.nan
        
        dw_statistic = diff_squared / sum_squared
        return dw_statistic
    
    @staticmethod
    def runs_test(serie: Serie) -> Dict[str, float]:
        """
        Runs test for randomness (Wald-Wolfowitz test)
        
        Tests the null hypothesis that the sequence is random
        
        Args:
            serie: Input time series
            
        Returns:
            Dictionary with test statistic, p-value
        """
        # Use direct array access to avoid sentinel date issues
        data = serie._data.to_numpy()
        values = data[~np.isnan(data)]
        
        if len(values) < 10:
            return {"statistic": np.nan, "p_value": np.nan, "n_observations": len(values)}
        n = len(values)
        median_val = np.median(values)
        
        # Convert to binary sequence (above/below median)
        binary = (values > median_val).astype(int)
        
        # Count runs
        runs = 1
        for i in range(1, n):
            if binary[i] != binary[i-1]:
                runs += 1
        
        # Count number of values above and below median
        n1 = np.sum(binary)  # Above median
        n2 = n - n1          # Below median
        
        if n1 == 0 or n2 == 0:
            return {"statistic": np.nan, "p_value": np.nan, "n_observations": n}
        
        # Expected number of runs and variance under null hypothesis
        expected_runs = (2 * n1 * n2) / n + 1
        variance_runs = (2 * n1 * n2 * (2 * n1 * n2 - n)) / (n**2 * (n - 1))
        
        if variance_runs <= 0:
            return {"statistic": np.nan, "p_value": np.nan, "n_observations": n}
        
        # Standardized test statistic
        z_statistic = (runs - expected_runs) / np.sqrt(variance_runs)
        
        # Two-tailed p-value
        p_value = 2 * (1 - stats.norm.cdf(abs(z_statistic)))
        
        return {
            "statistic": z_statistic,
            "p_value": p_value,
            "runs_observed": runs,
            "runs_expected": expected_runs,
            "n_observations": n,
            "n_above_median": n1,
            "n_below_median": n2
        }