"""
Convenience functions for statistical tests
These provide simpler tuple returns for common use cases
"""

from typing import Tuple, Optional

try:
    # Try relative imports first (when used as package)
    from .tests import StatisticalTests
    from ..series import Serie
except ImportError:
    # Fallback to absolute imports (when used directly)
    from stats.tests import StatisticalTests
    from series.serie import Serie


def jarque_bera_test(serie: Serie) -> Tuple[float, float]:
    """
    Jarque-Bera test for normality
    
    Returns:
        Tuple of (statistic, p_value)
    """
    result = StatisticalTests.jarque_bera_test(serie)
    return result['statistic'], result['p_value']


def ljung_box_test(serie: Serie, lags: int = 10) -> Tuple[float, float]:
    """
    Ljung-Box test for serial correlation
    
    Returns:
        Tuple of (statistic, p_value)
    """
    result = StatisticalTests.box_pierce_ljung_test(serie, lags=lags, test_type="ljung-box")
    return result['statistic'], result['p_value']


def augmented_dickey_fuller_test(serie: Serie, regression: str = "c") -> Tuple[float, float]:
    """
    Augmented Dickey-Fuller test for unit roots
    
    Returns:
        Tuple of (statistic, p_value)
    """
    result = StatisticalTests.augmented_dickey_fuller_test(serie, regression=regression)
    return result['statistic'], result['p_value']


def durbin_watson_test(serie: Serie) -> float:
    """
    Durbin-Watson test for serial correlation
    
    Returns:
        DW statistic (values near 2 indicate no correlation)
    """
    return StatisticalTests.durbin_watson_test(serie)


def runs_test(serie: Serie) -> Tuple[float, float]:
    """
    Runs test for randomness
    
    Returns:
        Tuple of (statistic, p_value)
    """
    result = StatisticalTests.runs_test(serie)
    return result['statistic'], result['p_value']