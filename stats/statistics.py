"""
Advanced statistical functions for time series
Implements TOL's comprehensive statistical operations
"""

import numpy as np
import numpy.ma as ma
from typing import Union, Optional, Tuple, List
from scipy import stats
import warnings


class ACFResult:
    """Array-like wrapper for ACF/PACF/CCF results that supports indexing and slicing"""
    def __init__(self, data: np.ndarray):
        self.data = data
    
    def __getitem__(self, key):
        return self.data[key]
    
    def __len__(self):
        return len(self.data)
    
    def __iter__(self):
        return iter(self.data)
    
    def __repr__(self):
        return f"ACFResult({self.data})"

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from ..core import Date
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from core.dates import Date


class SerieStatistics:
    """
    Advanced statistical operations for time series
    Mirrors TOL's statistical functions with proper missing value handling
    """
    
    @staticmethod
    def variance(serie: Serie, ddof: int = 1) -> float:
        """
        Calculate sample variance with missing value handling
        
        Args:
            serie: Input time series
            ddof: Delta degrees of freedom (1 for sample variance, 0 for population)
            
        Returns:
            Sample variance ignoring missing values
        """
        # Use direct array access to avoid sentinel date issues
        data = serie._data.to_numpy()
        values = data[~np.isnan(data)]
        
        if len(values) < 2:
            return np.nan
        
        values = np.array(values)
        return np.var(values, ddof=ddof)
    
    @staticmethod
    def standard_deviation(serie: Serie, ddof: int = 1) -> float:
        """Calculate sample standard deviation"""
        return np.sqrt(SerieStatistics.variance(serie, ddof))
    
    @staticmethod
    def covariance(serie1: Serie, serie2: Serie, ddof: int = 1) -> float:
        """
        Calculate sample covariance between two series
        
        Args:
            serie1, serie2: Input time series
            ddof: Delta degrees of freedom
            
        Returns:
            Sample covariance for overlapping periods
        """
        # Find common date range
        first = serie1.first_date if serie1.first_date > serie2.first_date else serie2.first_date
        last = serie1.last_date if serie1.last_date < serie2.last_date else serie2.last_date
        
        if first > last:
            return np.nan
        
        values1, values2 = [], []
        current = first
        while current <= last:
            val1 = serie1[current]
            val2 = serie2[current]
            
            # Only include pairs where both values are non-missing
            if (val1 is not None and not np.isnan(val1) and 
                val2 is not None and not np.isnan(val2)):
                values1.append(val1)
                values2.append(val2)
            
            current = serie1.dating.successor(current)
        
        if len(values1) < 2:
            return np.nan
        
        return np.cov(values1, values2, ddof=ddof)[0, 1]
    
    @staticmethod
    def correlation(serie1: Serie, serie2: Serie) -> float:
        """
        Calculate Pearson correlation coefficient
        
        Returns:
            Correlation coefficient between -1 and 1
        """
        cov = SerieStatistics.covariance(serie1, serie2, ddof=1)
        std1 = SerieStatistics.standard_deviation(serie1)
        std2 = SerieStatistics.standard_deviation(serie2)
        
        if std1 == 0 or std2 == 0 or np.isnan(cov):
            return np.nan
        
        return cov / (std1 * std2)
    
    @staticmethod
    def autocorrelation(serie: Serie, max_lags: int = 20) -> Serie:
        """
        Calculate sample autocorrelation function (ACF)
        
        Args:
            serie: Input time series
            max_lags: Maximum number of lags to compute
            
        Returns:
            Serie containing autocorrelations from lag 0 to max_lags
        """
        # Extract values, handling missing data
        # Use direct array access to avoid sentinel date issues
        data = serie._data.to_numpy()
        values = data.copy()  # Keep NaN values for proper lag alignment
        
        if len(values) < max_lags + 1:
            # Return NaN series if insufficient data
            acf_data = [np.nan] * (max_lags + 1)
        else:
            values = np.array(values)
            acf_data = SerieStatistics._compute_acf(values, max_lags)
        
        # Create result serie with integer indexing for lags
        # We'll use a simple indexing scheme since this is ACF output
        result_serie = Serie()
        result_serie._data.alloc_buffer(len(acf_data))
        for i, value in enumerate(acf_data):
            result_serie._data[i] = value
        result_serie._length = len(acf_data)
        return result_serie
    
    @staticmethod
    def _compute_acf(values: np.ndarray, max_lags: int) -> List[float]:
        """
        Compute autocorrelation function using numpy
        
        Args:
            values: Array of time series values (may contain NaN)
            max_lags: Maximum number of lags
            
        Returns:
            List of autocorrelations [rho(0), rho(1), ..., rho(max_lags)]
        """
        # Remove NaN values for computation
        clean_values = values[~np.isnan(values)]
        n = len(clean_values)
        
        if n < max_lags + 1:
            return [np.nan] * (max_lags + 1)
        
        # Center the data
        mean_val = np.mean(clean_values)
        centered = clean_values - mean_val
        
        # Compute autocorrelations
        acf = []
        variance = np.var(centered, ddof=0)  # Use population variance
        
        for lag in range(max_lags + 1):
            if lag == 0:
                acf.append(1.0)  # rho(0) = 1 by definition
            else:
                if n - lag > 0:
                    covariance = np.mean(centered[:-lag] * centered[lag:])
                    correlation = covariance / variance if variance > 0 else 0
                    acf.append(correlation)
                else:
                    acf.append(np.nan)
        
        return acf
    
    @staticmethod
    def partial_autocorrelation(serie: Serie, max_lags: int = 20) -> Serie:
        """
        Calculate partial autocorrelation function (PACF) using Yule-Walker equations
        
        Args:
            serie: Input time series
            max_lags: Maximum number of lags to compute
            
        Returns:
            Serie containing partial autocorrelations from lag 1 to max_lags
        """
        # First compute the ACF
        acf_serie = SerieStatistics.autocorrelation(serie, max_lags)
        acf_values = [acf_serie._data[i] for i in range(max_lags + 1)]
        
        # Check if ACF computation was successful
        if any(np.isnan(val) for val in acf_values):
            pacf_data = [np.nan] * max_lags
        else:
            pacf_data = SerieStatistics._compute_pacf(acf_values, max_lags)
        
        # Create result serie with simple indexing
        result_serie = Serie()
        result_serie._data.alloc_buffer(len(pacf_data))
        for i, value in enumerate(pacf_data):
            result_serie._data[i] = value
        result_serie._length = len(pacf_data)
        return result_serie
    
    @staticmethod
    def _compute_pacf(acf: List[float], max_lags: int) -> List[float]:
        """
        Compute partial autocorrelation using Durbin algorithm
        
        This implements the Yule-Walker equations via the Durbin algorithm,
        matching TOL's implementation.
        
        Args:
            acf: Autocorrelation function values [rho(0), rho(1), ..., rho(max_lags)]
            max_lags: Maximum number of lags
            
        Returns:
            List of partial autocorrelations [phi(1,1), phi(2,2), ..., phi(max_lags,max_lags)]
        """
        pacf = []
        phi = np.zeros((max_lags + 1, max_lags + 1))
        
        for k in range(1, max_lags + 1):
            if k == 1:
                # First-order case: phi(1,1) = rho(1)
                phi[1, 1] = acf[1]
                pacf.append(phi[1, 1])
            else:
                # Higher-order case: Durbin algorithm
                numerator = acf[k]
                for j in range(1, k):
                    numerator -= phi[k-1, j] * acf[k-j]
                
                denominator = 1.0
                for j in range(1, k):
                    denominator -= phi[k-1, j] * acf[j]
                
                if abs(denominator) < 1e-10:
                    phi[k, k] = 0.0
                else:
                    phi[k, k] = numerator / denominator
                
                # Update other coefficients
                for j in range(1, k):
                    phi[k, j] = phi[k-1, j] - phi[k, k] * phi[k-1, k-j]
                
                pacf.append(phi[k, k])
        
        return pacf
    
    @staticmethod
    def cross_correlation(serie1: Serie, serie2: Serie, max_lags: int = 20) -> Serie:
        """
        Calculate cross-correlation function between two series
        
        Args:
            serie1, serie2: Input time series
            max_lags: Maximum number of lags (both positive and negative)
            
        Returns:
            Serie with cross-correlations from -max_lags to +max_lags
        """
        # Find common period and extract values
        first = serie1.first_date if serie1.first_date > serie2.first_date else serie2.first_date
        last = serie1.last_date if serie1.last_date < serie2.last_date else serie2.last_date
        
        if first > last:
            ccf_data = [np.nan] * (2 * max_lags + 1)
        else:
            values1, values2 = [], []
            current = first
            while current <= last:
                val1 = serie1[current]
                val2 = serie2[current]
                values1.append(val1 if val1 is not None else np.nan)
                values2.append(val2 if val2 is not None else np.nan)
                current = serie1.dating.successor(current)
            
            ccf_data = SerieStatistics._compute_ccf(np.array(values1), np.array(values2), max_lags)
        
        # Create result serie with simple indexing
        result_serie = Serie()
        result_serie._data.alloc_buffer(len(ccf_data))
        for i, value in enumerate(ccf_data):
            result_serie._data[i] = value
        result_serie._length = len(ccf_data)
        return result_serie
    
    @staticmethod
    def _compute_ccf(values1: np.ndarray, values2: np.ndarray, max_lags: int) -> List[float]:
        """
        Compute cross-correlation function
        
        Args:
            values1, values2: Arrays of time series values
            max_lags: Maximum lag
            
        Returns:
            Cross-correlations from lag -max_lags to +max_lags
        """
        n = len(values1)
        if n != len(values2) or n < max_lags + 1:
            return [np.nan] * (2 * max_lags + 1)
        
        # Remove periods where either series has missing values
        valid_mask = ~(np.isnan(values1) | np.isnan(values2))
        if np.sum(valid_mask) < max_lags + 1:
            return [np.nan] * (2 * max_lags + 1)
        
        clean_values1 = values1[valid_mask]
        clean_values2 = values2[valid_mask]
        
        # Standardize the series
        mean1, std1 = np.mean(clean_values1), np.std(clean_values1, ddof=1)
        mean2, std2 = np.mean(clean_values2), np.std(clean_values2, ddof=1)
        
        if std1 == 0 or std2 == 0:
            return [np.nan] * (2 * max_lags + 1)
        
        std1_values = (clean_values1 - mean1) / std1
        std2_values = (clean_values2 - mean2) / std2
        
        ccf = []
        for lag in range(-max_lags, max_lags + 1):
            if lag < 0:
                # Negative lag: correlate x2(t) with x1(t-|lag|)
                lag_abs = abs(lag)
                if len(std1_values) > lag_abs:
                    corr = np.corrcoef(std2_values[:-lag_abs], std1_values[lag_abs:])[0, 1]
                else:
                    corr = np.nan
            elif lag == 0:
                # Zero lag: standard correlation
                corr = np.corrcoef(std1_values, std2_values)[0, 1]
            else:
                # Positive lag: correlate x1(t) with x2(t-lag)
                if len(std2_values) > lag:
                    corr = np.corrcoef(std1_values[:-lag], std2_values[lag:])[0, 1]
                else:
                    corr = np.nan
            
            ccf.append(corr if not np.isnan(corr) else 0.0)
        
        return ccf
    
    @staticmethod
    def skewness(serie: Serie) -> float:
        """Calculate sample skewness"""
        # Use direct array access to avoid sentinel date issues
        data = serie._data.to_numpy()
        values = data[~np.isnan(data)]
        
        if len(values) < 3:
            return np.nan
        
        return stats.skew(values)
    
    @staticmethod
    def kurtosis(serie: Serie, excess: bool = True) -> float:
        """
        Calculate sample kurtosis
        
        Args:
            serie: Input time series
            excess: If True, return excess kurtosis (subtract 3)
            
        Returns:
            Sample kurtosis
        """
        # Use direct array access to avoid sentinel date issues
        data = serie._data.to_numpy()
        values = data[~np.isnan(data)]
        
        if len(values) < 4:
            return np.nan
        
        return stats.kurtosis(values, fisher=excess)
    
    @staticmethod
    def quantile(serie: Serie, q: Union[float, List[float]]) -> Union[float, List[float]]:
        """
        Calculate quantiles
        
        Args:
            serie: Input time series
            q: Quantile or sequence of quantiles (0 <= q <= 1)
            
        Returns:
            Quantile value(s)
        """
        # Use direct array access to avoid sentinel date issues
        data = serie._data.to_numpy()
        values = data[~np.isnan(data)]
        
        if len(values) == 0:
            return np.nan if np.isscalar(q) else [np.nan] * len(q)
        
        return np.quantile(values, q)
    
    @staticmethod
    def median(serie: Serie) -> float:
        """Calculate median"""
        return SerieStatistics.quantile(serie, 0.5)
    
    @staticmethod
    def interquartile_range(serie: Serie) -> float:
        """Calculate interquartile range (Q3 - Q1)"""
        q1, q3 = SerieStatistics.quantile(serie, [0.25, 0.75])
        return q3 - q1 if not (np.isnan(q1) or np.isnan(q3)) else np.nan


# Convenience functions for easier usage
def mean(serie: Serie) -> float:
    """Calculate mean of a Serie"""
    # Use direct array access to avoid sentinel date issues
    data = serie._data.to_numpy()
    values = data[~np.isnan(data)]
    return np.mean(values) if len(values) > 0 else np.nan

def variance(serie: Serie, ddof: int = 1) -> float:
    """Calculate variance of a Serie"""  
    return SerieStatistics.variance(serie, ddof)

def std_dev(serie: Serie, ddof: int = 1) -> float:
    """Calculate standard deviation of a Serie"""
    return SerieStatistics.standard_deviation(serie, ddof)

def standard_deviation(serie: Serie, ddof: int = 1) -> float:
    """Calculate standard deviation of a Serie (alias)"""
    return SerieStatistics.standard_deviation(serie, ddof)

def covariance(serie1: Serie, serie2: Serie, ddof: int = 1) -> float:
    """Calculate covariance between two series"""
    return SerieStatistics.covariance(serie1, serie2, ddof)

def correlation(serie1: Serie, serie2: Serie) -> float:
    """Calculate correlation between two series"""
    return SerieStatistics.correlation(serie1, serie2)

def autocorrelation(serie: Serie, max_lags: int = 20, max_lag: int = None) -> Serie:
    """Calculate autocorrelation function"""
    if max_lag is not None:
        max_lags = max_lag
    return SerieStatistics.autocorrelation(serie, max_lags)

def autocorr_function(serie: Serie, max_lags: int = 20, max_lag: int = None):
    """Calculate autocorrelation function (alias) - returns array-like object"""
    if max_lag is not None:
        max_lags = max_lag
    acf_serie = SerieStatistics.autocorrelation(serie, max_lags)
    # Return a simple object that supports indexing and slicing
    return ACFResult(acf_serie._data.to_numpy())

def partial_autocorrelation(serie: Serie, max_lags: int = 20, max_lag: int = None) -> Serie:
    """Calculate partial autocorrelation function"""
    if max_lag is not None:
        max_lags = max_lag
    return SerieStatistics.partial_autocorrelation(serie, max_lags)

def partial_autocorr_function(serie: Serie, max_lags: int = 20, max_lag: int = None):
    """Calculate partial autocorrelation function (alias) - returns array-like object"""
    if max_lag is not None:
        max_lags = max_lag
    pacf_serie = SerieStatistics.partial_autocorrelation(serie, max_lags)
    return ACFResult(pacf_serie._data.to_numpy())

def cross_correlation(serie1: Serie, serie2: Serie, max_lags: int = 20, max_lag: int = None):
    """Calculate cross-correlation between two series - returns array-like object"""
    if max_lag is not None:
        max_lags = max_lag
    ccf_serie = SerieStatistics.cross_correlation(serie1, serie2, max_lags)
    return ACFResult(ccf_serie._data.to_numpy())

def skewness(serie: Serie) -> float:
    """Calculate skewness of a Serie"""
    return SerieStatistics.skewness(serie)

def kurtosis(serie: Serie, excess: bool = True) -> float:
    """Calculate kurtosis of a Serie"""
    return SerieStatistics.kurtosis(serie, excess)

def quantile(serie: Serie, q: Union[float, List[float]]) -> Union[float, List[float]]:
    """Calculate quantiles of a Serie"""
    return SerieStatistics.quantile(serie, q)

def median(serie: Serie) -> float:
    """Calculate median of a Serie"""
    return SerieStatistics.median(serie)

def interquartile_range(serie: Serie) -> float:
    """Calculate interquartile range of a Serie"""
    return SerieStatistics.interquartile_range(serie)