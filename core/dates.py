"""
Date and time handling for TOL Python implementation
Mirrors the BDate and BUserTimeSet functionality from TOL
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from enum import Enum
from typing import Optional, Union, List, Dict, Tuple, TYPE_CHECKING
import numpy as np

if TYPE_CHECKING:
    from ..series import Serie


class DatingType(Enum):
    """Dating type for time series"""
    FIXED = "DATING_FIXED"
    VOLATILE = "DATING_VOLATILE"


class Date:
    """
    TOL-compatible Date class
    Represents dates with TOL's specific semantics including Begin/End sentinels
    """
    
    _BEGIN_SENTINEL = datetime.min
    _END_SENTINEL = datetime.max
    
    def __init__(self, value: Union[datetime, str, None] = None):
        if value is None:
            self._value = datetime.now()
        elif isinstance(value, str):
            # Parse TOL date format (e.g., "y2000", "y2000m03d15")
            self._value = self._parse_tol_date(value)
        elif isinstance(value, datetime):
            self._value = value
        else:
            raise TypeError(f"Cannot create Date from {type(value)}")
    
    @classmethod
    def begin(cls) -> 'Date':
        """Create a Begin sentinel date"""
        date = cls.__new__(cls)
        date._value = cls._BEGIN_SENTINEL
        return date
    
    @classmethod
    def end(cls) -> 'Date':
        """Create an End sentinel date"""
        date = cls.__new__(cls)
        date._value = cls._END_SENTINEL
        return date
    
    @classmethod
    def range(cls, start_date: 'Date', end_date: 'Date', time_set: 'TimeSet') -> List['Date']:
        """Create a range of dates from start to end using the given TimeSet"""
        if not (start_date.is_normal() and end_date.is_normal()):
            raise ValueError("Cannot create range with sentinel dates")
        
        if start_date > end_date:
            raise ValueError("Start date must be before or equal to end date")
        
        dates = []
        current = start_date
        while current <= end_date:
            dates.append(current)
            current = time_set.successor(current)
        
        return dates
    
    def _parse_tol_date(self, date_str: str) -> datetime:
        """Parse TOL date format: y2000, y2000m03d15, etc."""
        if not date_str.startswith('y'):
            raise ValueError(f"Invalid TOL date format: {date_str}")
        
        parts = date_str[1:].replace('m', '-').replace('d', '-').split('-')
        year = int(parts[0])
        month = int(parts[1]) if len(parts) > 1 else 1
        day = int(parts[2]) if len(parts) > 2 else 1
        
        return datetime(year, month, day)
    
    def is_begin(self) -> bool:
        return self._value == self._BEGIN_SENTINEL
    
    def is_end(self) -> bool:
        return self._value == self._END_SENTINEL
    
    def is_normal(self) -> bool:
        """Check if date is neither Begin nor End"""
        return not (self.is_begin() or self.is_end())
    
    def __str__(self) -> str:
        if self.is_begin():
            return "Date::Begin"
        elif self.is_end():
            return "Date::End"
        else:
            return f"y{self._value.year}m{self._value.month:02d}d{self._value.day:02d}"
    
    def __repr__(self) -> str:
        return f"Date('{str(self)}')"
    
    def __eq__(self, other: 'Date') -> bool:
        return self._value == other._value
    
    def __hash__(self) -> int:
        """Make Date objects hashable for use in dictionaries and sets"""
        return hash(self._value)
    
    def __lt__(self, other: 'Date') -> bool:
        return self._value < other._value
    
    def __le__(self, other: 'Date') -> bool:
        return self._value <= other._value
    
    def __gt__(self, other: 'Date') -> bool:
        return self._value > other._value
    
    def __ge__(self, other: 'Date') -> bool:
        return self._value >= other._value
    
    def successor(self, time_set: Optional['TimeSet'] = None) -> 'Date':
        """Get the next date according to the given time set (default: daily)"""
        if not self.is_normal():
            return self
        
        if time_set is None:
            # Default to daily frequency
            time_set = TimeSet("daily")
        
        return time_set.successor(self)
    
    def predecessor(self, time_set: Optional['TimeSet'] = None) -> 'Date':
        """Get the previous date according to the given time set (default: daily)"""
        if not self.is_normal():
            return self
        
        if time_set is None:
            # Default to daily frequency
            time_set = TimeSet("daily")
        
        return time_set.predecessor(self)
    
    def add_days(self, days: int) -> 'Date':
        """Add or subtract days from the date"""
        if not self.is_normal():
            return self
        
        new_dt = self._value + timedelta(days=days)
        new_date = Date.__new__(Date)
        new_date._value = new_dt
        return new_date
    
    def add_months(self, months: int) -> 'Date':
        """Add or subtract months from the date"""
        if not self.is_normal():
            return self
        
        year = self._value.year
        month = self._value.month + months
        
        # Handle year overflow/underflow
        while month > 12:
            month -= 12
            year += 1
        while month < 1:
            month += 12
            year -= 1
        
        # Handle day overflow (e.g., Jan 31 + 1 month = Feb 28/29)
        day = min(self._value.day, self._days_in_month(year, month))
        
        new_dt = datetime(year, month, day)
        new_date = Date.__new__(Date)
        new_date._value = new_dt
        return new_date
    
    def _days_in_month(self, year: int, month: int) -> int:
        """Get the number of days in a given month"""
        if month in [1, 3, 5, 7, 8, 10, 12]:
            return 31
        elif month in [4, 6, 9, 11]:
            return 30
        elif month == 2:
            # Handle leap years
            if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                return 29
            else:
                return 28
        else:
            raise ValueError(f"Invalid month: {month}")
    
    def to_datetime(self) -> datetime:
        """Convert to Python datetime"""
        if not self.is_normal():
            raise ValueError("Cannot convert sentinel dates to datetime")
        return self._value


class TimeSetBase(ABC):
    """Abstract base class matching BUserTimeSet structure from TOL C++"""
    
    def __init__(self):
        # Caching system like C++ BUserTimeSet
        self._instants_cache: Optional[List[Date]] = None
        self._hash_cache: Dict[Tuple[Date, Date], List[Date]] = {}
        self._is_evaluated: bool = False
        self._granularity: Optional[str] = None
        
    @abstractmethod
    def _contains_impl(self, date: Date) -> bool:
        """Core containment logic - implement per subclass"""
        pass
    
    def includes(self, date: Date) -> bool:
        """Optimized includes with caching"""
        if not date.is_normal():
            return False
        return self._contains_impl(date)
    
    def get_instants_between(self, start: Date, end: Date) -> List[Date]:
        """Efficient range-based instant retrieval (like C++)"""
        cache_key = (start, end)
        if cache_key in self._hash_cache:
            return self._hash_cache[cache_key]
        
        # Compute instants in range
        instants = []
        current = self.successor(start.predecessor())
        while current.is_normal() and current <= end:
            if self.includes(current):
                instants.append(current)
            current = self.successor(current)
        
        self._hash_cache[cache_key] = instants
        return instants
    
    @abstractmethod
    def successor(self, date: Date) -> Date:
        """Get next date in this TimeSet after given date"""
        pass
    
    @abstractmethod
    def predecessor(self, date: Date) -> Date:
        """Get previous date in this TimeSet before given date"""
        pass
    
    def distance(self, from_date: Date, to_date: Date) -> int:
        """Calculate distance between dates in this TimeSet units"""
        if not (from_date.is_normal() and to_date.is_normal()):
            return 0
        
        count = 0
        current = from_date
        if from_date <= to_date:
            while current < to_date:
                current = self.successor(current)
                if current.is_normal():
                    count += 1
                else:
                    break
        else:
            while current > to_date:
                current = self.predecessor(current)
                if current.is_normal():
                    count += 1
                else:
                    break
            count = -count
        
        return count
    
    # Set operations
    def __add__(self, other: 'TimeSetBase') -> 'TimeSetBase':
        """Union operation (+)"""
        return TimeSetUnion(self, other)
    
    def __sub__(self, other: 'TimeSetBase') -> 'TimeSetBase':
        """Difference operation (-)"""
        return TimeSetDifference(self, other)
    
    def __mul__(self, other: 'TimeSetBase') -> 'TimeSetBase':
        """Intersection operation (*)"""
        return TimeSetIntersection(self, other)


class TimeSet(TimeSetBase):
    """
    Base class for time sets (dating patterns)
    Equivalent to BUserTimeSet in TOL
    """
    
    def __init__(self, frequency: str = "daily"):
        self.frequency = frequency
        self._freq_map = {
            "daily": timedelta(days=1),
            "weekly": timedelta(days=7),
            "monthly": "M",  # Special handling needed
            "quarterly": "Q",  # Special handling needed
            "yearly": "Y"     # Special handling needed
        }
    
    def successor(self, date: Date) -> Date:
        """Get the next date in the time set"""
        if not date.is_normal():
            return date
        
        if self.frequency == "daily":
            new_dt = date._value + timedelta(days=1)
        elif self.frequency == "weekly":
            new_dt = date._value + timedelta(days=7)
        elif self.frequency == "monthly":
            # Add one month - use Date's add_months method to handle day overflow
            return date.add_months(1)
        elif self.frequency == "quarterly":
            # Add three months - use Date's add_months method
            return date.add_months(3)
        elif self.frequency == "yearly":
            new_dt = datetime(date._value.year + 1, date._value.month, date._value.day)
        else:
            raise ValueError(f"Unknown frequency: {self.frequency}")
        
        new_date = Date.__new__(Date)
        new_date._value = new_dt
        return new_date
    
    def predecessor(self, date: Date) -> Date:
        """Get the previous date in the time set"""
        if not date.is_normal():
            return date
        
        if self.frequency == "daily":
            new_dt = date._value - timedelta(days=1)
        elif self.frequency == "weekly":
            new_dt = date._value - timedelta(days=7)
        elif self.frequency == "monthly":
            # Subtract one month - use Date's add_months method
            return date.add_months(-1)
        elif self.frequency == "quarterly":
            # Subtract three months - use Date's add_months method
            return date.add_months(-3)
        elif self.frequency == "yearly":
            new_dt = datetime(date._value.year - 1, date._value.month, date._value.day)
        else:
            raise ValueError(f"Unknown frequency: {self.frequency}")
        
        new_date = Date.__new__(Date)
        new_date._value = new_dt
        return new_date
    
    def distance(self, date1: Date, date2: Date) -> int:
        """Calculate the number of periods between two dates"""
        if not (date1.is_normal() and date2.is_normal()):
            raise ValueError("Cannot calculate distance with sentinel dates")
        
        if self.frequency == "daily":
            return (date2._value - date1._value).days
        elif self.frequency == "weekly":
            return (date2._value - date1._value).days // 7
        elif self.frequency == "monthly":
            return ((date2._value.year - date1._value.year) * 12 + 
                    (date2._value.month - date1._value.month))
        elif self.frequency == "quarterly":
            months = ((date2._value.year - date1._value.year) * 12 + 
                     (date2._value.month - date1._value.month))
            return months // 3
        elif self.frequency == "yearly":
            return date2._value.year - date1._value.year
        else:
            raise ValueError(f"Unknown frequency: {self.frequency}")
    
    def non_smaller(self, date: Date) -> Date:
        """Find the smallest date in the time set >= given date"""
        # For now, simple implementation - can be optimized
        return date
    
    def non_greater(self, date: Date) -> Date:
        """Find the largest date in the time set <= given date"""
        # For now, simple implementation - can be optimized
        return date
    
    @classmethod
    def monthly(cls, start_date: Date, periods: int) -> 'TimeSet':
        """Create a monthly TimeSet"""
        return cls("monthly")
    
    @classmethod 
    def daily(cls, start_date: Date, periods: int) -> 'TimeSet':
        """Create a daily TimeSet"""
        return cls("daily")
    
    @classmethod
    def weekly(cls, start_date: Date, periods: int) -> 'TimeSet':
        """Create a weekly TimeSet"""
        return cls("weekly")
    
    @classmethod
    def quarterly(cls, start_date: Date, periods: int) -> 'TimeSet':
        """Create a quarterly TimeSet"""
        return cls("quarterly")
    
    @classmethod
    def yearly(cls, start_date: Date, periods: int) -> 'TimeSet':
        """Create a yearly TimeSet"""
        return cls("yearly")
    
    def __len__(self) -> int:
        """Return the number of periods (for compatibility)"""
        return 0  # Base implementation
    
    # Set Operations (TOL-compatible)
    
    def __add__(self, other: 'TimeSet') -> 'TimeSet':
        """
        Union operation: returns a TimeSet containing dates in either self or other
        Equivalent to TOL's ctms1 + ctms2
        """
        if not isinstance(other, TimeSetBase):
            raise TypeError(f"Cannot add TimeSet with {type(other)}")
        return TimeSetUnion(self, other)
    
    def __sub__(self, other: 'TimeSet') -> 'TimeSet':
        """
        Difference operation: returns a TimeSet containing dates in self but not in other
        Equivalent to TOL's ctms1 - ctms2
        """
        if not isinstance(other, TimeSetBase):
            raise TypeError(f"Cannot subtract {type(other)} from TimeSet")
        return TimeSetDifference(self, other)
    
    def __mul__(self, other: 'TimeSet') -> 'TimeSet':
        """
        Intersection operation: returns a TimeSet containing dates in both self and other
        Equivalent to TOL's ctms1 * ctms2
        """
        if not isinstance(other, TimeSetBase):
            raise TypeError(f"Cannot multiply TimeSet with {type(other)}")
        return TimeSetIntersection(self, other)
    
    def _contains_impl(self, date: Date) -> bool:
        """
        Base implementation always returns True for regular timeSets
        """
        return True  # Base TimeSet includes all dates
    
    def union(self, other: 'TimeSet') -> 'TimeSet':
        """Union with another TimeSet (same as + operator)"""
        return self + other
    
    def intersection(self, other: 'TimeSet') -> 'TimeSet':
        """Intersection with another TimeSet (same as * operator)"""
        return self * other
    
    def difference(self, other: 'TimeSet') -> 'TimeSet':
        """Difference with another TimeSet (same as - operator)"""
        return self - other
    
    def to_dates_list(self, start: Date, max_dates: int = 100) -> List[Date]:
        """
        Convert any TimeSet to a Python list of dates
        Useful for getting concrete dates from complex TimeSet expressions
        
        Args:
            start: Starting date
            max_dates: Maximum number of dates to return (safety limit)
            
        Returns:
            List of Date objects
        """
        dates = []
        current = start
        
        for _ in range(max_dates):
            if not current.is_normal():
                break
                
            if self.includes(current):
                dates.append(current)
                
            # Find next date in this TimeSet
            next_date = self.successor(current)
            if not next_date.is_normal() or next_date._value <= current._value:
                break
                
            current = next_date
            
        return dates
    
    # TOL-compatible TimeSet functions
    
    def dates_of_set(self, start: Date, n: int) -> 'TimeSet':
        """
        Get n dates from the TimeSet starting at the given date
        Returns a TimeSet containing exactly those dates
        Equivalent to TOL's DatesOfSet function
        """
        dates = []
        current = start
        for _ in range(n):
            if current.is_normal() and self.includes(current):
                dates.append(current)
                current = self.successor(current)
            else:
                break
        return SpecificDatesTimeSet(dates)
    
    @staticmethod
    def day(days: Union[int, List[int]]) -> 'TimeSet':
        """
        Create a TimeSet for specific days of the month
        Equivalent to TOL's Day function
        """
        return DayTimeSet(days)
    
    @staticmethod
    def h(hours: Union[int, List[int]]) -> 'TimeSet':
        """
        Create a TimeSet for specific hours
        Equivalent to TOL's H function
        """
        return HourTimeSet(hours)
    
    @staticmethod  
    def m(months: Union[int, List[int]]) -> 'TimeSet':
        """
        Create a TimeSet for specific months
        Equivalent to TOL's M function
        """
        return MonthTimeSet(months)
    
    @staticmethod
    def mi(minutes: Union[int, List[int]]) -> 'TimeSet':
        """
        Create a TimeSet for specific minutes
        Equivalent to TOL's Mi function
        """
        return MinuteTimeSet(minutes)
    
    @staticmethod
    def s(seconds: Union[int, List[int]]) -> 'TimeSet':
        """
        Create a TimeSet for specific seconds
        Equivalent to TOL's S function
        """
        return SecondTimeSet(seconds)
    
    @staticmethod
    def periodic(start_date: Date, period: int, units: str = "days") -> 'TimeSet':
        """
        Create a periodic TimeSet (every n units)
        Equivalent to TOL's Periodic function
        """
        return PeriodicTimeSet(start_date, period, units)
    
    @staticmethod
    def range(start: Date, end: Date, step: Optional['TimeSet'] = None) -> 'TimeSet':
        """
        Create a TimeSet for a date range
        Equivalent to TOL's Range function
        """
        if step is None:
            step = TimeSet("daily")
        return RangeTimeSet(start, end, step)
    
    @staticmethod
    def ser_tms(serie: 'Serie') -> 'TimeSet':
        """
        Create a TimeSet from a Serie's dates
        Equivalent to TOL's SerTms function
        """
        return SerieTimeSet(serie)
    
    @staticmethod
    def wd(weekdays: Union[int, List[int]]) -> 'TimeSet':
        """
        Create a TimeSet for specific weekdays (0=Monday, 6=Sunday)
        Equivalent to TOL's WD function
        """
        return WeekDayTimeSet(weekdays)
    
    @staticmethod
    def y(years: Union[int, List[int]]) -> 'TimeSet':
        """
        Create a TimeSet for specific years
        Equivalent to TOL's Y function
        """
        return YearTimeSet(years)
    
    @staticmethod
    def next_date(timeset: 'TimeSet', date: Date) -> Date:
        """
        Static method to get the next date in a TimeSet
        
        Args:
            timeset: The TimeSet to use for navigation
            date: The current date
            
        Returns:
            Next date in the TimeSet
        """
        return timeset.successor(date)
    
    @staticmethod 
    def prev_date(timeset: 'TimeSet', date: Date) -> Date:
        """
        Static method to get the previous date in a TimeSet
        
        Args:
            timeset: The TimeSet to use for navigation
            date: The current date
            
        Returns:
            Previous date in the TimeSet
        """
        return timeset.predecessor(date)
    
    @staticmethod
    def last_day_of_month() -> 'TimeSet':
        """
        Create a TimeSet for the last day of each month
        More precise than day([28, 29, 30, 31])
        
        Returns:
            TimeSet containing exactly the last day of each month
        """
        return LastDayOfMonthTimeSet()
    
    @staticmethod
    def successor_tol(center: 'TimeSet', displacement: Union[int, float], 
                      units: Optional['TimeSet'] = None) -> 'TimeSet':
        """
        TOL-compatible Successor function
        
        Returns the TimeSet center, moved displacement dates along the given 
        unit's dating, toward the future if displacement is positive, or toward 
        the past if displacement is negative. If displacement is zero returns center itself.
        
        Args:
            center: The base TimeSet to shift
            displacement: Number of periods to move (positive = future, negative = past)
            units: Optional TimeSet for units (defaults to daily)
            
        Returns:
            New TimeSet shifted by the specified displacement
        """
        if units is None:
            units = TimeSet("daily")
        
        displacement = int(displacement)  # Convert to integer
        
        if displacement == 0:
            # If no displacement, return intersection of center and units
            return center * units
        
        return TimeSetSuccessor(center, displacement, units)
    
    # Shorter alias for convenience
    succ = successor_tol


class TimeSetBinary(TimeSet):
    """
    Base class for binary TimeSet operations
    Mirrors TOL's BCTmsBinary structure
    """
    
    def __init__(self, left: TimeSet, right: TimeSet):
        """Initialize with two TimeSet operands"""
        super().__init__()
        self.left = left
        self.right = right
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}({self.left}, {self.right})"


class TimeSetUnion(TimeSetBase):
    """
    Union of two TimeSets (A + B)
    A date is included if it's in either TimeSet A or TimeSet B
    Equivalent to TOL's BCTmsUnion
    """
    
    def __init__(self, left: TimeSetBase, right: TimeSetBase):
        super().__init__()
        self.left = left
        self.right = right
    
    def _contains_impl(self, date: Date) -> bool:
        """Test if date is in either time set"""
        return self.left.includes(date) or self.right.includes(date)
    
    def successor(self, date: Date) -> Date:
        """
        Find the next date in the union
        Returns the closer of the two successors
        """
        if not date.is_normal():
            return date
        
        # Get successors from both sets
        left_succ = self.left.successor(date)
        right_succ = self.right.successor(date)
        
        # Handle edge cases
        if not left_succ.is_normal():
            return right_succ
        if not right_succ.is_normal():
            return left_succ
        
        # Return the earlier successor
        return left_succ if left_succ._value < right_succ._value else right_succ
    
    def predecessor(self, date: Date) -> Date:
        """
        Find the previous date in the union
        Returns the closer of the two predecessors
        """
        if not date.is_normal():
            return date
        
        # Get predecessors from both sets
        left_pred = self.left.predecessor(date)
        right_pred = self.right.predecessor(date)
        
        # Handle edge cases
        if not left_pred.is_normal():
            return right_pred
        if not right_pred.is_normal():
            return left_pred
        
        # Return the later predecessor
        return left_pred if left_pred._value > right_pred._value else right_pred
    
    def __str__(self) -> str:
        return f"({self.left} + {self.right})"


class TimeSetIntersection(TimeSetBase):
    """
    Intersection of two TimeSets (A * B)
    A date is included only if it's in both TimeSet A and TimeSet B
    Equivalent to TOL's BCTmsIntersection
    """
    
    def __init__(self, left: TimeSetBase, right: TimeSetBase):
        super().__init__()
        self.left = left
        self.right = right
    
    def _contains_impl(self, date: Date) -> bool:
        """Test if date is in both time sets"""
        return self.left.includes(date) and self.right.includes(date)
    
    def successor(self, date: Date) -> Date:
        """
        Find the next date in the intersection
        Must be in both sets
        """
        if not date.is_normal():
            return date
        
        current = date
        max_iterations = 10000  # Safety limit
        
        for _ in range(max_iterations):
            # Try left successor
            current = self.left.successor(current)
            if not current.is_normal():
                return current  # No more dates
            
            # Check if it's also in the right set
            if self.right.includes(current):
                return current
        
        # If we hit the iteration limit, return sentinel
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """
        Find the previous date in the intersection
        Must be in both sets
        """
        if not date.is_normal():
            return date
        
        current = date
        max_iterations = 10000  # Safety limit
        
        for _ in range(max_iterations):
            # Try left predecessor
            current = self.left.predecessor(current)
            if not current.is_normal():
                return current  # No more dates
            
            # Check if it's also in the right set
            if self.right.includes(current):
                return current
        
        # If we hit the iteration limit, return sentinel
        return Date.begin()
    
    def __str__(self) -> str:
        return f"({self.left} * {self.right})"


class TimeSetDifference(TimeSetBase):
    """
    Difference of two TimeSets (A - B)
    A date is included if it's in TimeSet A but NOT in TimeSet B
    Equivalent to TOL's BCTmsDifference
    """
    
    def __init__(self, left: TimeSetBase, right: TimeSetBase):
        super().__init__()
        self.left = left
        self.right = right
    
    def _contains_impl(self, date: Date) -> bool:
        """Test if date is in left but not in right"""
        return self.left.includes(date) and not self.right.includes(date)
    
    def successor(self, date: Date) -> Date:
        """
        Find the next date in the difference
        Must be in left but not in right
        """
        if not date.is_normal():
            return date
        
        current = date
        max_iterations = 10000  # Safety limit
        
        for _ in range(max_iterations):
            # Try left successor
            current = self.left.successor(current)
            if not current.is_normal():
                return current  # No more dates
            
            # Check if it's NOT in the right set
            if not self.right.includes(current):
                return current
        
        # If we hit the iteration limit, return sentinel
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """
        Find the previous date in the difference
        Must be in left but not in right
        """
        if not date.is_normal():
            return date
        
        current = date
        max_iterations = 10000  # Safety limit
        
        for _ in range(max_iterations):
            # Try left predecessor
            current = self.left.predecessor(current)
            if not current.is_normal():
                return current  # No more dates
            
            # Check if it's NOT in the right set
            if not self.right.includes(current):
                return current
        
        # If we hit the iteration limit, return sentinel
        return Date.begin()
    
    def __str__(self) -> str:
        return f"({self.left} - {self.right})"


class DayTimeSet(TimeSetBase):
    """
    TimeSet for specific days of the month
    Equivalent to TOL's Day function
    """
    
    def __init__(self, days: Union[int, List[int]]):
        super().__init__()
        self.days = [days] if isinstance(days, int) else list(days)
        # Validate days are in range 1-31
        for day in self.days:
            if day < 1 or day > 31:
                raise ValueError(f"Day {day} out of range (1-31)")
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date falls on one of the specified days"""
        return date._value.day in self.days
    
    def successor(self, date: Date) -> Date:
        """Find next date matching the day constraint"""
        if not date.is_normal():
            return date
        
        current = date
        for _ in range(32):  # At most need to check one month ahead
            current = TimeSet("daily").successor(current)
            if not current.is_normal():
                return current
            if current._value.day in self.days:
                return current
        
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """Find previous date matching the day constraint"""
        if not date.is_normal():
            return date
        
        current = date
        for _ in range(32):  # At most need to check one month back
            current = TimeSet("daily").predecessor(current)
            if not current.is_normal():
                return current
            if current._value.day in self.days:
                return current
        
        return Date.begin()
    
    def __str__(self) -> str:
        return f"Day({self.days})"


class MonthTimeSet(TimeSetBase):
    """
    TimeSet for specific months
    Equivalent to TOL's M function
    """
    
    def __init__(self, months: Union[int, List[int]]):
        super().__init__()
        self.months = [months] if isinstance(months, int) else list(months)
        # Validate months are in range 1-12
        for month in self.months:
            if month < 1 or month > 12:
                raise ValueError(f"Month {month} out of range (1-12)")
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date falls in one of the specified months"""
        return date._value.month in self.months
    
    def successor(self, date: Date) -> Date:
        """Find next date in the specified months"""
        if not date.is_normal():
            return date
        
        current = date
        for _ in range(366):  # At most one year
            current = TimeSet("daily").successor(current)
            if not current.is_normal():
                return current
            if current._value.month in self.months:
                return current
        
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """Find previous date in the specified months"""
        if not date.is_normal():
            return date
        
        current = date
        for _ in range(366):  # At most one year
            current = TimeSet("daily").predecessor(current)
            if not current.is_normal():
                return current
            if current._value.month in self.months:
                return current
        
        return Date.begin()
    
    def __str__(self) -> str:
        return f"Month({self.months})"


class YearTimeSet(TimeSetBase):
    """
    TimeSet for specific years
    Equivalent to TOL's Y function
    """
    
    def __init__(self, years: Union[int, List[int]]):
        super().__init__()
        self.years = [years] if isinstance(years, int) else list(years)
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date falls in one of the specified years"""
        return date._value.year in self.years
    
    def successor(self, date: Date) -> Date:
        """Find next date in the specified years"""
        if not date.is_normal():
            return date
        
        current = date
        # If current year is in list, just go to next day
        if current._value.year in self.years:
            next_date = TimeSet("daily").successor(current)
            if next_date.is_normal() and next_date._value.year in self.years:
                return next_date
        
        # Find next year in list
        current_year = current._value.year
        next_years = [y for y in self.years if y > current_year]
        if not next_years:
            return Date.end()
        
        next_year = min(next_years)
        return Date(datetime(next_year, 1, 1))
    
    def predecessor(self, date: Date) -> Date:
        """Find previous date in the specified years"""
        if not date.is_normal():
            return date
        
        current = date
        # If current year is in list, just go to previous day
        if current._value.year in self.years:
            prev_date = TimeSet("daily").predecessor(current)
            if prev_date.is_normal() and prev_date._value.year in self.years:
                return prev_date
        
        # Find previous year in list
        current_year = current._value.year
        prev_years = [y for y in self.years if y < current_year]
        if not prev_years:
            return Date.begin()
        
        prev_year = max(prev_years)
        return Date(datetime(prev_year, 12, 31))
    
    def __str__(self) -> str:
        return f"Year({self.years})"


class WeekDayTimeSet(TimeSetBase):
    """
    TimeSet for specific weekdays
    Equivalent to TOL's WD function
    0=Monday, 6=Sunday
    """
    
    def __init__(self, weekdays: Union[int, List[int]]):
        super().__init__()
        self.weekdays = [weekdays] if isinstance(weekdays, int) else list(weekdays)
        # Validate weekdays are in range 0-6
        for wd in self.weekdays:
            if wd < 0 or wd > 6:
                raise ValueError(f"Weekday {wd} out of range (0-6)")
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date falls on one of the specified weekdays"""
        return date._value.weekday() in self.weekdays
    
    def successor(self, date: Date) -> Date:
        """Find next date matching the weekday constraint"""
        if not date.is_normal():
            return date
        
        current = date
        for _ in range(7):  # At most need to check one week
            current = TimeSet("daily").successor(current)
            if not current.is_normal():
                return current
            if current._value.weekday() in self.weekdays:
                return current
        
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """Find previous date matching the weekday constraint"""
        if not date.is_normal():
            return date
        
        current = date
        for _ in range(7):  # At most need to check one week
            current = TimeSet("daily").predecessor(current)
            if not current.is_normal():
                return current
            if current._value.weekday() in self.weekdays:
                return current
        
        return Date.begin()
    
    def __str__(self) -> str:
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        day_names = [days[wd] for wd in self.weekdays]
        return f"WeekDay({day_names})"


class HourTimeSet(TimeSetBase):
    """
    TimeSet for specific hours (0-23)
    Equivalent to TOL's H function
    """
    
    def __init__(self, hours: Union[int, List[int]]):
        super().__init__()
        self.hours = [hours] if isinstance(hours, int) else list(hours)
        # Validate hours are in range 0-23
        for hour in self.hours:
            if hour < 0 or hour > 23:
                raise ValueError(f"Hour {hour} out of range (0-23)")
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date/time falls in one of the specified hours"""
        return date._value.hour in self.hours
    
    def successor(self, date: Date) -> Date:
        """Find next hour matching the constraint"""
        if not date.is_normal():
            return date
        
        current = date._value
        for _ in range(24):  # At most one day
            current = current + timedelta(hours=1)
            if current.hour in self.hours:
                new_date = Date.__new__(Date)
                new_date._value = current
                return new_date
        
        return Date.end()
    
    def __str__(self) -> str:
        return f"Hour({self.hours})"


class MinuteTimeSet(TimeSetBase):
    """
    TimeSet for specific minutes (0-59)
    Equivalent to TOL's Mi function
    """
    
    def __init__(self, minutes: Union[int, List[int]]):
        super().__init__()
        self.minutes = [minutes] if isinstance(minutes, int) else list(minutes)
        # Validate minutes are in range 0-59
        for minute in self.minutes:
            if minute < 0 or minute > 59:
                raise ValueError(f"Minute {minute} out of range (0-59)")
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date/time falls in one of the specified minutes"""
        return date._value.minute in self.minutes
    
    def successor(self, date: Date) -> Date:
        """Find next minute matching the constraint"""
        if not date.is_normal():
            return date
        
        current = date._value
        for _ in range(60):  # At most one hour
            current = current + timedelta(minutes=1)
            if current.minute in self.minutes:
                new_date = Date.__new__(Date)
                new_date._value = current
                return new_date
        
        return Date.end()
    
    def __str__(self) -> str:
        return f"Minute({self.minutes})"


class SecondTimeSet(TimeSetBase):
    """
    TimeSet for specific seconds (0-59)
    Equivalent to TOL's S function
    """
    
    def __init__(self, seconds: Union[int, List[int]]):
        super().__init__()
        self.seconds = [seconds] if isinstance(seconds, int) else list(seconds)
        # Validate seconds are in range 0-59
        for second in self.seconds:
            if second < 0 or second > 59:
                raise ValueError(f"Second {second} out of range (0-59)")
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date/time falls in one of the specified seconds"""
        return date._value.second in self.seconds
    
    def successor(self, date: Date) -> Date:
        """Find next second matching the constraint"""
        if not date.is_normal():
            return date
        
        current = date._value
        for _ in range(60):  # At most one minute
            current = current + timedelta(seconds=1)
            if current.second in self.seconds:
                new_date = Date.__new__(Date)
                new_date._value = current
                return new_date
        
        return Date.end()
    
    def __str__(self) -> str:
        return f"Second({self.seconds})"


class PeriodicTimeSet(TimeSetBase):
    """
    Periodic TimeSet - every n units from a start date
    Equivalent to TOL's Periodic function
    """
    
    def __init__(self, start_date: Date, period: int, units: str = "days"):
        super().__init__()
        self.start_date = start_date
        self.period = period
        self.units = units
        
        # Map units to timedelta
        self.unit_map = {
            "days": lambda n: timedelta(days=n),
            "weeks": lambda n: timedelta(weeks=n),
            "hours": lambda n: timedelta(hours=n),
            "minutes": lambda n: timedelta(minutes=n),
            "seconds": lambda n: timedelta(seconds=n),
        }
        
        if units not in self.unit_map:
            raise ValueError(f"Unknown time unit: {units}")
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date is on the periodic schedule"""
        if not self.start_date.is_normal():
            return False
        
        if date._value < self.start_date._value:
            return False
        
        # For simple units, check if difference is divisible by period
        if self.units in ["days", "weeks", "hours", "minutes", "seconds"]:
            diff = date._value - self.start_date._value
            
            if self.units == "days":
                return diff.days % self.period == 0
            elif self.units == "weeks":
                return (diff.days // 7) % self.period == 0
            elif self.units == "hours":
                return (diff.total_seconds() // 3600) % self.period == 0
            elif self.units == "minutes":
                return (diff.total_seconds() // 60) % self.period == 0
            elif self.units == "seconds":
                return int(diff.total_seconds()) % self.period == 0
        
        return False
    
    def successor(self, date: Date) -> Date:
        """Find next date in the periodic sequence"""
        if not date.is_normal():
            return date
        
        # If before start, return start
        if date._value < self.start_date._value:
            return self.start_date
        
        # Calculate next occurrence
        delta_func = self.unit_map[self.units]
        
        # Find how many periods have passed
        diff = date._value - self.start_date._value
        
        if self.units == "days":
            periods_passed = diff.days // self.period
            next_occurrence = self.start_date._value + timedelta(days=(periods_passed + 1) * self.period)
        elif self.units == "weeks":
            weeks_passed = diff.days // 7 // self.period
            next_occurrence = self.start_date._value + timedelta(weeks=(weeks_passed + 1) * self.period)
        else:
            # For sub-day units, use total_seconds
            if self.units == "hours":
                unit_seconds = 3600
            elif self.units == "minutes":
                unit_seconds = 60
            else:  # seconds
                unit_seconds = 1
            
            total_units = int(diff.total_seconds() // unit_seconds)
            periods_passed = total_units // self.period
            next_occurrence = self.start_date._value + delta_func((periods_passed + 1) * self.period)
        
        new_date = Date.__new__(Date)
        new_date._value = next_occurrence
        return new_date
    
    def __str__(self) -> str:
        return f"Periodic(start={self.start_date}, every {self.period} {self.units})"


class RangeTimeSet(TimeSetBase):
    """
    TimeSet for a date range with a step
    Equivalent to TOL's Range function
    """
    
    def __init__(self, start: Date, end: Date, step: TimeSet):
        super().__init__()
        self.start = start
        self.end = end
        self.step = step
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date is within range and matches the step from start"""
        
        if date._value < self.start._value or date._value > self.end._value:
            return False
        
        # For basic TimeSet steps (daily, weekly, etc.), check if date aligns with step from start
        if hasattr(self.step, 'frequency'):
            if self.step.frequency == "daily":
                return True  # All dates in range are valid for daily
            elif self.step.frequency == "weekly":
                # Check if date is exactly n weeks from start
                diff_days = (date._value - self.start._value).days
                return diff_days % 7 == 0
            elif self.step.frequency == "monthly":
                # Check if same day of month and n months apart
                return (date._value.day == self.start._value.day and 
                       (date._value.year - self.start._value.year) * 12 + 
                       (date._value.month - self.start._value.month) >= 0)
        
        # For other step types, use the step's includes method
        return self.step.includes(date)
    
    def successor(self, date: Date) -> Date:
        """Find next date in the range"""
        if not date.is_normal():
            return date
        
        # If before start, return start (if it matches step)
        if date._value < self.start._value:
            if self.step.includes(self.start):
                return self.start
            else:
                current = self.start
        else:
            current = date
        
        # Find next date matching step
        while current._value <= self.end._value:
            current = self.step.successor(current)
            if not current.is_normal():
                return current
            if current._value <= self.end._value:
                return current
        
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """Find previous date in the range"""
        if not date.is_normal():
            return date
        
        # If after end, start from end
        if date._value > self.end._value:
            current = self.end
        else:
            current = date
        
        # Find previous date matching step
        while current._value >= self.start._value:
            current = self.step.predecessor(current)
            if not current.is_normal():
                return current
            if current._value >= self.start._value:
                return current
        
        return Date.begin()
    
    def __str__(self) -> str:
        return f"Range({self.start} to {self.end}, step={self.step})"


class SerieTimeSet(TimeSetBase):
    """
    TimeSet from a Serie's dates
    Equivalent to TOL's SerTms function
    """
    
    def __init__(self, serie: 'Serie'):
        super().__init__()
        self.serie = serie
        # Cache the dates for efficiency
        self._dates = []
        current = serie.first_date
        while current <= serie.last_date:
            self._dates.append(current)
            current = serie.dating.successor(current)
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date is one of the Serie's dates"""
        return date in self._dates
    
    def successor(self, date: Date) -> Date:
        """Find next date in the Serie"""
        if not date.is_normal():
            return date
        
        for d in self._dates:
            if d._value > date._value:
                return d
        
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """Find previous date in the Serie"""
        if not date.is_normal():
            return date
        
        for d in reversed(self._dates):
            if d._value < date._value:
                return d
        
        return Date.begin()
    
    def __str__(self) -> str:
        return f"SerieTimeSet({len(self._dates)} dates)"


class SpecificDatesTimeSet(TimeSetBase):
    """
    TimeSet containing exactly the specified dates
    Used for dates_of_set and other functions that need exact date lists
    """
    
    def __init__(self, dates: List[Date]):
        super().__init__()
        self._dates = sorted(dates, key=lambda d: d._value if d.is_normal() else datetime.min)
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date is one of the specific dates"""
        return any(d._value == date._value for d in self._dates if d.is_normal())
    
    def successor(self, date: Date) -> Date:
        """Find next date in the specific dates list"""
        if not date.is_normal():
            return date
        
        for d in self._dates:
            if d.is_normal() and d._value > date._value:
                return d
        
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """Find previous date in the specific dates list"""
        if not date.is_normal():
            return date
        
        for d in reversed(self._dates):
            if d.is_normal() and d._value < date._value:
                return d
        
        return Date.begin()
    
    def get_dates_list(self) -> List[Date]:
        """Get the dates as a Python list (for convenience)"""
        return self._dates.copy()
    
    def __len__(self) -> int:
        return len(self._dates)
    
    def __str__(self) -> str:
        if len(self._dates) <= 3:
            date_strs = [str(d) for d in self._dates]
            return f"SpecificDates({date_strs})"
        else:
            return f"SpecificDates({len(self._dates)} dates: {self._dates[0]}...{self._dates[-1]})"


class LastDayOfMonthTimeSet(TimeSetBase):
    """
    TimeSet for the last day of each month
    More precise than TimeSet.day([28, 29, 30, 31])
    """
    
    def __init__(self):
        super().__init__()
    
    def _contains_impl(self, date: Date) -> bool:
        """Check if date is the last day of its month"""
        # Get the next day
        next_day = date.add_days(1)
        # If next day is the 1st, current day is last of month
        return next_day._value.day == 1
    
    def successor(self, date: Date) -> Date:
        """Find next last day of month"""
        if not date.is_normal():
            return date
        
        # Start from the day after current date
        current = date.add_days(1)
        
        # Find the next last day of month
        for _ in range(60):  # Safety limit - 2 months max
            if self.includes(current):
                return current
            current = current.add_days(1)
            if not current.is_normal():
                return Date.end()
        
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """Find previous last day of month"""
        if not date.is_normal():
            return date
        
        # Start from the day before current date
        current = date.add_days(-1)
        
        # Find the previous last day of month
        for _ in range(60):  # Safety limit - 2 months max
            if self.includes(current):
                return current
            current = current.add_days(-1)
            if not current.is_normal():
                return Date.begin()
        
        return Date.begin()
    
    def __str__(self) -> str:
        return "LastDayOfMonth()"


class TimeSetSuccessor(TimeSetBase):
    """
    TimeSet that represents a shifted TimeSet by a displacement
    Equivalent to TOL's BTmsSuccessor
    """
    
    def __init__(self, center: TimeSet, displacement: int, units: TimeSet):
        super().__init__()
        self.center = center
        self.displacement = displacement
        self.units = units
    
    def _contains_impl(self, date: Date) -> bool:
        """Implement C++ SuccSetStatus logic exactly"""
        if self.displacement == 0:
            # Zero displacement: intersection of center and units
            return self.center.includes(date) and self.units.includes(date)
        
        # First check if date is in units (C++ pattern)
        if not self.units.includes(date):
            return False
        
        # Implement C++ SuccSetStatus algorithm
        return self._succ_set_status(date)
    
    def _succ_set_status(self, date: Date) -> bool:
        """C++ SuccSetStatus algorithm implementation"""
        n = self.displacement
        
        if n > 0:
            # Positive displacement logic
            u1 = self._units_next(date, -n + 1)
            if not u1.is_normal():
                return False
            
            u0 = self.units.predecessor(u1)
            if not u0.is_normal():
                return False
            
            c = self._find_center_before(u1)
            return c.is_normal() and c >= u0
            
        elif n < 0:
            # Negative displacement logic  
            u1 = self._units_next(date, -n - 1)
            if not u1.is_normal():
                return False
            
            u0 = self.units.successor(u1)
            if not u0.is_normal():
                return False
            
            c = self._find_center_after(u1)
            return c.is_normal() and c <= u0
        
        return False
    
    def _units_next(self, date: Date, n: int) -> Date:
        """Navigate n steps in units TimeSet"""
        current = date
        if n > 0:
            for _ in range(n):
                current = self.units.successor(current)
                if not current.is_normal():
                    break
        elif n < 0:
            for _ in range(abs(n)):
                current = self.units.predecessor(current)
                if not current.is_normal():
                    break
        return current
    
    def _find_center_before(self, date: Date) -> Date:
        """Find center element before given date"""
        current = date
        while current.is_normal():
            current = self.center.predecessor(current)
            if current.is_normal() and self.center.includes(current):
                return current
        return Date.end()
    
    def _find_center_after(self, date: Date) -> Date:
        """Find center element after given date"""
        current = date
        while current.is_normal():
            current = self.center.successor(current)
            if current.is_normal() and self.center.includes(current):
                return current
        return Date.end()
    
    def successor(self, date: Date) -> Date:
        """Implement C++ SafeSuccessor binary search algorithm"""
        if not date.is_normal():
            return date
        
        # Binary search implementation (simplified)
        # Start with exponential expansion to find bounds
        r = 1
        while r <= 1000:  # Safety limit
            candidate = self._find_candidate_successor(date, r)
            if candidate.is_normal() and candidate > date and self.includes(candidate):
                return candidate
            r *= 2
        
        return Date.end()
    
    def _find_candidate_successor(self, date: Date, range_mult: int) -> Date:
        """Helper for binary search - find candidate in expanded range"""
        # Simplified implementation - full C++ version is more complex
        current = date
        for _ in range(range_mult):
            current = self.units.successor(current)
            if not current.is_normal():
                break
            if self.includes(current):
                return current
        return Date.end()
    
    def predecessor(self, date: Date) -> Date:
        """Find previous date in the successor TimeSet"""
        if not date.is_normal():
            return date
        
        # Start searching from the previous date
        current = self.units.predecessor(date)
        
        # Search for previous date that is included
        for _ in range(1000):  # Safety limit
            if not current.is_normal():
                return Date.begin()
            if self.includes(current):
                return current
            current = self.units.predecessor(current)
        
        return Date.begin()
    
    def __str__(self) -> str:
        sign = "+" if self.displacement >= 0 else ""
        return f"Successor({self.center}, {sign}{self.displacement}, {self.units})"