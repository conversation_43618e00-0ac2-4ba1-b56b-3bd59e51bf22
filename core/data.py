"""
Data container for TOL Python implementation
Mirrors the BData functionality from TOL
"""

import numpy as np
from typing import Optional, Union, Any, List
import numpy.ma as ma


class Data:
    """
    TOL-compatible data container
    Wraps NumPy array with TOL-specific semantics including missing values
    """
    
    def __init__(self, size: Optional[int] = None, data: Optional[np.ndarray] = None):
        if data is not None:
            if isinstance(data, list):
                # Convert list to numpy array, preserving None as NaN
                self._data = ma.array([np.nan if x is None else x for x in data])
            else:
                self._data = ma.array(data)
        elif size is not None:
            # Create empty array with specified size
            self._data = ma.empty(size)
            self._data[:] = ma.masked  # All values initially missing
        else:
            self._data = ma.array([])
    
    def __len__(self) -> int:
        return len(self._data)
    
    def __getitem__(self, index: Union[int, slice]) -> Union[float, ma.MaskedArray]:
        """Get item by index"""
        return self._data[index]
    
    def __setitem__(self, index: Union[int, slice], value: Union[float, None]):
        """Set item by index"""
        if value is None:
            self._data[index] = ma.masked
        else:
            self._data[index] = value
    
    def alloc_buffer(self, size: int):
        """Allocate buffer with specified size"""
        self._data = ma.empty(size)
        self._data[:] = ma.masked
    
    def realloc_buffer(self, new_size: int):
        """Reallocate buffer to new size, preserving existing data"""
        old_size = len(self._data)
        new_data = ma.empty(new_size)
        new_data[:] = ma.masked
        
        # Copy existing data
        copy_size = min(old_size, new_size)
        if copy_size > 0:
            new_data[:copy_size] = self._data[:copy_size]
        
        self._data = new_data
    
    def delete_buffer(self):
        """Delete buffer"""
        self._data = ma.array([])
    
    def is_missing(self, index: int) -> bool:
        """Check if value at index is missing"""
        return ma.is_masked(self._data[index])
    
    def to_numpy(self) -> np.ndarray:
        """Convert to regular NumPy array (missing values become NaN for float, or stay masked)"""
        # If data is integer type, we need to convert to float to support NaN
        if np.issubdtype(self._data.dtype, np.integer):
            # Convert to float first so we can use NaN for missing values
            float_data = self._data.astype(np.float64)
            return float_data.filled(np.nan)
        else:
            # For float types, we can directly fill with NaN
            return self._data.filled(np.nan)
    
    def to_list(self) -> List[Optional[float]]:
        """Convert to Python list (masked values become None)"""
        result = []
        for i in range(len(self._data)):
            if ma.is_masked(self._data[i]):
                result.append(None)
            else:
                result.append(float(self._data[i]))
        return result
    
    def copy(self) -> 'Data':
        """Create a copy of the data"""
        new_data = Data()
        new_data._data = self._data.copy()
        return new_data
    
    def __repr__(self) -> str:
        return f"Data({self._data})"