"""
Bayesian estimation framework for TOL Python

This module implements TOL's Bayesian hierarchical estimator capabilities,
including MCMC sampling, prior specification, and Bayesian ARIMA models.

Based on TOL's BSR (Bayesian Sparse Regression) and BysMcmc framework.
"""

try:
    # Try relative imports first (when used as package)
    from .core import BayesianModel, Prior, MCMCConfig
    from .priors import NormalPrior, GammaPrior, InverseGammaPrior, HierarchicalPrior
    from .arima import BayesianARIMA, BayesianARIMAResults
except ImportError:
    # Fallback to absolute imports (when used directly)
    from bayesian.core import BayesianModel, Prior, MCMCConfig
    from bayesian.priors import NormalPrior, GammaPrior, InverseGammaPrior, HierarchicalPrior
    from bayesian.arima import BayesianARIMA, BayesianARIMAResults

__all__ = [
    'BayesianModel',
    'Prior', 
    'MCMCConfig',
    'NormalPrior',
    'GammaPrior', 
    'InverseGammaPrior',
    'HierarchicalPrior',
    'BayesianARIMA',
    'BayesianARIMAResults'
]