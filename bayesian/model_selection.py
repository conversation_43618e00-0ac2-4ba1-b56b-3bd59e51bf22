"""
Bayesian model comparison and selection

Implements TOL's Bayesian model selection framework with:
- Deviance Information Criterion (DIC)
- Watanabe-Akaike Information Criterion (WAIC)
- Marginal likelihood estimation
- Bayes factors for model comparison
- Cross-validation for predictive performance

Based on TOL's model comparison capabilities and Bayesian methodology.
"""

import numpy as np
import numpy.ma as ma
from typing import Dict, List, Optional, Union, Tuple, Any, Callable
import warnings
from scipy import stats, special
from scipy.special import logsumexp

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from .core import BayesianModel
    from .arima import BayesianARIMA, BayesianARIMAResults
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from bayesian.core import BayesianModel
    from bayesian.arima import BayesianARIMA, BayesianARIMAResults


class ModelComparisonCriteria:
    """
    Model comparison criteria for Bayesian models
    
    Implements various information criteria and model selection methods
    for comparing Bayesian models fitted to the same data.
    """
    
    @staticmethod
    def compute_dic(model: BayesianModel, mcmc_results: Dict[str, Any]) -> Dict[str, float]:
        """
        Compute Deviance Information Criterion (DIC)
        
        DIC = D(θ̄) + 2p_D where:
        - D(θ̄) is deviance at posterior mean
        - p_D is effective number of parameters
        
        Args:
            model: Bayesian model
            mcmc_results: MCMC sampling results
            
        Returns:
            Dictionary with DIC components
        """
        parameter_store = mcmc_results['parameter_store']
        
        # Get posterior samples
        all_samples = []
        deviances = []
        
        # Collect samples and compute deviances
        n_samples = min(1000, mcmc_results['n_samples'])  # Limit for computation
        
        for i in range(n_samples):
            # Get parameter values for this sample
            sample_params = {}
            for param_name in model.get_parameter_names():
                history = parameter_store.get_history(param_name)
                if len(history) > i:
                    sample_params[param_name] = history[i]
            
            if len(sample_params) == len(model.get_parameter_names()):
                all_samples.append(sample_params)
                
                # Compute deviance: D(θ) = -2 * log(L(θ))
                log_lik = model.log_likelihood(sample_params)
                deviance = -2 * log_lik if np.isfinite(log_lik) else np.inf
                deviances.append(deviance)
        
        if len(deviances) == 0:
            return {'DIC': np.nan, 'D_bar': np.nan, 'p_D': np.nan}
        
        deviances = np.array(deviances)
        valid_deviances = deviances[np.isfinite(deviances)]
        
        if len(valid_deviances) == 0:
            return {'DIC': np.nan, 'D_bar': np.nan, 'p_D': np.nan}
        
        # Posterior mean deviance
        D_bar = np.mean(valid_deviances)
        
        # Compute deviance at posterior mean parameters
        posterior_means = {}
        for param_name in model.get_parameter_names():
            history = parameter_store.get_history(param_name)
            if len(history) > 0:
                posterior_means[param_name] = np.mean(history)
        
        log_lik_mean = model.log_likelihood(posterior_means)
        D_theta_bar = -2 * log_lik_mean if np.isfinite(log_lik_mean) else np.inf
        
        # Effective number of parameters
        p_D = D_bar - D_theta_bar
        
        # DIC
        DIC = D_theta_bar + 2 * p_D
        
        return {
            'DIC': DIC,
            'D_bar': D_bar,
            'D_theta_bar': D_theta_bar,
            'p_D': p_D
        }
    
    @staticmethod
    def compute_waic(model: BayesianModel, mcmc_results: Dict[str, Any],
                    data_points: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Compute Watanabe-Akaike Information Criterion (WAIC)
        
        WAIC = -2 * (lppd - p_WAIC) where:
        - lppd is log pointwise predictive density
        - p_WAIC is effective number of parameters
        
        Args:
            model: Bayesian model
            mcmc_results: MCMC sampling results
            data_points: Individual data points (if available)
            
        Returns:
            Dictionary with WAIC components
        """
        # This is a simplified implementation
        # Full WAIC requires pointwise log-likelihood evaluation
        
        parameter_store = mcmc_results['parameter_store']
        n_samples = min(500, mcmc_results['n_samples'])
        
        # Collect log-likelihood samples
        log_lik_samples = []
        
        for i in range(n_samples):
            sample_params = {}
            for param_name in model.get_parameter_names():
                history = parameter_store.get_history(param_name)
                if len(history) > i:
                    sample_params[param_name] = history[i]
            
            if len(sample_params) == len(model.get_parameter_names()):
                log_lik = model.log_likelihood(sample_params)
                if np.isfinite(log_lik):
                    log_lik_samples.append(log_lik)
        
        if len(log_lik_samples) == 0:
            return {'WAIC': np.nan, 'lppd': np.nan, 'p_WAIC': np.nan}
        
        log_lik_samples = np.array(log_lik_samples)
        
        # Log pointwise predictive density (simplified)
        # For full implementation, need pointwise likelihood
        lppd = np.mean(log_lik_samples)
        
        # Effective number of parameters (simplified)
        p_WAIC = np.var(log_lik_samples)
        
        # WAIC
        WAIC = -2 * (lppd - p_WAIC)
        
        return {
            'WAIC': WAIC,
            'lppd': lppd,
            'p_WAIC': p_WAIC
        }
    
    @staticmethod
    def compute_marginal_likelihood(model: BayesianModel, mcmc_results: Dict[str, Any],
                                  method: str = "harmonic_mean") -> float:
        """
        Estimate marginal likelihood p(y|M)
        
        Uses various estimation methods:
        - Harmonic mean estimator (biased but simple)
        - Bridge sampling (more accurate, requires implementation)
        - Importance sampling
        
        Args:
            model: Bayesian model
            mcmc_results: MCMC results
            method: Estimation method
            
        Returns:
            Log marginal likelihood estimate
        """
        parameter_store = mcmc_results['parameter_store']
        n_samples = min(1000, mcmc_results['n_samples'])
        
        # Collect likelihood samples
        log_lik_samples = []
        
        for i in range(n_samples):
            sample_params = {}
            for param_name in model.get_parameter_names():
                history = parameter_store.get_history(param_name)
                if len(history) > i:
                    sample_params[param_name] = history[i]
            
            if len(sample_params) == len(model.get_parameter_names()):
                log_lik = model.log_likelihood(sample_params)
                if np.isfinite(log_lik):
                    log_lik_samples.append(log_lik)
        
        if len(log_lik_samples) == 0:
            return np.nan
        
        log_lik_samples = np.array(log_lik_samples)
        
        if method == "harmonic_mean":
            # Harmonic mean estimator: 1/E[1/L(θ|y)]
            # Compute in log space for numerical stability
            return -logsumexp(-log_lik_samples) + np.log(len(log_lik_samples))
        
        elif method == "importance_sampling":
            # Simple importance sampling using prior as proposal
            # This is a placeholder - full implementation needed
            return np.mean(log_lik_samples)
        
        else:
            raise ValueError(f"Unknown method: {method}")


class BayesFactorComparison:
    """
    Bayes factor computation for model comparison
    
    Computes Bayes factors B_{12} = p(y|M_1) / p(y|M_2) for comparing models
    """
    
    @staticmethod
    def compute_bayes_factor(model1_marginal_lik: float, 
                           model2_marginal_lik: float) -> Dict[str, float]:
        """
        Compute Bayes factor and interpretation
        
        Args:
            model1_marginal_lik: Log marginal likelihood of model 1
            model2_marginal_lik: Log marginal likelihood of model 2
            
        Returns:
            Dictionary with Bayes factor and interpretation
        """
        # Log Bayes factor
        log_bf = model1_marginal_lik - model2_marginal_lik
        
        # Bayes factor
        bf = np.exp(log_bf)
        
        # Interpretation (Kass and Raftery, 1995)
        if abs(log_bf) < 1:
            evidence = "Not worth more than a bare mention"
        elif abs(log_bf) < 3:
            evidence = "Positive evidence"
        elif abs(log_bf) < 5:
            evidence = "Strong evidence"
        else:
            evidence = "Very strong evidence"
        
        if log_bf > 0:
            interpretation = f"{evidence} for Model 1"
        else:
            interpretation = f"{evidence} for Model 2"
        
        return {
            'log_bayes_factor': log_bf,
            'bayes_factor': bf,
            'evidence_strength': evidence,
            'interpretation': interpretation
        }


class ARIMAModelSelection:
    """
    Automatic ARIMA model selection using Bayesian criteria
    
    Implements model selection procedures for finding optimal ARIMA specifications
    """
    
    def __init__(self, max_p: int = 3, max_d: int = 2, max_q: int = 3,
                 seasonal: bool = False, max_P: int = 2, max_D: int = 1, max_Q: int = 2,
                 season_length: int = 12):
        """
        Initialize ARIMA model selection
        
        Args:
            max_p: Maximum AR order
            max_d: Maximum differencing order
            max_q: Maximum MA order
            seasonal: Whether to include seasonal components
            max_P: Maximum seasonal AR order
            max_D: Maximum seasonal differencing order
            max_Q: Maximum seasonal MA order
            season_length: Seasonal period length
        """
        self.max_p = max_p
        self.max_d = max_d
        self.max_q = max_q
        self.seasonal = seasonal
        self.max_P = max_P
        self.max_D = max_D
        self.max_Q = max_Q
        self.season_length = season_length
        
        self.fitted_models = []
        self.model_criteria = []
    
    def select_model(self, serie: Serie, criterion: str = "DIC",
                    max_models: int = 20) -> Tuple[BayesianARIMA, Dict[str, Any]]:
        """
        Select best ARIMA model using Bayesian criteria
        
        Args:
            serie: Time series data
            criterion: Selection criterion ("DIC", "WAIC", "marginal_likelihood")
            max_models: Maximum number of models to try
            
        Returns:
            Tuple of (best_model, comparison_results)
        """
        from ..arima import ARIMAFactor
        
        # Generate candidate models
        candidate_specs = self._generate_candidate_models()
        
        # Limit number of models
        if len(candidate_specs) > max_models:
            # Prioritize simpler models
            candidate_specs = sorted(candidate_specs, 
                                   key=lambda x: sum(x[:3]) + (sum(x[3:6]) if len(x) > 3 else 0))
            candidate_specs = candidate_specs[:max_models]
        
        results = []
        
        for i, spec in enumerate(candidate_specs):
            try:
                print(f"Fitting model {i+1}/{len(candidate_specs)}: ARIMA{spec}")
                
                # Create ARIMA factor
                if self.seasonal and len(spec) >= 6:
                    p, d, q, P, D, Q = spec
                    factor = ARIMAFactor(p, d, q, P, D, Q, self.season_length)
                else:
                    p, d, q = spec[:3]
                    factor = ARIMAFactor(p, d, q)
                
                # Fit Bayesian ARIMA
                bayesian_arima = BayesianARIMA(factor)
                bayes_results = bayesian_arima.fit(serie)
                
                # Compute criteria
                criteria_dict = self._compute_criteria(bayesian_arima.model, 
                                                     bayesian_arima.results)
                
                criteria_dict['model'] = bayesian_arima
                criteria_dict['specification'] = spec
                criteria_dict['factor'] = factor
                
                results.append(criteria_dict)
                
            except Exception as e:
                warnings.warn(f"Failed to fit ARIMA{spec}: {str(e)}")
                continue
        
        if len(results) == 0:
            raise ValueError("No models successfully fitted")
        
        # Select best model based on criterion
        if criterion == "DIC":
            best_idx = np.argmin([r['DIC'] for r in results])
        elif criterion == "WAIC":
            best_idx = np.argmin([r['WAIC'] for r in results])
        elif criterion == "marginal_likelihood":
            best_idx = np.argmax([r['log_marginal_likelihood'] for r in results])
        else:
            raise ValueError(f"Unknown criterion: {criterion}")
        
        best_model = results[best_idx]['model']
        
        # Compile comparison results
        comparison_results = {
            'best_model_spec': results[best_idx]['specification'],
            'selection_criterion': criterion,
            'all_results': results,
            'ranking': self._rank_models(results, criterion)
        }
        
        return best_model, comparison_results
    
    def _generate_candidate_models(self) -> List[Tuple[int, ...]]:
        """Generate list of candidate ARIMA specifications"""
        candidates = []
        
        # Non-seasonal models
        for p in range(self.max_p + 1):
            for d in range(self.max_d + 1):
                for q in range(self.max_q + 1):
                    # Skip (0,0,0) model
                    if p == 0 and d == 0 and q == 0:
                        continue
                    candidates.append((p, d, q))
        
        # Seasonal models
        if self.seasonal:
            seasonal_candidates = []
            for p in range(self.max_p + 1):
                for d in range(self.max_d + 1):
                    for q in range(self.max_q + 1):
                        for P in range(self.max_P + 1):
                            for D in range(self.max_D + 1):
                                for Q in range(self.max_Q + 1):
                                    # Skip all-zero models
                                    if all(x == 0 for x in [p, d, q, P, D, Q]):
                                        continue
                                    seasonal_candidates.append((p, d, q, P, D, Q))
            
            # Limit seasonal models (they're expensive)
            if len(seasonal_candidates) > 50:
                # Prioritize simpler seasonal models
                seasonal_candidates = sorted(seasonal_candidates, 
                                           key=lambda x: sum(x))[:50]
            
            candidates.extend(seasonal_candidates)
        
        return candidates
    
    def _compute_criteria(self, model: BayesianModel, 
                         results: BayesianARIMAResults) -> Dict[str, float]:
        """Compute model selection criteria"""
        criteria = {}
        
        # Create dummy mcmc_results for compatibility
        mcmc_results = {
            'n_samples': results.n_samples,
            'parameter_store': None  # Would need actual parameter store
        }
        
        # For now, use simplified criteria based on results
        if results.dic is not None:
            criteria['DIC'] = results.dic
        else:
            criteria['DIC'] = np.inf
        
        if results.waic is not None:
            criteria['WAIC'] = results.waic
        else:
            criteria['WAIC'] = np.inf
        
        if results.log_marginal_likelihood is not None:
            criteria['log_marginal_likelihood'] = results.log_marginal_likelihood
        else:
            criteria['log_marginal_likelihood'] = -np.inf
        
        return criteria
    
    def _rank_models(self, results: List[Dict[str, Any]], 
                    criterion: str) -> List[Dict[str, Any]]:
        """Rank models by selection criterion"""
        
        # Sort by criterion
        if criterion in ["DIC", "WAIC"]:
            # Lower is better
            sorted_results = sorted(results, key=lambda x: x[criterion])
        else:
            # Higher is better (marginal likelihood)
            sorted_results = sorted(results, key=lambda x: x[criterion], reverse=True)
        
        # Add ranking information
        ranking = []
        for i, result in enumerate(sorted_results):
            rank_info = {
                'rank': i + 1,
                'specification': result['specification'],
                criterion: result[criterion],
                'model': result['model']
            }
            ranking.append(rank_info)
        
        return ranking
    
    def summary_table(self, comparison_results: Dict[str, Any]) -> str:
        """Generate summary table of model comparison"""
        ranking = comparison_results['ranking']
        criterion = comparison_results['selection_criterion']
        
        lines = []
        lines.append("Bayesian ARIMA Model Selection Results")
        lines.append("=" * 60)
        lines.append(f"Selection criterion: {criterion}")
        lines.append("")
        lines.append(f"{'Rank':>4s} {'Model':>15s} {criterion:>12s}")
        lines.append("-" * 40)
        
        for rank_info in ranking[:10]:  # Show top 10
            spec = rank_info['specification']
            if len(spec) == 3:
                model_str = f"ARIMA({spec[0]},{spec[1]},{spec[2]})"
            else:
                model_str = f"ARIMA({spec[0]},{spec[1]},{spec[2]})({spec[3]},{spec[4]},{spec[5]})"
            
            criterion_val = rank_info[criterion]
            lines.append(f"{rank_info['rank']:>4d} {model_str:>15s} {criterion_val:>12.2f}")
        
        lines.append("")
        lines.append(f"Best model: {ranking[0]['specification']}")
        
        return "\n".join(lines)