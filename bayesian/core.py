"""
Core Bayesian framework for TOL Python

Implements the foundational classes for Bayesian hierarchical modeling,
following TOL's BysMcmc architecture and BSR (Bayesian Sparse Regression) approach.

Key Components:
- BayesianModel: Base class for hierarchical models
- Prior: Abstract base class for Bayesian priors  
- MCMCConfig: Configuration for MCMC sampling
- Parameter management and model specification

References:
- TOL's BysMcmc::@Config system
- BSR hierarchical linear models
- TOL's block-based MCMC structure
"""

import numpy as np
import numpy.ma as ma
from typing import Dict, List, Optional, Union, Any, Tuple
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
import warnings
from scipy import stats

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie


@dataclass 
class MCMCConfig:
    """
    Configuration for MCMC sampling, based on TOL's BysMcmc::@Config
    
    Mirrors TOL's configuration structure:
    - mcmc.burnin: Number of burn-in iterations
    - mcmc.sampleLength: Number of samples to collect
    - mcmc.cacheLength: Cache size for efficient storage
    - Various tolerance and diagnostic settings
    """
    
    # Core MCMC dimensions (from TOL's BysMcmc)
    mcmc_burnin: int = 1000
    mcmc_sample_length: int = 5000
    mcmc_cache_length: int = 500
    
    # Numerical tolerances (from TOL's basic configuration)
    cholesky_epsilon: float = 1e-13
    cholesky_warning_freq: int = 100
    truncated_normal_gibbs_iterations: int = 5
    
    # Initial value strategies (from TOL's bsr.iniVal options)
    try_find_feasible: bool = True
    try_given_by_user: bool = True
    try_zero_start: bool = True
    try_constrained_mle: bool = True
    try_constrained_min_norm: bool = True
    
    # Diagnostic configuration (from TOL's report settings)
    raftery_diag_q: float = 0.025
    raftery_diag_r: float = 0.007
    raftery_diag_s: float = 0.950
    raftery_diag_eps: float = 0.001
    acf_max_lag: int = 20
    histogram_bins: int = 100
    kernel_density_points: int = 200
    
    # Execution flags
    do_resume: bool = False
    do_report: bool = True
    do_evaluation: bool = True
    do_linear_effects: bool = False
    
    # Convergence criteria
    convergence_tolerance: float = 1e-6
    max_iterations: int = 50000
    thin_interval: int = 1
    
    # Random seed for reproducibility
    random_seed: Optional[int] = None
    
    def __post_init__(self):
        """Validate configuration parameters"""
        if self.mcmc_burnin < 0:
            raise ValueError("Burn-in iterations must be non-negative")
        if self.mcmc_sample_length <= 0:
            raise ValueError("Sample length must be positive") 
        if self.mcmc_cache_length <= 0:
            raise ValueError("Cache length must be positive")
        if not 0 < self.convergence_tolerance < 1:
            raise ValueError("Convergence tolerance must be between 0 and 1")


class Prior(ABC):
    """
    Abstract base class for Bayesian priors
    
    Follows TOL's prior specification system with automatic hyperparameter
    handling and conjugacy detection. Each prior must implement log-density
    evaluation and sampling methods.
    
    TOL Features Implemented:
    - Automatic scaling based on data
    - Hyperparameter updates for conjugate families
    - Support for hierarchical priors
    """
    
    def __init__(self, name: str = ""):
        """
        Initialize prior
        
        Args:
            name: Descriptive name for the prior
        """
        self.name = name
        self._is_conjugate = False
        self._hyperparameters = {}
    
    @abstractmethod
    def log_density(self, x: np.ndarray) -> float:
        """
        Evaluate log-density of prior at x
        
        Args:
            x: Parameter values to evaluate
            
        Returns:
            Log-density value
        """
        pass
    
    @abstractmethod
    def sample(self, n: int = 1, random_state: Optional[int] = None) -> np.ndarray:
        """
        Generate samples from prior distribution
        
        Args:
            n: Number of samples to generate
            random_state: Random seed for reproducibility
            
        Returns:
            Array of samples
        """
        pass
    
    def update_hyperparameters(self, data: np.ndarray, likelihood_params: Dict) -> None:
        """
        Update hyperparameters based on data (for conjugate priors)
        
        Args:
            data: Observed data
            likelihood_params: Parameters from likelihood function
        """
        if not self._is_conjugate:
            warnings.warn(f"Prior {self.name} is not conjugate - hyperparameters not updated")
            return
    
    def is_conjugate(self) -> bool:
        """Check if prior is conjugate to the likelihood"""
        return self._is_conjugate
    
    def get_hyperparameters(self) -> Dict:
        """Get current hyperparameter values"""
        return self._hyperparameters.copy()
    
    def set_hyperparameters(self, **kwargs) -> None:
        """Set hyperparameter values"""
        for key, value in kwargs.items():
            if key in self._hyperparameters:
                self._hyperparameters[key] = value
            else:
                warnings.warn(f"Unknown hyperparameter: {key}")


class BayesianModel(ABC):
    """
    Base class for Bayesian hierarchical models
    
    Implements TOL's BSR modeling framework with:
    - Block-based parameter structure
    - Hierarchical prior specification
    - MCMC configuration management
    - Model validation and diagnostics
    
    Based on TOL's BysMcmc::Bsr architecture with modular blocks
    that can be combined into complex hierarchical models.
    """
    
    def __init__(self, config: Optional[MCMCConfig] = None):
        """
        Initialize Bayesian model
        
        Args:
            config: MCMC configuration (uses defaults if None)
        """
        self.config = config or MCMCConfig()
        self.priors = {}
        self.parameter_blocks = {}
        self.constraints = []
        self.data = None
        self.results = None
        self._is_fitted = False
        
        # Initialize random state if specified
        if self.config.random_seed is not None:
            np.random.seed(self.config.random_seed)
    
    def add_prior(self, parameter_name: str, prior: Prior) -> None:
        """
        Add prior distribution for a parameter
        
        Args:
            parameter_name: Name of the parameter
            prior: Prior distribution object
        """
        self.priors[parameter_name] = prior
    
    def add_parameter_block(self, block_name: str, parameter_names: List[str],
                           block_type: str = "linear") -> None:
        """
        Add parameter block for structured MCMC sampling
        
        Follows TOL's block structure where related parameters are
        updated together for improved mixing.
        
        Args:
            block_name: Name of the parameter block
            parameter_names: List of parameters in this block
            block_type: Type of block (linear, nonlinear, variance, etc.)
        """
        self.parameter_blocks[block_name] = {
            'parameters': parameter_names,
            'type': block_type,
            'last_values': None,
            'acceptance_rate': 0.0
        }
    
    def add_constraint(self, constraint_type: str, parameters: List[str], 
                      bounds: Optional[Tuple] = None, 
                      linear_constraint: Optional[Dict] = None) -> None:
        """
        Add parameter constraints
        
        Supports TOL's constraint types:
        - Box constraints (upper/lower bounds)
        - Linear equality/inequality constraints
        - Positivity constraints
        
        Args:
            constraint_type: 'box', 'linear_eq', 'linear_ineq', 'positive'
            parameters: List of parameter names affected
            bounds: (lower, upper) bounds for box constraints
            linear_constraint: {'A': matrix, 'b': vector} for linear constraints
        """
        constraint = {
            'type': constraint_type,
            'parameters': parameters,
            'bounds': bounds,
            'linear_constraint': linear_constraint
        }
        self.constraints.append(constraint)
    
    @abstractmethod
    def log_likelihood(self, parameters: Dict[str, np.ndarray]) -> float:
        """
        Evaluate log-likelihood given parameters
        
        Args:
            parameters: Dictionary of parameter values
            
        Returns:
            Log-likelihood value
        """
        pass
    
    @abstractmethod
    def log_prior(self, parameters: Dict[str, np.ndarray]) -> float:
        """
        Evaluate log-prior given parameters
        
        Args:
            parameters: Dictionary of parameter values
            
        Returns:
            Log-prior value
        """
        pass
    
    def log_posterior(self, parameters: Dict[str, np.ndarray]) -> float:
        """
        Evaluate log-posterior (log-likelihood + log-prior)
        
        Args:
            parameters: Dictionary of parameter values
            
        Returns:
            Log-posterior value
        """
        try:
            log_lik = self.log_likelihood(parameters)
            log_pri = self.log_prior(parameters)
            
            # Check for invalid values
            if not np.isfinite(log_lik) or not np.isfinite(log_pri):
                return -np.inf
                
            return log_lik + log_pri
            
        except Exception as e:
            warnings.warn(f"Error evaluating log-posterior: {str(e)}")
            return -np.inf
    
    def validate_parameters(self, parameters: Dict[str, np.ndarray]) -> bool:
        """
        Check if parameters satisfy all constraints
        
        Args:
            parameters: Dictionary of parameter values
            
        Returns:
            True if all constraints are satisfied
        """
        for constraint in self.constraints:
            if not self._check_constraint(parameters, constraint):
                return False
        return True
    
    def _check_constraint(self, parameters: Dict[str, np.ndarray], 
                         constraint: Dict) -> bool:
        """
        Check if parameters satisfy a specific constraint
        
        Args:
            parameters: Dictionary of parameter values
            constraint: Constraint specification
            
        Returns:
            True if constraint is satisfied
        """
        constraint_type = constraint['type']
        param_names = constraint['parameters']
        
        if constraint_type == 'box':
            bounds = constraint['bounds']
            if bounds is None:
                return True
                
            lower, upper = bounds
            for param_name in param_names:
                if param_name in parameters:
                    param_val = parameters[param_name]
                    if np.any(param_val < lower) or np.any(param_val > upper):
                        return False
        
        elif constraint_type == 'positive':
            for param_name in param_names:
                if param_name in parameters:
                    if np.any(parameters[param_name] <= 0):
                        return False
        
        elif constraint_type == 'linear_ineq':
            # A * x <= b constraint
            linear_constraint = constraint['linear_constraint']
            if linear_constraint is None:
                return True
                
            A = linear_constraint['A']
            b = linear_constraint['b']
            
            # Build parameter vector in correct order
            x = []
            for param_name in param_names:
                if param_name in parameters:
                    param_val = parameters[param_name]
                    if np.isscalar(param_val):
                        x.append(param_val)
                    else:
                        x.extend(param_val.flatten())
            
            if len(x) > 0:
                x = np.array(x)
                if np.any(A @ x > b + 1e-10):  # Small tolerance for numerical errors
                    return False
        
        return True
    
    def get_parameter_names(self) -> List[str]:
        """Get list of all parameter names in the model"""
        return list(self.priors.keys())
    
    def get_block_names(self) -> List[str]:
        """Get list of all parameter block names"""
        return list(self.parameter_blocks.keys())
    
    def is_fitted(self) -> bool:
        """Check if model has been fitted"""
        return self._is_fitted
    
    def summary(self) -> str:
        """
        Generate summary of model specification
        
        Returns:
            Formatted string with model details
        """
        lines = []
        lines.append("Bayesian Model Summary")
        lines.append("=" * 50)
        lines.append(f"Parameters: {len(self.priors)}")
        lines.append(f"Parameter blocks: {len(self.parameter_blocks)}")
        lines.append(f"Constraints: {len(self.constraints)}")
        lines.append("")
        
        # MCMC configuration
        lines.append("MCMC Configuration:")
        lines.append(f"  Burn-in: {self.config.mcmc_burnin}")
        lines.append(f"  Samples: {self.config.mcmc_sample_length}")
        lines.append(f"  Cache size: {self.config.mcmc_cache_length}")
        lines.append("")
        
        # Prior specifications
        if self.priors:
            lines.append("Prior Distributions:")
            for param_name, prior in self.priors.items():
                lines.append(f"  {param_name}: {prior.__class__.__name__}")
        
        # Parameter blocks
        if self.parameter_blocks:
            lines.append("")
            lines.append("Parameter Blocks:")
            for block_name, block_info in self.parameter_blocks.items():
                params = ", ".join(block_info['parameters'])
                lines.append(f"  {block_name} ({block_info['type']}): {params}")
        
        # Constraints
        if self.constraints:
            lines.append("")
            lines.append("Constraints:")
            for i, constraint in enumerate(self.constraints):
                constraint_type = constraint['type']
                params = ", ".join(constraint['parameters'])
                lines.append(f"  {i+1}. {constraint_type}: {params}")
        
        lines.append("")
        lines.append(f"Model fitted: {self._is_fitted}")
        
        return "\n".join(lines)
    
    def __str__(self) -> str:
        return f"BayesianModel(parameters={len(self.priors)}, blocks={len(self.parameter_blocks)})"
    
    def __repr__(self) -> str:
        return (f"BayesianModel(parameters={list(self.priors.keys())}, "
                f"fitted={self._is_fitted})")


class ParameterStore:
    """
    Parameter storage and management for MCMC sampling
    
    Implements TOL's parameter storage system with efficient access
    and update mechanisms for block-based sampling.
    """
    
    def __init__(self, parameter_names: List[str]):
        """
        Initialize parameter storage
        
        Args:
            parameter_names: List of all parameter names
        """
        self.parameter_names = parameter_names
        self.current_values = {}
        self.history = {}
        self.acceptance_rates = {}
        
        # Initialize with None values
        for name in parameter_names:
            self.current_values[name] = None
            self.history[name] = []
            self.acceptance_rates[name] = 0.0
    
    def set_values(self, parameters: Dict[str, np.ndarray]) -> None:
        """
        Set current parameter values
        
        Args:
            parameters: Dictionary of parameter values
        """
        for name, value in parameters.items():
            if name in self.parameter_names:
                self.current_values[name] = np.array(value).copy()
            else:
                warnings.warn(f"Unknown parameter: {name}")
    
    def get_values(self, names: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
        """
        Get current parameter values
        
        Args:
            names: Specific parameter names to retrieve (all if None)
            
        Returns:
            Dictionary of parameter values
        """
        if names is None:
            names = self.parameter_names
        
        result = {}
        for name in names:
            if name in self.current_values and self.current_values[name] is not None:
                result[name] = self.current_values[name].copy()
        
        return result
    
    def store_sample(self, parameters: Dict[str, np.ndarray]) -> None:
        """
        Store parameter sample in history
        
        Args:
            parameters: Dictionary of parameter values to store
        """
        for name, value in parameters.items():
            if name in self.history:
                self.history[name].append(np.array(value).copy())
    
    def get_history(self, name: str, start: int = 0, end: Optional[int] = None) -> np.ndarray:
        """
        Get parameter history
        
        Args:
            name: Parameter name
            start: Starting index
            end: Ending index (None for all)
            
        Returns:
            Array of historical values
        """
        if name not in self.history:
            raise ValueError(f"Unknown parameter: {name}")
        
        history = self.history[name][start:end]
        if len(history) == 0:
            return np.array([])
        
        return np.array(history)
    
    def clear_history(self) -> None:
        """Clear all stored history"""
        for name in self.parameter_names:
            self.history[name] = []
    
    def get_summary_statistics(self) -> Dict[str, Dict[str, float]]:
        """
        Compute summary statistics for all parameters
        
        Returns:
            Dictionary with mean, std, quantiles for each parameter
        """
        summary = {}
        
        for name in self.parameter_names:
            if len(self.history[name]) > 0:
                samples = np.array(self.history[name])
                
                # Handle scalar vs vector parameters
                if samples.ndim == 1:
                    summary[name] = {
                        'mean': np.mean(samples),
                        'std': np.std(samples),
                        'q025': np.quantile(samples, 0.025),
                        'q050': np.quantile(samples, 0.50),
                        'q975': np.quantile(samples, 0.975),
                        'n_samples': len(samples)
                    }
                else:
                    # For vector parameters, compute statistics for each component
                    n_components = samples.shape[1]
                    summary[name] = {}
                    
                    for i in range(n_components):
                        component_samples = samples[:, i]
                        summary[name][f'component_{i}'] = {
                            'mean': np.mean(component_samples),
                            'std': np.std(component_samples),
                            'q025': np.quantile(component_samples, 0.025),
                            'q050': np.quantile(component_samples, 0.50),
                            'q975': np.quantile(component_samples, 0.975)
                        }
                    
                    summary[name]['n_samples'] = len(samples)
        
        return summary