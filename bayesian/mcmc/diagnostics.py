"""
MCMC convergence diagnostics

Implements TOL's MCMC diagnostic framework including:
- Raftery-<PERSON> diagnostic
- Autocorrelation analysis
- Effective sample size
- Geweke convergence diagnostic
- Potential scale reduction factor (R-hat)

Based on TOL's report configuration and diagnostic capabilities.
"""

import numpy as np
import numpy.ma as ma
from typing import Dict, List, Optional, Union, Tuple, Any
import warnings
from scipy import stats
from scipy.special import logsumexp

from ..core import MCMCConfig


class MCMCDiagnostics:
    """
    MCMC convergence diagnostics following TOL's diagnostic framework
    
    Implements comprehensive diagnostics to assess:
    - Chain convergence
    - Effective sample size
    - Autocorrelation structure
    - Burn-in adequacy
    """
    
    @staticmethod
    def compute_diagnostics(samples: Dict[str, np.ndarray], 
                          config: MCMCConfig) -> Dict[str, Any]:
        """
        Compute comprehensive MCMC diagnostics
        
        Args:
            samples: Dictionary of parameter samples
            config: MCMC configuration with diagnostic settings
            
        Returns:
            Dictionary with diagnostic results
        """
        diagnostics = {}
        
        for param_name, param_samples in samples.items():
            if len(param_samples) < 10:
                warnings.warn(f"Too few samples for {param_name} diagnostics")
                continue
            
            param_diagnostics = {}
            
            # Effective sample size
            param_diagnostics['effective_sample_size'] = MCMCDiagnostics.effective_sample_size(param_samples)
            
            # Autocorrelation function
            param_diagnostics['autocorrelation'] = MCMCDiagnostics.autocorrelation_function(
                param_samples, max_lag=config.acf_max_lag
            )
            
            # Geweke diagnostic
            param_diagnostics['geweke'] = MCMCDiagnostics.geweke_diagnostic(param_samples)
            
            # Raftery-Lewis diagnostic
            param_diagnostics['raftery_lewis'] = MCMCDiagnostics.raftery_lewis_diagnostic(
                param_samples, 
                q=config.raftery_diag_q,
                r=config.raftery_diag_r,
                s=config.raftery_diag_s,
                eps=config.raftery_diag_eps
            )
            
            # Monte Carlo standard error
            param_diagnostics['mc_error'] = MCMCDiagnostics.monte_carlo_error(param_samples)
            
            diagnostics[param_name] = param_diagnostics
        
        return diagnostics
    
    @staticmethod
    def autocorrelation_function(samples: np.ndarray, max_lag: int = 20) -> Dict[str, np.ndarray]:
        """
        Compute autocorrelation function
        
        Args:
            samples: MCMC samples
            max_lag: Maximum lag to compute
            
        Returns:
            Dictionary with autocorrelations and lags
        """
        samples = np.array(samples)
        
        # Handle vector parameters
        if samples.ndim > 1:
            # For vector parameters, compute ACF for first component
            samples = samples[:, 0]
        
        n = len(samples)
        max_lag = min(max_lag, n // 4)  # Don't use too large lags
        
        # Center the data
        samples_centered = samples - np.mean(samples)
        
        # Compute autocorrelations
        autocorrs = []
        lags = np.arange(max_lag + 1)
        
        for lag in lags:
            if lag == 0:
                autocorr = 1.0
            else:
                # Compute lagged correlation
                if n - lag > 10:  # Need sufficient overlap
                    corr = np.corrcoef(samples_centered[:-lag], samples_centered[lag:])[0, 1]
                    autocorr = corr if not np.isnan(corr) else 0.0
                else:
                    autocorr = 0.0
            
            autocorrs.append(autocorr)
        
        return {
            'lags': lags,
            'autocorrelations': np.array(autocorrs)
        }
    
    @staticmethod
    def effective_sample_size(samples: np.ndarray) -> float:
        """
        Compute effective sample size
        
        Uses the autocorrelation-based formula:
        ESS = N / (1 + 2 * Σ ρ_k) where ρ_k is autocorrelation at lag k
        
        Args:
            samples: MCMC samples
            
        Returns:
            Effective sample size
        """
        samples = np.array(samples)
        
        # Handle vector parameters
        if samples.ndim > 1:
            samples = samples[:, 0]
        
        n = len(samples)
        
        # Compute autocorrelations
        acf_result = MCMCDiagnostics.autocorrelation_function(samples, max_lag=min(n//4, 200))
        autocorrs = acf_result['autocorrelations']
        
        # Find cutoff where autocorrelation becomes negligible
        cutoff = 1
        for i in range(1, len(autocorrs)):
            if autocorrs[i] <= 0.05:  # 5% threshold
                cutoff = i
                break
            cutoff = i
        
        # Compute sum of autocorrelations up to cutoff
        autocorr_sum = np.sum(autocorrs[1:cutoff+1])
        
        # Effective sample size
        ess = n / (1 + 2 * autocorr_sum)
        
        return max(1.0, ess)  # ESS should be at least 1
    
    @staticmethod
    def geweke_diagnostic(samples: np.ndarray, first_fraction: float = 0.1,
                         last_fraction: float = 0.5) -> Dict[str, float]:
        """
        Compute Geweke convergence diagnostic
        
        Compares means from first and last portions of the chain.
        Z-score should be ~N(0,1) if chain has converged.
        
        Args:
            samples: MCMC samples
            first_fraction: Fraction of samples from beginning
            last_fraction: Fraction of samples from end
            
        Returns:
            Dictionary with z-score and p-value
        """
        samples = np.array(samples)
        
        # Handle vector parameters
        if samples.ndim > 1:
            samples = samples[:, 0]
        
        n = len(samples)
        
        if n < 100:
            return {'z_score': np.nan, 'p_value': np.nan}
        
        # Split samples
        first_end = int(first_fraction * n)
        last_start = int((1 - last_fraction) * n)
        
        first_samples = samples[:first_end]
        last_samples = samples[last_start:]
        
        # Compute means
        first_mean = np.mean(first_samples)
        last_mean = np.mean(last_samples)
        
        # Compute spectral densities at zero frequency (for standard errors)
        first_var = MCMCDiagnostics._spectral_density_zero(first_samples)
        last_var = MCMCDiagnostics._spectral_density_zero(last_samples)
        
        # Standard error of difference
        se_diff = np.sqrt(first_var / len(first_samples) + last_var / len(last_samples))
        
        if se_diff == 0:
            return {'z_score': np.nan, 'p_value': np.nan}
        
        # Z-score
        z_score = (first_mean - last_mean) / se_diff
        
        # P-value (two-tailed test)
        p_value = 2 * (1 - stats.norm.cdf(np.abs(z_score)))
        
        return {
            'z_score': z_score,
            'p_value': p_value
        }
    
    @staticmethod
    def _spectral_density_zero(samples: np.ndarray) -> float:
        """
        Estimate spectral density at zero frequency
        
        Used for computing standard errors in Geweke diagnostic
        """
        n = len(samples)
        
        # Simple approach: use autocorrelation-based estimate
        acf_result = MCMCDiagnostics.autocorrelation_function(samples, max_lag=min(n//4, 50))
        autocorrs = acf_result['autocorrelations']
        
        # Spectral density at zero: σ² * (1 + 2 * Σ ρ_k)
        sample_var = np.var(samples, ddof=1)
        autocorr_sum = np.sum(autocorrs[1:])
        
        spectral_density = sample_var * (1 + 2 * autocorr_sum)
        
        return max(sample_var, spectral_density)  # At least sample variance
    
    @staticmethod
    def raftery_lewis_diagnostic(samples: np.ndarray, q: float = 0.025,
                                r: float = 0.007, s: float = 0.95,
                                eps: float = 0.001) -> Dict[str, float]:
        """
        Compute Raftery-Lewis diagnostic
        
        Estimates required burn-in and sample size to estimate
        the q-quantile with given precision.
        
        Args:
            samples: MCMC samples
            q: Quantile of interest (default 0.025 for 2.5%)
            r: Desired margin of error (default 0.007)
            s: Probability of achieving desired precision (default 0.95)
            eps: Tolerance for autocorrelation (default 0.001)
            
        Returns:
            Dictionary with diagnostic results
        """
        samples = np.array(samples)
        
        # Handle vector parameters
        if samples.ndim > 1:
            samples = samples[:, 0]
        
        n = len(samples)
        
        if n < 100:
            return {
                'burn_in': np.nan,
                'total_n': np.nan,
                'lower_bound': np.nan,
                'dependence_factor': np.nan
            }
        
        # Compute empirical quantile
        empirical_quantile = np.quantile(samples, q)
        
        # Create binary series (1 if below quantile, 0 otherwise)
        binary_series = (samples <= empirical_quantile).astype(int)
        
        # Estimate transition matrix for binary series
        # This is a simplified implementation
        
        # Count transitions
        n_00 = np.sum((binary_series[:-1] == 0) & (binary_series[1:] == 0))
        n_01 = np.sum((binary_series[:-1] == 0) & (binary_series[1:] == 1))
        n_10 = np.sum((binary_series[:-1] == 1) & (binary_series[1:] == 0))
        n_11 = np.sum((binary_series[:-1] == 1) & (binary_series[1:] == 1))
        
        # Transition probabilities
        if n_00 + n_01 > 0:
            p_01 = n_01 / (n_00 + n_01)
        else:
            p_01 = 0.5
        
        if n_10 + n_11 > 0:
            p_10 = n_10 / (n_10 + n_11)
        else:
            p_10 = 0.5
        
        # Avoid division by zero
        p_01 = max(min(p_01, 1-eps), eps)
        p_10 = max(min(p_10, 1-eps), eps)
        
        # Eigenvalue of transition matrix
        alpha = 2 - p_01 - p_10
        
        # Compute required sample sizes
        z_s = stats.norm.ppf((1 + s) / 2)  # Critical value
        
        # Required total sample size (simplified formula)
        phi = stats.norm.ppf(1 - r/2)
        
        # Dependence factor
        if alpha > eps:
            dependence_factor = (2 - alpha) / (alpha ** 2)
        else:
            dependence_factor = 1000  # Very high dependence
        
        # Minimum sample size
        min_n = (z_s ** 2 * q * (1 - q)) / (r ** 2)
        
        # Adjusted for dependence
        total_n = min_n * dependence_factor
        
        # Burn-in (rule of thumb)
        burn_in = max(int(total_n * 0.1), int(n * 0.1))
        
        return {
            'burn_in': burn_in,
            'total_n': int(total_n),
            'lower_bound': int(min_n),
            'dependence_factor': dependence_factor
        }
    
    @staticmethod
    def monte_carlo_error(samples: np.ndarray) -> float:
        """
        Compute Monte Carlo standard error
        
        Args:
            samples: MCMC samples
            
        Returns:
            Monte Carlo standard error
        """
        samples = np.array(samples)
        
        # Handle vector parameters
        if samples.ndim > 1:
            samples = samples[:, 0]
        
        n = len(samples)
        
        if n < 2:
            return np.nan
        
        # Compute effective sample size
        ess = MCMCDiagnostics.effective_sample_size(samples)
        
        # Monte Carlo error
        sample_std = np.std(samples, ddof=1)
        mc_error = sample_std / np.sqrt(ess)
        
        return mc_error
    
    @staticmethod
    def rhat_diagnostic(chains: List[np.ndarray]) -> float:
        """
        Compute potential scale reduction factor (R-hat)
        
        Requires multiple chains. Measures convergence by comparing
        within-chain and between-chain variance.
        
        Args:
            chains: List of MCMC chains
            
        Returns:
            R-hat statistic
        """
        if len(chains) < 2:
            warnings.warn("R-hat requires at least 2 chains")
            return np.nan
        
        # Convert to arrays and handle vector parameters
        processed_chains = []
        for chain in chains:
            chain = np.array(chain)
            if chain.ndim > 1:
                chain = chain[:, 0]  # Use first component
            processed_chains.append(chain)
        
        # Check chain lengths
        chain_lengths = [len(chain) for chain in processed_chains]
        if not all(length == chain_lengths[0] for length in chain_lengths):
            warnings.warn("All chains must have same length for R-hat")
            return np.nan
        
        m = len(processed_chains)  # Number of chains
        n = chain_lengths[0]       # Length of each chain
        
        if n < 4:
            return np.nan
        
        # Chain means
        chain_means = [np.mean(chain) for chain in processed_chains]
        overall_mean = np.mean(chain_means)
        
        # Between-chain variance
        B = n * np.var(chain_means, ddof=1)
        
        # Within-chain variance
        within_variances = [np.var(chain, ddof=1) for chain in processed_chains]
        W = np.mean(within_variances)
        
        # Pooled variance estimate
        var_hat = ((n - 1) * W + B) / n
        
        # R-hat
        if W > 0:
            rhat = np.sqrt(var_hat / W)
        else:
            rhat = np.nan
        
        return rhat
    
    @staticmethod
    def diagnostic_summary(diagnostics: Dict[str, Any]) -> str:
        """
        Generate formatted summary of diagnostics
        
        Args:
            diagnostics: Dictionary of diagnostic results
            
        Returns:
            Formatted summary string
        """
        lines = []
        lines.append("MCMC Convergence Diagnostics")
        lines.append("=" * 50)
        
        for param_name, param_diag in diagnostics.items():
            lines.append(f"\nParameter: {param_name}")
            lines.append("-" * 30)
            
            # Effective sample size
            if 'effective_sample_size' in param_diag:
                ess = param_diag['effective_sample_size']
                lines.append(f"Effective Sample Size: {ess:.1f}")
            
            # Monte Carlo error
            if 'mc_error' in param_diag:
                mc_err = param_diag['mc_error']
                lines.append(f"Monte Carlo Error: {mc_err:.6f}")
            
            # Geweke diagnostic
            if 'geweke' in param_diag:
                geweke = param_diag['geweke']
                if not np.isnan(geweke['z_score']):
                    lines.append(f"Geweke Z-score: {geweke['z_score']:.3f} (p={geweke['p_value']:.3f})")
                    
                    # Interpretation
                    if geweke['p_value'] < 0.05:
                        lines.append("  Warning: Evidence of non-convergence")
                    else:
                        lines.append("  OK: No evidence of non-convergence")
            
            # Raftery-Lewis diagnostic
            if 'raftery_lewis' in param_diag:
                rl = param_diag['raftery_lewis']
                if not np.isnan(rl['burn_in']):
                    lines.append(f"Raftery-Lewis:")
                    lines.append(f"  Recommended burn-in: {rl['burn_in']}")
                    lines.append(f"  Recommended total N: {rl['total_n']}")
                    lines.append(f"  Dependence factor: {rl['dependence_factor']:.1f}")
        
        return "\n".join(lines)
    
    @staticmethod
    def convergence_assessment(diagnostics: Dict[str, Any]) -> Dict[str, str]:
        """
        Assess overall convergence based on diagnostics
        
        Args:
            diagnostics: Dictionary of diagnostic results
            
        Returns:
            Dictionary with convergence assessment for each parameter
        """
        assessment = {}
        
        for param_name, param_diag in diagnostics.items():
            issues = []
            
            # Check effective sample size
            if 'effective_sample_size' in param_diag:
                ess = param_diag['effective_sample_size']
                if ess < 100:
                    issues.append("Low effective sample size")
            
            # Check Geweke diagnostic
            if 'geweke' in param_diag:
                geweke = param_diag['geweke']
                if not np.isnan(geweke['p_value']) and geweke['p_value'] < 0.05:
                    issues.append("Geweke test suggests non-convergence")
            
            # Overall assessment
            if len(issues) == 0:
                assessment[param_name] = "Good"
            elif len(issues) == 1:
                assessment[param_name] = f"Warning: {issues[0]}"
            else:
                assessment[param_name] = f"Poor: {'; '.join(issues)}"
        
        return assessment