"""
MCMC sampling algorithms for Bayesian estimation

Implements TOL's MCMC framework including:
- <PERSON> sampler with block updates
- Metropolis-Hastings algorithm
- ARMS (Adaptive Rejection Metropolis Sampling)
- Constrained sampling methods

Based on TOL's BysMcmc and gibbssampler.cpp implementations.
"""

from .gibbs import GibbsSampler, BlockSampler
from .metropolis import MetropolisHastings, AdaptiveMetropolis
from .arms import ARMS, AdaptiveRejectionSampler
from .diagnostics import MCMCDiagnostics

__all__ = [
    'GibbsSampler',
    'BlockSampler', 
    'MetropolisHastings',
    'AdaptiveMetropolis',
    'ARMS',
    'AdaptiveRejectionSampler',
    'MCMCDiagnostics'
]