"""
Metropolis-Hastings algorithm implementation

Implements TOL's Metropolis-Hastings sampling framework with:
- Standard random walk Metropolis
- Adaptive Metropolis with covariance adaptation
- Multiple proposal distributions
- Constrained parameter spaces

Based on TOL's MCMC framework and optimization for complex posteriors.
"""

import numpy as np
import numpy.ma as ma
from typing import Dict, List, Optional, Union, Callable, Tuple, Any
from abc import ABC, abstractmethod
import warnings
from scipy import stats, linalg

from ..core import BayesianModel, MCMCConfig, ParameterStore


class ProposalDistribution(ABC):
    """
    Abstract base class for proposal distributions in Metropolis-Hastings
    """
    
    @abstractmethod
    def propose(self, current_state: np.ndarray) -> np.ndarray:
        """
        Generate proposal given current state
        
        Args:
            current_state: Current parameter values
            
        Returns:
            Proposed parameter values
        """
        pass
    
    @abstractmethod
    def log_density_ratio(self, proposal: np.ndarray, current: np.ndarray) -> float:
        """
        Compute log ratio of proposal densities: log(q(current|proposal)/q(proposal|current))
        
        For symmetric proposals, this returns 0.
        
        Args:
            proposal: Proposed parameter values
            current: Current parameter values
            
        Returns:
            Log density ratio
        """
        pass
    
    @abstractmethod
    def adapt(self, samples: np.ndarray, acceptance_rate: float) -> None:
        """
        Adapt proposal distribution based on recent samples
        
        Args:
            samples: Recent parameter samples
            acceptance_rate: Recent acceptance rate
        """
        pass


class RandomWalkProposal(ProposalDistribution):
    """
    Random walk proposal distribution
    
    Proposes: θ* = θ + ε where ε ~ N(0, Σ)
    This is a symmetric proposal (log_density_ratio = 0).
    """
    
    def __init__(self, covariance: np.ndarray, adaptive: bool = True):
        """
        Initialize random walk proposal
        
        Args:
            covariance: Proposal covariance matrix
            adaptive: Whether to adapt covariance during sampling
        """
        self.covariance = np.array(covariance)
        self.adaptive = adaptive
        self.target_acceptance = 0.44  # Optimal for multivariate case
        self.adaptation_window = 100
        
        # Validate covariance matrix
        if self.covariance.ndim == 0:
            # Scalar case
            self.covariance = np.array([[self.covariance]])
        elif self.covariance.ndim == 1:
            # Diagonal case
            self.covariance = np.diag(self.covariance)
        
        # Check positive definiteness
        try:
            self.chol_cov = linalg.cholesky(self.covariance, lower=True)
        except linalg.LinAlgError:
            warnings.warn("Covariance matrix not positive definite - using regularized version")
            self.covariance += 1e-6 * np.eye(self.covariance.shape[0])
            self.chol_cov = linalg.cholesky(self.covariance, lower=True)
    
    def propose(self, current_state: np.ndarray) -> np.ndarray:
        """Generate random walk proposal"""
        current_state = np.array(current_state)
        
        # Generate random increment
        if current_state.ndim == 0 or len(current_state) == 1:
            # Scalar case
            increment = np.random.normal(0, np.sqrt(self.covariance[0, 0]))
            return current_state + increment
        else:
            # Multivariate case
            standard_normal = np.random.standard_normal(len(current_state))
            increment = self.chol_cov @ standard_normal
            return current_state + increment
    
    def log_density_ratio(self, proposal: np.ndarray, current: np.ndarray) -> float:
        """Random walk is symmetric, so ratio is always 1 (log ratio = 0)"""
        return 0.0
    
    def adapt(self, samples: np.ndarray, acceptance_rate: float) -> None:
        """Adapt proposal covariance based on acceptance rate and sample covariance"""
        if not self.adaptive or len(samples) < 20:
            return
        
        # Scale adaptation based on acceptance rate
        if acceptance_rate < self.target_acceptance - 0.1:
            # Too low acceptance - decrease step size
            scale_factor = 0.9
        elif acceptance_rate > self.target_acceptance + 0.1:
            # Too high acceptance - increase step size
            scale_factor = 1.1
        else:
            scale_factor = 1.0
        
        # Update covariance with scaling
        self.covariance *= scale_factor
        
        # Also adapt to sample covariance (with regularization)
        if len(samples) >= self.adaptation_window:
            recent_samples = samples[-self.adaptation_window:]
            sample_cov = np.cov(recent_samples.T, ddof=1)
            
            if sample_cov.ndim == 0:
                sample_cov = np.array([[sample_cov]])
            
            # Blend with current covariance
            blend_weight = 0.1
            self.covariance = (1 - blend_weight) * self.covariance + blend_weight * sample_cov
            
            # Ensure positive definiteness
            eigenvals, eigenvecs = linalg.eigh(self.covariance)
            eigenvals = np.maximum(eigenvals, 1e-8)
            self.covariance = eigenvecs @ np.diag(eigenvals) @ eigenvecs.T
        
        # Update Cholesky decomposition
        try:
            self.chol_cov = linalg.cholesky(self.covariance, lower=True)
        except linalg.LinAlgError:
            # Regularize if needed
            self.covariance += 1e-6 * np.eye(self.covariance.shape[0])
            self.chol_cov = linalg.cholesky(self.covariance, lower=True)


class IndependenceProposal(ProposalDistribution):
    """
    Independence proposal distribution
    
    Proposes from a fixed distribution independent of current state.
    Often used with approximate posterior as proposal.
    """
    
    def __init__(self, proposal_mean: np.ndarray, proposal_cov: np.ndarray):
        """
        Initialize independence proposal
        
        Args:
            proposal_mean: Mean of proposal distribution
            proposal_cov: Covariance of proposal distribution
        """
        self.mean = np.array(proposal_mean)
        self.covariance = np.array(proposal_cov)
        
        # Validate inputs
        if self.covariance.ndim == 0:
            self.covariance = np.array([[self.covariance]])
        elif self.covariance.ndim == 1:
            self.covariance = np.diag(self.covariance)
        
        try:
            self.precision = linalg.inv(self.covariance)
            self.log_det_cov = np.log(linalg.det(self.covariance))
        except linalg.LinAlgError:
            raise ValueError("Proposal covariance must be positive definite")
    
    def propose(self, current_state: np.ndarray) -> np.ndarray:
        """Generate independence proposal"""
        if len(self.mean) == 1:
            return np.random.normal(self.mean[0], np.sqrt(self.covariance[0, 0]))
        else:
            return np.random.multivariate_normal(self.mean, self.covariance)
    
    def log_density_ratio(self, proposal: np.ndarray, current: np.ndarray) -> float:
        """Compute log ratio for independence proposal"""
        # q(current|proposal) / q(proposal|current) = q(current) / q(proposal)
        log_q_current = self._log_density(current)
        log_q_proposal = self._log_density(proposal)
        return log_q_current - log_q_proposal
    
    def _log_density(self, x: np.ndarray) -> float:
        """Evaluate log-density of proposal distribution"""
        x = np.array(x)
        diff = x - self.mean
        
        if len(self.mean) == 1:
            # Scalar case
            log_density = -0.5 * diff**2 / self.covariance[0, 0]
            log_density -= 0.5 * np.log(2 * np.pi * self.covariance[0, 0])
        else:
            # Multivariate case
            quad_form = diff @ self.precision @ diff
            log_density = -0.5 * quad_form
            log_density -= 0.5 * len(self.mean) * np.log(2 * np.pi)
            log_density -= 0.5 * self.log_det_cov
        
        return log_density
    
    def adapt(self, samples: np.ndarray, acceptance_rate: float) -> None:
        """Independence proposal typically doesn't adapt"""
        pass


class MetropolisHastings:
    """
    Metropolis-Hastings sampler for Bayesian models
    
    Implements the general Metropolis-Hastings algorithm with
    support for different proposal distributions and constraints.
    """
    
    def __init__(self, model: BayesianModel, proposal: ProposalDistribution,
                 config: Optional[MCMCConfig] = None):
        """
        Initialize Metropolis-Hastings sampler
        
        Args:
            model: Bayesian model to sample from
            proposal: Proposal distribution
            config: MCMC configuration
        """
        self.model = model
        self.proposal = proposal
        self.config = config or MCMCConfig()
        
        # Initialize parameter storage
        param_names = self.model.get_parameter_names()
        self.parameter_store = ParameterStore(param_names)
        
        # Sampling state
        self.current_iteration = 0
        self.is_burning_in = True
        self.samples_collected = 0
        self.n_accepted = 0
        self.n_proposed = 0
        
        # Current state
        self.current_log_posterior = -np.inf
        
        # Random state
        if self.config.random_seed is not None:
            np.random.seed(self.config.random_seed)
    
    def initialize_parameters(self, start_values: Optional[Dict[str, np.ndarray]] = None) -> None:
        """Initialize starting parameter values"""
        if start_values is None:
            start_values = {}
        
        # Initialize from priors or provided values
        initial_params = {}
        for param_name in self.model.get_parameter_names():
            if param_name in start_values:
                initial_params[param_name] = np.array(start_values[param_name])
            else:
                # Sample from prior
                prior = self.model.priors[param_name]
                initial_params[param_name] = prior.sample(1)
        
        # Validate initial parameters
        if not self.model.validate_parameters(initial_params):
            warnings.warn("Initial parameters violate constraints - attempting to find feasible values")
            initial_params = self._find_feasible_start(initial_params)
        
        # Store initial values and compute log-posterior
        self.parameter_store.set_values(initial_params)
        self.current_log_posterior = self.model.log_posterior(initial_params)
        
        if not np.isfinite(self.current_log_posterior):
            warnings.warn("Initial log-posterior is not finite")
    
    def _find_feasible_start(self, initial_params: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """Find feasible starting values"""
        # Try sampling from priors multiple times
        for attempt in range(100):
            candidate_params = {}
            for param_name in self.model.get_parameter_names():
                prior = self.model.priors[param_name]
                candidate_params[param_name] = prior.sample(1)
            
            if self.model.validate_parameters(candidate_params):
                log_post = self.model.log_posterior(candidate_params)
                if np.isfinite(log_post):
                    return candidate_params
        
        warnings.warn("Could not find feasible starting values")
        return initial_params
    
    def run_chain(self, start_values: Optional[Dict[str, np.ndarray]] = None) -> Dict[str, Any]:
        """
        Run the Metropolis-Hastings chain
        
        Args:
            start_values: Optional starting values
            
        Returns:
            Dictionary with sampling results
        """
        # Initialize parameters
        self.initialize_parameters(start_values)
        
        # Total iterations
        total_iterations = self.config.mcmc_burnin + self.config.mcmc_sample_length
        
        # Adaptation tracking
        adaptation_samples = []
        last_adaptation = 0
        
        # Run sampling loop
        for iteration in range(total_iterations):
            self.current_iteration = iteration
            
            # Update burn-in status
            if iteration >= self.config.mcmc_burnin:
                self.is_burning_in = False
            
            # Perform one Metropolis-Hastings step
            self._metropolis_step()
            
            # Store samples (after burn-in)
            if not self.is_burning_in:
                current_params = self.parameter_store.get_values()
                self.parameter_store.store_sample(current_params)
                self.samples_collected += 1
            
            # Collect samples for adaptation
            if self.is_burning_in:
                current_params = self.parameter_store.get_values()
                # Convert to vector for adaptation
                param_vector = self._params_to_vector(current_params)
                adaptation_samples.append(param_vector)
                
                # Adapt proposal periodically during burn-in
                if iteration - last_adaptation >= 100 and len(adaptation_samples) >= 50:
                    adaptation_array = np.array(adaptation_samples)
                    acceptance_rate = self.acceptance_rate
                    self.proposal.adapt(adaptation_array, acceptance_rate)
                    last_adaptation = iteration
            
            # Progress reporting
            if iteration % 1000 == 0:
                self._report_progress(iteration, total_iterations)
        
        return self._compile_results()
    
    def _metropolis_step(self) -> None:
        """Perform one Metropolis-Hastings step"""
        # Get current parameters
        current_params = self.parameter_store.get_values()
        current_vector = self._params_to_vector(current_params)
        
        # Generate proposal
        proposal_vector = self.proposal.propose(current_vector)
        proposal_params = self._vector_to_params(proposal_vector)
        
        # Check constraints
        if not self.model.validate_parameters(proposal_params):
            # Reject proposal that violates constraints
            self.n_proposed += 1
            return
        
        # Evaluate proposal log-posterior
        proposal_log_posterior = self.model.log_posterior(proposal_params)
        
        self.n_proposed += 1
        
        # Compute acceptance probability
        if np.isfinite(proposal_log_posterior):
            # Log acceptance probability
            log_alpha = proposal_log_posterior - self.current_log_posterior
            
            # Add proposal density ratio for asymmetric proposals
            log_alpha += self.proposal.log_density_ratio(proposal_vector, current_vector)
            
            # Accept or reject
            if np.log(np.random.rand()) < log_alpha:
                # Accept proposal
                self.parameter_store.set_values(proposal_params)
                self.current_log_posterior = proposal_log_posterior
                self.n_accepted += 1
        # If proposal log-posterior is not finite, automatically reject
    
    def _params_to_vector(self, params: Dict[str, np.ndarray]) -> np.ndarray:
        """Convert parameter dictionary to vector"""
        vector_parts = []
        for param_name in self.model.get_parameter_names():
            if param_name in params:
                param_val = params[param_name]
                if np.isscalar(param_val):
                    vector_parts.append(param_val)
                else:
                    vector_parts.extend(param_val.flatten())
        return np.array(vector_parts)
    
    def _vector_to_params(self, vector: np.ndarray) -> Dict[str, np.ndarray]:
        """Convert vector back to parameter dictionary"""
        params = {}
        idx = 0
        
        for param_name in self.model.get_parameter_names():
            if param_name in self.model.priors:
                # Get expected shape from prior or current value
                current_val = self.parameter_store.get_values().get(param_name)
                if current_val is not None:
                    if np.isscalar(current_val):
                        params[param_name] = vector[idx]
                        idx += 1
                    else:
                        param_size = current_val.size
                        params[param_name] = vector[idx:idx+param_size].reshape(current_val.shape)
                        idx += param_size
                else:
                    # Default to scalar
                    params[param_name] = vector[idx]
                    idx += 1
        
        return params
    
    @property
    def acceptance_rate(self) -> float:
        """Current acceptance rate"""
        if self.n_proposed > 0:
            return self.n_accepted / self.n_proposed
        return 0.0
    
    def _report_progress(self, iteration: int, total_iterations: int) -> None:
        """Report sampling progress"""
        if self.config.do_report:
            progress = (iteration + 1) / total_iterations * 100
            status = "Burn-in" if self.is_burning_in else "Sampling"
            acc_rate = self.acceptance_rate
            print(f"{status}: {iteration+1}/{total_iterations} ({progress:.1f}%) - Acceptance: {acc_rate:.3f}")
    
    def _compile_results(self) -> Dict[str, Any]:
        """Compile sampling results"""
        # Get summary statistics
        summary_stats = self.parameter_store.get_summary_statistics()
        
        results = {
            'summary_statistics': summary_stats,
            'acceptance_rate': self.acceptance_rate,
            'n_samples': self.samples_collected,
            'n_accepted': self.n_accepted,
            'n_proposed': self.n_proposed,
            'config': self.config,
            'parameter_store': self.parameter_store
        }
        
        return results
    
    def get_samples(self, param_name: str, thin: int = 1) -> np.ndarray:
        """Get samples for a specific parameter"""
        history = self.parameter_store.get_history(param_name)
        return history[::thin]
    
    def summary(self) -> str:
        """Generate summary of sampling results"""
        lines = []
        lines.append("Metropolis-Hastings Results")
        lines.append("=" * 50)
        lines.append(f"Samples collected: {self.samples_collected}")
        lines.append(f"Acceptance rate: {self.acceptance_rate:.3f}")
        lines.append(f"Burn-in: {self.config.mcmc_burnin}")
        lines.append("")
        
        # Parameter summaries
        summary_stats = self.parameter_store.get_summary_statistics()
        for param_name, stats in summary_stats.items():
            if 'mean' in stats:  # Scalar parameter
                lines.append(f"{param_name}:")
                lines.append(f"  Mean: {stats['mean']:.4f}")
                lines.append(f"  Std:  {stats['std']:.4f}")
                lines.append(f"  95% CI: [{stats['q025']:.4f}, {stats['q975']:.4f}]")
        
        return "\n".join(lines)


class AdaptiveMetropolis(MetropolisHastings):
    """
    Adaptive Metropolis algorithm
    
    Automatically adapts the proposal covariance during burn-in
    based on the sample covariance of the chain.
    """
    
    def __init__(self, model: BayesianModel, initial_cov: Optional[np.ndarray] = None,
                 config: Optional[MCMCConfig] = None):
        """
        Initialize Adaptive Metropolis sampler
        
        Args:
            model: Bayesian model
            initial_cov: Initial proposal covariance (identity if None)
            config: MCMC configuration
        """
        # Create adaptive random walk proposal
        n_params = len(model.get_parameter_names())
        if initial_cov is None:
            initial_cov = np.eye(n_params) * 0.1
        
        proposal = RandomWalkProposal(initial_cov, adaptive=True)
        
        super().__init__(model, proposal, config)
        
        # Adaptive Metropolis specific parameters
        self.adaptation_start = 100  # Start adapting after this many iterations
        self.adaptation_interval = 50  # Adapt every N iterations
        self.epsilon = 1e-6  # Regularization parameter
        self.scaling_factor = 2.38**2 / n_params  # Optimal scaling
    
    def run_chain(self, start_values: Optional[Dict[str, np.ndarray]] = None) -> Dict[str, Any]:
        """Run adaptive Metropolis chain with enhanced adaptation"""
        # Initialize parameters
        self.initialize_parameters(start_values)
        
        # Total iterations
        total_iterations = self.config.mcmc_burnin + self.config.mcmc_sample_length
        
        # Storage for adaptation
        all_samples = []
        
        # Run sampling loop
        for iteration in range(total_iterations):
            self.current_iteration = iteration
            
            # Update burn-in status
            if iteration >= self.config.mcmc_burnin:
                self.is_burning_in = False
            
            # Perform one step
            self._metropolis_step()
            
            # Store current state for adaptation
            current_params = self.parameter_store.get_values()
            param_vector = self._params_to_vector(current_params)
            all_samples.append(param_vector)
            
            # Store samples (after burn-in)
            if not self.is_burning_in:
                self.parameter_store.store_sample(current_params)
                self.samples_collected += 1
            
            # Adaptive proposal update during burn-in
            if (self.is_burning_in and 
                iteration >= self.adaptation_start and 
                iteration % self.adaptation_interval == 0):
                self._update_proposal_covariance(all_samples)
            
            # Progress reporting
            if iteration % 1000 == 0:
                self._report_progress(iteration, total_iterations)
        
        return self._compile_results()
    
    def _update_proposal_covariance(self, samples: List[np.ndarray]) -> None:
        """Update proposal covariance based on sample covariance"""
        if len(samples) < 10:
            return
        
        # Convert to array
        samples_array = np.array(samples)
        
        # Compute sample covariance
        sample_cov = np.cov(samples_array.T, ddof=1)
        
        # Handle scalar case
        if sample_cov.ndim == 0:
            sample_cov = np.array([[sample_cov]])
        
        # Scale by optimal factor
        scaled_cov = self.scaling_factor * sample_cov
        
        # Add regularization
        regularized_cov = scaled_cov + self.epsilon * np.eye(scaled_cov.shape[0])
        
        # Update proposal
        self.proposal.covariance = regularized_cov
        
        # Update Cholesky decomposition
        try:
            self.proposal.chol_cov = linalg.cholesky(regularized_cov, lower=True)
        except linalg.LinAlgError:
            # Further regularization if needed
            regularized_cov += 1e-4 * np.eye(regularized_cov.shape[0])
            self.proposal.chol_cov = linalg.cholesky(regularized_cov, lower=True)
            self.proposal.covariance = regularized_cov