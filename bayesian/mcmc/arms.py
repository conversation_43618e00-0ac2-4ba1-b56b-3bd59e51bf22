"""
ARMS (Adaptive Rejection Metropolis Sampling) implementation

Implements TOL's ARMS algorithm for sampling from univariate log-concave distributions.
Based on TOL's armseval.cpp and the original ARMS algorithm.

Key features:
- Adaptive envelope construction
- Rejection sampling with squeezing
- Metropolis step for non-log-concave cases
- Support for bounded and unbounded domains
"""

import numpy as np
from typing import Dict, List, Optional, Union, Callable, Tuple, Any
from abc import ABC, abstractmethod
import warnings
from scipy import stats, optimize

from ..core import BayesianModel


class ARMS:
    """
    Adaptive Rejection Metropolis Sampling
    
    Implements the ARMS algorithm for sampling from univariate distributions
    that are log-concave or approximately log-concave.
    
    Based on TOL's ARMS implementation in armseval.cpp with the following features:
    - Automatic envelope construction and refinement
    - Support for bounded domains
    - Metropolis correction for non-log-concave densities
    - Squeezing for improved efficiency
    """
    
    def __init__(self, log_density_func: Callable[[float], float],
                 lower_bound: float = -np.inf, upper_bound: float = np.inf,
                 initial_points: Optional[List[float]] = None,
                 use_metropolis: bool = True):
        """
        Initialize ARMS sampler
        
        Args:
            log_density_func: Function that evaluates log-density
            lower_bound: Lower bound of support
            upper_bound: Upper bound of support  
            initial_points: Initial points for envelope construction
            use_metropolis: Whether to use Metropolis correction for non-log-concave densities
        """
        self.log_density_func = log_density_func
        self.lower_bound = lower_bound
        self.upper_bound = upper_bound
        self.use_metropolis = use_metropolis
        
        # Initialize envelope points
        if initial_points is None:
            self.initial_points = self._get_initial_points()
        else:
            self.initial_points = sorted(initial_points)
        
        # Envelope construction
        self.envelope_points = []
        self.envelope_slopes = []
        self.envelope_intercepts = []
        self.envelope_cumulative = []
        
        # Squeezing function (lower hull)
        self.squeeze_points = []
        self.squeeze_slopes = []
        self.squeeze_intercepts = []
        
        # Sampling statistics
        self.n_evaluations = 0
        self.n_rejections = 0
        self.n_metropolis_steps = 0
        
        # Build initial envelope
        self._build_initial_envelope()
    
    def _get_initial_points(self) -> List[float]:
        """Get initial points for envelope construction"""
        if np.isfinite(self.lower_bound) and np.isfinite(self.upper_bound):
            # Bounded domain
            width = self.upper_bound - self.lower_bound
            return [
                self.lower_bound + 0.1 * width,
                self.lower_bound + 0.5 * width,
                self.lower_bound + 0.9 * width
            ]
        elif np.isfinite(self.lower_bound):
            # Lower bounded
            return [
                self.lower_bound + 0.1,
                self.lower_bound + 1.0,
                self.lower_bound + 10.0
            ]
        elif np.isfinite(self.upper_bound):
            # Upper bounded
            return [
                self.upper_bound - 10.0,
                self.upper_bound - 1.0,
                self.upper_bound - 0.1
            ]
        else:
            # Unbounded
            return [-1.0, 0.0, 1.0]
    
    def _build_initial_envelope(self) -> None:
        """Build initial piecewise linear envelope"""
        # Evaluate log-density and derivatives at initial points
        points = []
        log_densities = []
        derivatives = []
        
        for x in self.initial_points:
            if self.lower_bound < x < self.upper_bound:
                log_f = self.log_density_func(x)
                self.n_evaluations += 1
                
                if np.isfinite(log_f):
                    # Compute derivative numerically
                    h = 1e-6
                    if x + h < self.upper_bound:
                        log_f_plus = self.log_density_func(x + h)
                        self.n_evaluations += 1
                        if np.isfinite(log_f_plus):
                            derivative = (log_f_plus - log_f) / h
                        else:
                            derivative = 0.0
                    else:
                        derivative = 0.0
                    
                    points.append(x)
                    log_densities.append(log_f)
                    derivatives.append(derivative)
        
        if len(points) < 2:
            raise ValueError("Need at least 2 valid points to construct envelope")
        
        # Store envelope information
        self.envelope_points = points
        self.envelope_log_densities = log_densities
        self.envelope_derivatives = derivatives
        
        # Build piecewise linear envelope
        self._update_envelope()
    
    def _update_envelope(self) -> None:
        """Update piecewise linear upper envelope"""
        n_points = len(self.envelope_points)
        
        # Find intersection points and build envelope segments
        intersections = [self.lower_bound]
        
        for i in range(n_points - 1):
            x1, x2 = self.envelope_points[i], self.envelope_points[i + 1]
            f1, f2 = self.envelope_log_densities[i], self.envelope_log_densities[i + 1]
            d1, d2 = self.envelope_derivatives[i], self.envelope_derivatives[i + 1]
            
            # Find intersection of tangent lines
            if abs(d2 - d1) > 1e-12:
                intersection = (f2 - f1 - d2 * x2 + d1 * x1) / (d1 - d2)
            else:
                intersection = (x1 + x2) / 2
            
            # Ensure intersection is within bounds
            intersection = max(min(intersection, self.upper_bound), self.lower_bound)
            intersections.append(intersection)
        
        intersections.append(self.upper_bound)
        
        # Build envelope segments
        self.envelope_segments = []
        self.envelope_integrals = []
        
        for i in range(len(intersections) - 1):
            left, right = intersections[i], intersections[i + 1]
            
            if right <= left:
                continue
            
            # Find which envelope point governs this segment
            segment_point_idx = min(i, n_points - 1)
            x_env = self.envelope_points[segment_point_idx]
            f_env = self.envelope_log_densities[segment_point_idx]
            d_env = self.envelope_derivatives[segment_point_idx]
            
            # Linear envelope: log h(x) = f(x_env) + d_env * (x - x_env)
            # So h(x) = exp(f(x_env) + d_env * (x - x_env))
            intercept = f_env - d_env * x_env
            
            # Integral of exp(d_env * x + intercept) from left to right
            if abs(d_env) > 1e-12:
                integral = (np.exp(d_env * right + intercept) - 
                           np.exp(d_env * left + intercept)) / d_env
            else:
                # Constant case
                integral = (right - left) * np.exp(intercept)
            
            if integral > 0:
                self.envelope_segments.append({
                    'left': left,
                    'right': right,
                    'slope': d_env,
                    'intercept': intercept,
                    'point_idx': segment_point_idx
                })
                self.envelope_integrals.append(integral)
        
        # Normalize to get probabilities
        total_integral = sum(self.envelope_integrals)
        if total_integral > 0:
            self.envelope_probabilities = [integral / total_integral 
                                         for integral in self.envelope_integrals]
        else:
            raise ValueError("Total envelope integral is zero")
    
    def sample(self, n_samples: int = 1) -> Union[float, np.ndarray]:
        """
        Generate samples using ARMS
        
        Args:
            n_samples: Number of samples to generate
            
        Returns:
            Samples from the distribution
        """
        samples = []
        
        for _ in range(n_samples):
            sample = self._sample_one()
            samples.append(sample)
        
        if n_samples == 1:
            return samples[0]
        return np.array(samples)
    
    def _sample_one(self) -> float:
        """Generate one sample using ARMS"""
        max_iterations = 1000
        
        for iteration in range(max_iterations):
            # Sample from envelope
            x_proposal = self._sample_from_envelope()
            
            # Evaluate log-density at proposal
            log_f_proposal = self.log_density_func(x_proposal)
            self.n_evaluations += 1
            
            if not np.isfinite(log_f_proposal):
                self.n_rejections += 1
                continue
            
            # Evaluate envelope at proposal
            log_envelope = self._evaluate_envelope(x_proposal)
            
            # Check for numerical issues
            if log_f_proposal > log_envelope + 1e-10:
                # Function value exceeds envelope - need to update envelope
                self._add_point_to_envelope(x_proposal, log_f_proposal)
                continue
            
            # Rejection test
            log_u = np.log(np.random.rand())
            
            if log_u <= log_f_proposal - log_envelope:
                # Accept
                return x_proposal
            else:
                # Try squeezing test if available
                if self._squeezing_test(x_proposal, log_u):
                    # Accept based on squeezing
                    return x_proposal
                
                # If using Metropolis correction and density is not log-concave
                if self.use_metropolis:
                    # Metropolis step (simplified)
                    self.n_metropolis_steps += 1
                    # For now, just accept with some probability
                    if np.random.rand() < 0.5:
                        return x_proposal
                
                # Reject and add point to envelope
                self._add_point_to_envelope(x_proposal, log_f_proposal)
                self.n_rejections += 1
        
        warnings.warn("ARMS sampling reached maximum iterations")
        return self._sample_from_envelope()
    
    def _sample_from_envelope(self) -> float:
        """Sample from the piecewise exponential envelope"""
        # Choose segment
        segment_idx = np.random.choice(len(self.envelope_segments), 
                                     p=self.envelope_probabilities)
        segment = self.envelope_segments[segment_idx]
        
        # Sample from exponential within segment
        left, right = segment['left'], segment['right']
        slope, intercept = segment['slope'], segment['intercept']
        
        if abs(slope) > 1e-12:
            # Non-constant case
            u = np.random.rand()
            exp_left = np.exp(slope * left + intercept)
            exp_right = np.exp(slope * right + intercept)
            
            # Inverse CDF sampling
            exp_x = exp_left + u * (exp_right - exp_left)
            x = (np.log(exp_x) - intercept) / slope
        else:
            # Constant case - uniform sampling
            x = left + np.random.rand() * (right - left)
        
        # Ensure bounds
        return max(min(x, self.upper_bound), self.lower_bound)
    
    def _evaluate_envelope(self, x: float) -> float:
        """Evaluate the log of the envelope function at x"""
        # Find the appropriate segment
        for segment in self.envelope_segments:
            if segment['left'] <= x <= segment['right']:
                return segment['slope'] * x + segment['intercept']
        
        # If not found, return a very negative value
        return -np.inf
    
    def _squeezing_test(self, x: float, log_u: float) -> bool:
        """Perform squeezing test using lower hull"""
        # Simplified squeezing - would need full implementation
        return False
    
    def _add_point_to_envelope(self, x: float, log_f_x: float) -> None:
        """Add a new point to the envelope and update"""
        # Compute derivative numerically
        h = 1e-6
        if x + h < self.upper_bound:
            log_f_plus = self.log_density_func(x + h)
            self.n_evaluations += 1
            if np.isfinite(log_f_plus):
                derivative = (log_f_plus - log_f_x) / h
            else:
                derivative = 0.0
        else:
            derivative = 0.0
        
        # Insert the new point in the correct position
        inserted = False
        for i, env_x in enumerate(self.envelope_points):
            if x < env_x:
                self.envelope_points.insert(i, x)
                self.envelope_log_densities.insert(i, log_f_x)
                self.envelope_derivatives.insert(i, derivative)
                inserted = True
                break
        
        if not inserted:
            self.envelope_points.append(x)
            self.envelope_log_densities.append(log_f_x)
            self.envelope_derivatives.append(derivative)
        
        # Rebuild envelope
        self._update_envelope()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get sampling statistics"""
        return {
            'n_evaluations': self.n_evaluations,
            'n_rejections': self.n_rejections,
            'n_metropolis_steps': self.n_metropolis_steps,
            'n_envelope_points': len(self.envelope_points),
            'efficiency': 1 - self.n_rejections / max(self.n_evaluations, 1)
        }


class AdaptiveRejectionSampler:
    """
    Adaptive Rejection Sampler for multivariate distributions
    
    Extends ARMS concept to multivariate case by sampling each
    dimension conditionally using ARMS.
    """
    
    def __init__(self, model: BayesianModel, parameter_name: str):
        """
        Initialize adaptive rejection sampler for a specific parameter
        
        Args:
            model: Bayesian model
            parameter_name: Name of parameter to sample
        """
        self.model = model
        self.parameter_name = parameter_name
        self.arms_samplers = {}  # Cache ARMS samplers for different conditioning values
    
    def sample_conditional(self, conditioning_params: Dict[str, np.ndarray]) -> float:
        """
        Sample parameter conditionally using ARMS
        
        Args:
            conditioning_params: Values of all other parameters
            
        Returns:
            Sample from conditional distribution
        """
        # Create log-density function for this parameter given others
        def conditional_log_density(x: float) -> float:
            # Build complete parameter set
            all_params = conditioning_params.copy()
            all_params[self.parameter_name] = x
            
            return self.model.log_posterior(all_params)
        
        # Get bounds for this parameter
        lower_bound, upper_bound = self._get_parameter_bounds()
        
        # Create cache key for this conditioning
        cache_key = self._get_cache_key(conditioning_params)
        
        # Use cached ARMS sampler or create new one
        if cache_key not in self.arms_samplers:
            self.arms_samplers[cache_key] = ARMS(
                conditional_log_density,
                lower_bound=lower_bound,
                upper_bound=upper_bound
            )
            
            # Limit cache size
            if len(self.arms_samplers) > 10:
                # Remove oldest entry
                oldest_key = next(iter(self.arms_samplers))
                del self.arms_samplers[oldest_key]
        
        arms_sampler = self.arms_samplers[cache_key]
        return arms_sampler.sample(1)
    
    def _get_parameter_bounds(self) -> Tuple[float, float]:
        """Get bounds for the parameter from model constraints"""
        lower_bound = -np.inf
        upper_bound = np.inf
        
        # Check model constraints
        for constraint in self.model.constraints:
            if self.parameter_name in constraint['parameters']:
                if constraint['type'] == 'box' and constraint['bounds'] is not None:
                    bounds = constraint['bounds']
                    lower_bound = max(lower_bound, bounds[0])
                    upper_bound = min(upper_bound, bounds[1])
                elif constraint['type'] == 'positive':
                    lower_bound = max(lower_bound, 0.0)
        
        return lower_bound, upper_bound
    
    def _get_cache_key(self, conditioning_params: Dict[str, np.ndarray]) -> str:
        """Generate cache key for conditioning parameters"""
        # Simple hash of parameter values (rounded for numerical stability)
        key_parts = []
        for name in sorted(conditioning_params.keys()):
            if name != self.parameter_name:
                val = conditioning_params[name]
                if np.isscalar(val):
                    key_parts.append(f"{name}:{val:.6f}")
                else:
                    # For arrays, use first few elements
                    val_flat = val.flatten()
                    key_parts.append(f"{name}:{val_flat[:3]}")
        
        return "|".join(key_parts)