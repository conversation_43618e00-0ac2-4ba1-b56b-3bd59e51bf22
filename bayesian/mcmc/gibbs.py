"""
Gibbs sampler implementation for Bayesian models

Implements TOL's Gibbs sampling framework with:
- Block-based parameter updates
- Conditional distribution sampling
- Constrained parameter spaces
- Automatic conjugacy detection

Based on TOL's gibbssampler.cpp and BysMcmc::Bsr::Gibbs framework.
"""

import numpy as np
import numpy.ma as ma
from typing import Dict, List, Optional, Union, Callable, Tuple, Any
from abc import ABC, abstractmethod
import warnings
from scipy import stats, optimize
from scipy.special import logsumexp

from ..core import BayesianModel, MCMCConfig, ParameterStore
from ..priors import Prior


class ConditionalDistribution(ABC):
    """
    Abstract base class for conditional distributions in Gibbs sampling
    
    Implements TOL's FullConditional framework where each parameter
    has a conditional distribution given all other parameters.
    """
    
    def __init__(self, parameter_names: List[str], model: BayesianModel):
        """
        Initialize conditional distribution
        
        Args:
            parameter_names: Names of parameters in this conditional
            model: Bayesian model containing likelihood and priors
        """
        self.parameter_names = parameter_names
        self.model = model
        self.last_sample = None
        self.acceptance_rate = 0.0
        self.n_samples = 0
    
    @abstractmethod
    def log_conditional_density(self, x: np.ndarray, 
                               conditioning_params: Dict[str, np.ndarray]) -> float:
        """
        Evaluate log-density of conditional distribution
        
        Args:
            x: Parameter values to evaluate
            conditioning_params: Values of all other parameters
            
        Returns:
            Log-conditional density
        """
        pass
    
    @abstractmethod
    def sample_conditional(self, conditioning_params: Dict[str, np.ndarray],
                          random_state: Optional[int] = None) -> np.ndarray:
        """
        Sample from conditional distribution
        
        Args:
            conditioning_params: Values of all other parameters
            random_state: Random seed
            
        Returns:
            Sample from conditional distribution
        """
        pass
    
    def update_acceptance_rate(self, accepted: bool) -> None:
        """Update acceptance rate statistics"""
        self.n_samples += 1
        self.acceptance_rate = ((self.n_samples - 1) * self.acceptance_rate + accepted) / self.n_samples


class ConjugateConditional(ConditionalDistribution):
    """
    Conditional distribution with conjugate prior
    
    For conjugate prior-likelihood pairs, the conditional distribution
    has a known form that can be sampled directly.
    """
    
    def __init__(self, parameter_names: List[str], model: BayesianModel,
                 conditional_type: str = "normal"):
        """
        Initialize conjugate conditional
        
        Args:
            parameter_names: Names of parameters
            model: Bayesian model
            conditional_type: Type of conditional ("normal", "gamma", "beta")
        """
        super().__init__(parameter_names, model)
        self.conditional_type = conditional_type
    
    def log_conditional_density(self, x: np.ndarray,
                               conditioning_params: Dict[str, np.ndarray]) -> float:
        """Evaluate log-density using conjugate form"""
        # Build complete parameter set
        all_params = conditioning_params.copy()
        for i, name in enumerate(self.parameter_names):
            if np.isscalar(x):
                all_params[name] = x
            else:
                all_params[name] = x[i] if len(self.parameter_names) > 1 else x
        
        # Evaluate model log-posterior
        return self.model.log_posterior(all_params)
    
    def sample_conditional(self, conditioning_params: Dict[str, np.ndarray],
                          random_state: Optional[int] = None,
                          current_block_values: Optional[Dict[str, np.ndarray]] = None) -> np.ndarray:
        """Sample using conjugate form"""
        if random_state is not None:
            np.random.seed(random_state)
        
        if self.conditional_type == "normal":
            return self._sample_normal_conditional(conditioning_params)
        elif self.conditional_type == "gamma":
            return self._sample_gamma_conditional(conditioning_params)
        elif self.conditional_type == "beta":
            return self._sample_beta_conditional(conditioning_params)
        else:
            raise ValueError(f"Unknown conditional type: {self.conditional_type}")
    
    def _sample_normal_conditional(self, conditioning_params: Dict[str, np.ndarray]) -> np.ndarray:
        """Sample from normal conditional distribution"""
        # This is model-specific and would need to be implemented
        # based on the exact conjugate relationship
        
        # For now, use a generic approach
        param_name = self.parameter_names[0]
        prior = self.model.priors[param_name]
        
        # Get current parameter value for initialization
        current_val = conditioning_params.get(param_name, prior.sample(1))
        
        # For conjugate normal case, compute posterior parameters
        # This would be implemented specifically for each model type
        
        # Placeholder: sample from prior (should be posterior)
        return prior.sample(1)
    
    def _sample_gamma_conditional(self, conditioning_params: Dict[str, np.ndarray]) -> np.ndarray:
        """Sample from gamma conditional distribution"""
        param_name = self.parameter_names[0]
        prior = self.model.priors[param_name]
        
        # Placeholder implementation
        return prior.sample(1)
    
    def _sample_beta_conditional(self, conditioning_params: Dict[str, np.ndarray]) -> np.ndarray:
        """Sample from beta conditional distribution"""
        param_name = self.parameter_names[0]
        prior = self.model.priors[param_name]
        
        # Placeholder implementation
        return prior.sample(1)


class MetropolisConditional(ConditionalDistribution):
    """
    Conditional distribution using Metropolis-Hastings sampling
    
    Used when the conditional distribution is not in a standard form
    or when conjugacy is not available.
    """
    
    def __init__(self, parameter_names: List[str], model: BayesianModel,
                 proposal_cov: Optional[np.ndarray] = None,
                 adaptation: bool = True):
        """
        Initialize Metropolis conditional
        
        Args:
            parameter_names: Names of parameters
            model: Bayesian model
            proposal_cov: Proposal covariance matrix
            adaptation: Whether to adapt proposal during sampling
        """
        super().__init__(parameter_names, model)
        
        n_params = len(parameter_names)
        if proposal_cov is None:
            self.proposal_cov = np.eye(n_params) * 0.1
        else:
            self.proposal_cov = proposal_cov
        
        self.adaptation = adaptation
        self.adaptation_history = []
        self.target_acceptance = 0.44  # Optimal for multivariate normal
    
    def log_conditional_density(self, x: np.ndarray,
                               conditioning_params: Dict[str, np.ndarray]) -> float:
        """Evaluate log-density of conditional"""
        # Build complete parameter set
        all_params = conditioning_params.copy()
        if len(self.parameter_names) == 1:
            all_params[self.parameter_names[0]] = x
        else:
            for i, name in enumerate(self.parameter_names):
                all_params[name] = x[i]
        
        return self.model.log_posterior(all_params)
    
    def sample_conditional(self, conditioning_params: Dict[str, np.ndarray],
                          random_state: Optional[int] = None, 
                          current_block_values: Optional[Dict[str, np.ndarray]] = None) -> np.ndarray:
        """Sample using Metropolis-Hastings"""
        if random_state is not None:
            np.random.seed(random_state)
        
        # Get current values for parameters in this block
        current_vals = []
        for name in self.parameter_names:
            if current_block_values and name in current_block_values:
                val = current_block_values[name]
                # Ensure scalar value - handle numpy scalars properly
                try:
                    # Try to treat as array first
                    val_array = np.asarray(val)
                    if val_array.shape == ():  # Scalar
                        current_vals.append(float(val_array))
                    elif val_array.size > 0:  # Array with elements
                        current_vals.append(float(val_array.flat[0]))
                    else:  # Empty array
                        current_vals.append(0.0)
                except:
                    # Fallback for any other type
                    current_vals.append(float(val))
            else:
                # Initialize from prior
                prior = self.model.priors[name]
                sample = prior.sample(1)
                # Ensure scalar value - handle numpy scalars properly
                try:
                    sample_array = np.asarray(sample)
                    if sample_array.shape == ():  # Scalar
                        current_vals.append(float(sample_array))
                    elif sample_array.size > 0:  # Array with elements
                        current_vals.append(float(sample_array.flat[0]))
                    else:  # Empty array
                        current_vals.append(0.0)
                except:
                    current_vals.append(float(sample))
        
        current_vals = np.array(current_vals)
        
        
        # Ensure dimensions match
        if len(current_vals) != self.proposal_cov.shape[0]:
            raise ValueError(f"Dimension mismatch: current_vals has length {len(current_vals)}, "
                           f"but proposal_cov has shape {self.proposal_cov.shape}")
        
        # Propose new values
        proposal = np.random.multivariate_normal(current_vals, self.proposal_cov)
        
        # Evaluate acceptance probability
        current_log_density = self.log_conditional_density(current_vals, conditioning_params)
        proposal_log_density = self.log_conditional_density(proposal, conditioning_params)
        
        log_acceptance_prob = proposal_log_density - current_log_density
        
        # Accept or reject
        if np.log(np.random.rand()) < log_acceptance_prob:
            accepted = True
            sample = proposal
        else:
            accepted = False
            sample = current_vals
        
        # Update acceptance rate
        self.update_acceptance_rate(accepted)
        
        # Adapt proposal covariance
        if self.adaptation and self.n_samples > 50:
            self._adapt_proposal_covariance()
        
        # Return the sample array as-is for proper handling by BlockSampler
        return sample
    
    def _adapt_proposal_covariance(self) -> None:
        """Adapt proposal covariance based on acceptance rate"""
        if len(self.adaptation_history) > 20:
            recent_acceptance = np.mean(self.adaptation_history[-20:])
            
            # Scale proposal covariance
            if recent_acceptance < self.target_acceptance - 0.1:
                # Too low acceptance - decrease step size
                self.proposal_cov *= 0.9
            elif recent_acceptance > self.target_acceptance + 0.1:
                # Too high acceptance - increase step size  
                self.proposal_cov *= 1.1
        
        self.adaptation_history.append(self.acceptance_rate)


class BlockSampler:
    """
    Block sampler for grouped parameters
    
    Implements TOL's block-based MCMC where related parameters
    are updated together to improve mixing and efficiency.
    """
    
    def __init__(self, block_name: str, parameter_names: List[str],
                 conditional: ConditionalDistribution):
        """
        Initialize block sampler
        
        Args:
            block_name: Name of the parameter block
            parameter_names: Names of parameters in block
            conditional: Conditional distribution for this block
        """
        self.block_name = block_name
        self.parameter_names = parameter_names
        self.conditional = conditional
        self.acceptance_rate = 0.0
        self.n_updates = 0
    
    def update_block(self, current_params: Dict[str, np.ndarray],
                    random_state: Optional[int] = None) -> Dict[str, np.ndarray]:
        """
        Update all parameters in this block
        
        Args:
            current_params: Current values of all parameters
            random_state: Random seed
            
        Returns:
            Updated parameter values
        """
        # Get conditioning parameters (all except this block)
        conditioning_params = {name: val for name, val in current_params.items()
                             if name not in self.parameter_names}
        
        # Get current values for this block
        current_block_values = {name: current_params[name] for name in self.parameter_names
                              if name in current_params}
        
        # Sample new values for this block
        new_values = self.conditional.sample_conditional(conditioning_params, random_state, current_block_values)
        
        
        # Update parameter dictionary
        updated_params = current_params.copy()
        
        # Handle different return types from sample_conditional
        if len(self.parameter_names) == 1:
            # Single parameter case
            if hasattr(new_values, '__len__') and len(new_values) == 1:
                # Extract scalar from array
                updated_params[self.parameter_names[0]] = float(new_values[0])
            else:
                # Already scalar
                updated_params[self.parameter_names[0]] = float(new_values)
        else:
            # Multiple parameters case
            if not hasattr(new_values, '__len__'):
                # If scalar returned for multiple parameters, something is wrong
                raise ValueError(f"Expected array of {len(self.parameter_names)} values, got scalar")
            
            new_values_array = np.asarray(new_values).flatten()
            if len(new_values_array) != len(self.parameter_names):
                raise ValueError(f"Expected {len(self.parameter_names)} values, got {len(new_values_array)}")
            
            for i, name in enumerate(self.parameter_names):
                updated_params[name] = float(new_values_array[i])
        
        # Update statistics
        self.acceptance_rate = self.conditional.acceptance_rate
        self.n_updates += 1
        
        return updated_params


class GibbsSampler:
    """
    Gibbs sampler for Bayesian models
    
    Implements TOL's BysMcmc::Bsr::Gibbs framework with:
    - Block-based parameter updates
    - Automatic conditional distribution detection
    - Convergence diagnostics
    - Constrained parameter sampling
    """
    
    def __init__(self, model: BayesianModel, config: Optional[MCMCConfig] = None):
        """
        Initialize Gibbs sampler
        
        Args:
            model: Bayesian model to sample from
            config: MCMC configuration
        """
        self.model = model
        self.config = config or MCMCConfig()
        
        # Initialize parameter storage
        param_names = self.model.get_parameter_names()
        self.parameter_store = ParameterStore(param_names)
        
        # Initialize block samplers
        self.block_samplers = {}
        self._setup_block_samplers()
        
        # Sampling state
        self.current_iteration = 0
        self.is_burning_in = True
        self.samples_collected = 0
        
        # Diagnostics
        self.diagnostics = {}
        
        # Random state
        if self.config.random_seed is not None:
            np.random.seed(self.config.random_seed)
    
    def _setup_block_samplers(self) -> None:
        """Setup block samplers for each parameter block"""
        
        # If no blocks are defined, create individual parameter blocks
        if not self.model.parameter_blocks:
            for param_name in self.model.get_parameter_names():
                self.model.add_parameter_block(f"block_{param_name}", [param_name], "individual")
        
        # Create block samplers
        for block_name, block_info in self.model.parameter_blocks.items():
            param_names = block_info['parameters']
            block_type = block_info['type']
            
            # Determine if conjugate sampling is possible
            is_conjugate = self._is_conjugate_block(param_names)
            
            if is_conjugate:
                conditional = ConjugateConditional(param_names, self.model)
            else:
                conditional = MetropolisConditional(param_names, self.model)
            
            self.block_samplers[block_name] = BlockSampler(block_name, param_names, conditional)
    
    def _is_conjugate_block(self, param_names: List[str]) -> bool:
        """
        Check if a parameter block can use conjugate sampling
        
        Args:
            param_names: Names of parameters in block
            
        Returns:
            True if conjugate sampling is possible
        """
        # Simple heuristic: check if all parameters have conjugate priors
        for name in param_names:
            if name in self.model.priors:
                prior = self.model.priors[name]
                if not prior.is_conjugate():
                    return False
        return True
    
    def initialize_parameters(self, start_values: Optional[Dict[str, np.ndarray]] = None) -> None:
        """
        Initialize parameter values for sampling
        
        Args:
            start_values: Optional starting values for parameters
        """
        if start_values is None:
            start_values = {}
        
        # Initialize from priors or provided values
        initial_params = {}
        for param_name in self.model.get_parameter_names():
            if param_name in start_values:
                initial_params[param_name] = np.array(start_values[param_name])
            else:
                # Sample from prior
                prior = self.model.priors[param_name]
                initial_params[param_name] = prior.sample(1)
        
        # Validate initial parameters
        if not self.model.validate_parameters(initial_params):
            warnings.warn("Initial parameters violate constraints - attempting to find feasible values")
            initial_params = self._find_feasible_start(initial_params)
        
        # Store initial values
        self.parameter_store.set_values(initial_params)
    
    def _find_feasible_start(self, initial_params: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        Find feasible starting values that satisfy constraints
        
        Args:
            initial_params: Initial parameter values
            
        Returns:
            Feasible parameter values
        """
        # Simple approach: try sampling from priors multiple times
        for attempt in range(100):
            candidate_params = {}
            for param_name in self.model.get_parameter_names():
                prior = self.model.priors[param_name]
                candidate_params[param_name] = prior.sample(1)
            
            if self.model.validate_parameters(candidate_params):
                return candidate_params
        
        # If we can't find feasible values, return original and let sampler handle it
        warnings.warn("Could not find feasible starting values")
        return initial_params
    
    def run_chain(self, start_values: Optional[Dict[str, np.ndarray]] = None) -> Dict[str, Any]:
        """
        Run the Gibbs sampling chain
        
        Args:
            start_values: Optional starting values for parameters
            
        Returns:
            Dictionary with sampling results and diagnostics
        """
        # Initialize parameters
        self.initialize_parameters(start_values)
        
        # Total iterations including burn-in
        total_iterations = self.config.mcmc_burnin + self.config.mcmc_sample_length
        
        # Run sampling loop
        for iteration in range(total_iterations):
            self.current_iteration = iteration
            
            # Update burn-in status
            if iteration >= self.config.mcmc_burnin:
                self.is_burning_in = False
            
            # Perform one Gibbs iteration
            self._gibbs_iteration()
            
            # Store samples (after burn-in)
            if not self.is_burning_in:
                current_params = self.parameter_store.get_values()
                self.parameter_store.store_sample(current_params)
                self.samples_collected += 1
            
            # Progress reporting
            if iteration % 1000 == 0:
                self._report_progress(iteration, total_iterations)
        
        # Compute diagnostics
        self._compute_diagnostics()
        
        # Return results
        return self._compile_results()
    
    def _gibbs_iteration(self) -> None:
        """Perform one iteration of Gibbs sampling"""
        current_params = self.parameter_store.get_values()
        
        # Update each block in sequence
        for block_name, block_sampler in self.block_samplers.items():
            # Update this block
            updated_params = block_sampler.update_block(current_params)
            
            # Validate updated parameters
            if self.model.validate_parameters(updated_params):
                current_params = updated_params
            else:
                # If constraints are violated, keep current values
                warnings.warn(f"Block {block_name} update violated constraints - keeping current values")
        
        # Store updated parameters
        self.parameter_store.set_values(current_params)
    
    def _report_progress(self, iteration: int, total_iterations: int) -> None:
        """Report sampling progress"""
        if self.config.do_report:
            progress = (iteration + 1) / total_iterations * 100
            status = "Burn-in" if self.is_burning_in else "Sampling"
            print(f"{status}: {iteration+1}/{total_iterations} ({progress:.1f}%)")
            
            # Report acceptance rates
            for block_name, block_sampler in self.block_samplers.items():
                acc_rate = block_sampler.acceptance_rate
                print(f"  {block_name}: {acc_rate:.3f}")
    
    def _compute_diagnostics(self) -> None:
        """Compute convergence diagnostics"""
        from .diagnostics import MCMCDiagnostics
        
        # Get sample history
        samples = {}
        for param_name in self.model.get_parameter_names():
            history = self.parameter_store.get_history(param_name)
            if len(history) > 0:
                samples[param_name] = history
        
        # Compute diagnostics
        self.diagnostics = MCMCDiagnostics.compute_diagnostics(
            samples, self.config
        )
    
    def _compile_results(self) -> Dict[str, Any]:
        """Compile sampling results"""
        # Get summary statistics
        summary_stats = self.parameter_store.get_summary_statistics()
        
        # Get acceptance rates
        acceptance_rates = {}
        for block_name, block_sampler in self.block_samplers.items():
            acceptance_rates[block_name] = block_sampler.acceptance_rate
        
        results = {
            'summary_statistics': summary_stats,
            'diagnostics': self.diagnostics,
            'acceptance_rates': acceptance_rates,
            'n_samples': self.samples_collected,
            'config': self.config,
            'parameter_store': self.parameter_store
        }
        
        return results
    
    def get_samples(self, param_name: str, thin: int = 1) -> np.ndarray:
        """
        Get samples for a specific parameter
        
        Args:
            param_name: Name of parameter
            thin: Thinning interval
            
        Returns:
            Array of samples
        """
        history = self.parameter_store.get_history(param_name)
        return history[::thin]
    
    def get_posterior_mean(self, param_name: str) -> np.ndarray:
        """Get posterior mean for a parameter"""
        samples = self.get_samples(param_name)
        return np.mean(samples, axis=0)
    
    def get_credible_interval(self, param_name: str, alpha: float = 0.05) -> Tuple[np.ndarray, np.ndarray]:
        """
        Get credible interval for a parameter
        
        Args:
            param_name: Name of parameter
            alpha: Significance level (0.05 for 95% CI)
            
        Returns:
            Tuple of (lower_bound, upper_bound)
        """
        samples = self.get_samples(param_name)
        lower = np.quantile(samples, alpha/2, axis=0)
        upper = np.quantile(samples, 1-alpha/2, axis=0)
        return lower, upper
    
    def summary(self) -> str:
        """Generate summary of sampling results"""
        lines = []
        lines.append("Gibbs Sampling Results")
        lines.append("=" * 50)
        lines.append(f"Samples collected: {self.samples_collected}")
        lines.append(f"Burn-in: {self.config.mcmc_burnin}")
        lines.append("")
        
        # Parameter summaries
        summary_stats = self.parameter_store.get_summary_statistics()
        for param_name, stats in summary_stats.items():
            if 'mean' in stats:  # Scalar parameter
                lines.append(f"{param_name}:")
                lines.append(f"  Mean: {stats['mean']:.4f}")
                lines.append(f"  Std:  {stats['std']:.4f}")
                lines.append(f"  95% CI: [{stats['q025']:.4f}, {stats['q975']:.4f}]")
            else:  # Vector parameter
                lines.append(f"{param_name}: (vector)")
                lines.append(f"  Components: {stats['n_samples']}")
        
        lines.append("")
        
        # Acceptance rates
        lines.append("Acceptance Rates:")
        for block_name, rate in self.acceptance_rates.items():
            lines.append(f"  {block_name}: {rate:.3f}")
        
        return "\n".join(lines)