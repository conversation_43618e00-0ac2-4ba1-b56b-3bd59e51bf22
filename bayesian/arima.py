"""
Bayesian ARIMA models

Implements Bayesian estimation for ARIMA models with:
- Hierarchical priors for AR/MA coefficients and variance
- MCMC estimation via Gibbs sampling
- State-space representation with Bayesian parameter estimation
- Credible intervals and posterior inference
- Model comparison and selection

Extends the existing ARIMA framework with full Bayesian capabilities.
Based on TOL's Bayesian ARIMA implementations and BSR modeling.
"""

import numpy as np
import numpy.ma as ma
from typing import Dict, List, Optional, Union, Tuple, Any
from dataclasses import dataclass
import warnings
from scipy import stats

try:
    # Try relative imports first (when used as package)
    from ..series import Serie
    from ..arima import ARIMA, ARIMAFactor, ARIMAResults
    from ..arima.kalman import ARIMAKalmanFilter
    from .core import BayesianModel, MCMCConfig, ParameterStore
    from .priors import Prior, NormalPrior, GammaPrior, InverseGammaPrior, HierarchicalPrior
    from .mcmc import GibbsSampler, MCMCDiagnostics
except ImportError:
    # Fallback to absolute imports (when used directly)
    from series.serie import Serie
    from arima.arima_model import ARIMA, ARIMAResults
    from arima.arima_factor import ARIMAFactor
    from arima.kalman import ARIMAKalmanFilter
    from bayesian.core import BayesianModel, MCMCConfig, ParameterStore
    from bayesian.priors import Prior, NormalPrior, GammaPrior, InverseGammaPrior, HierarchicalPrior
    from bayesian.mcmc import GibbsSampler, MCMCDiagnostics


@dataclass
class BayesianARIMAResults:
    """
    Results from Bayesian ARIMA estimation
    
    Contains posterior samples, credible intervals, and model diagnostics
    """
    
    # Model specification
    factor: ARIMAFactor
    
    # Posterior samples
    ar_samples: Optional[np.ndarray] = None
    ma_samples: Optional[np.ndarray] = None
    intercept_samples: Optional[np.ndarray] = None
    variance_samples: Optional[np.ndarray] = None
    
    # Posterior summaries
    ar_posterior_mean: Optional[np.ndarray] = None
    ar_posterior_std: Optional[np.ndarray] = None
    ar_credible_intervals: Optional[Tuple[np.ndarray, np.ndarray]] = None
    
    ma_posterior_mean: Optional[np.ndarray] = None
    ma_posterior_std: Optional[np.ndarray] = None
    ma_credible_intervals: Optional[Tuple[np.ndarray, np.ndarray]] = None
    
    intercept_posterior_mean: Optional[float] = None
    intercept_posterior_std: Optional[float] = None
    intercept_credible_interval: Optional[Tuple[float, float]] = None
    
    variance_posterior_mean: Optional[float] = None
    variance_posterior_std: Optional[float] = None
    variance_credible_interval: Optional[Tuple[float, float]] = None
    
    # Model comparison
    log_marginal_likelihood: Optional[float] = None
    dic: Optional[float] = None  # Deviance Information Criterion
    waic: Optional[float] = None  # Watanabe-Akaike Information Criterion
    
    # MCMC diagnostics
    mcmc_diagnostics: Optional[Dict] = None
    effective_sample_sizes: Optional[Dict] = None
    
    # Data and fitted values
    original_series: Optional[Serie] = None
    fitted_values: Optional[Serie] = None
    posterior_predictive_samples: Optional[np.ndarray] = None
    
    # Sample information
    n_samples: int = 0
    burn_in: int = 0
    
    def summary(self, alpha: float = 0.05) -> str:
        """
        Generate summary of Bayesian ARIMA results
        
        Args:
            alpha: Significance level for credible intervals
            
        Returns:
            Formatted summary string
        """
        ci_level = int((1 - alpha) * 100)
        
        lines = []
        lines.append("Bayesian ARIMA Results")
        lines.append("=" * 50)
        lines.append(f"Model: {self.factor.get_model_string()}")
        lines.append(f"Posterior samples: {self.n_samples}")
        lines.append(f"Burn-in: {self.burn_in}")
        lines.append("")
        
        # AR parameters
        if self.ar_posterior_mean is not None and len(self.ar_posterior_mean) > 0:
            lines.append("AR Parameters:")
            lines.append(f"{'Parameter':>12s} {'Mean':>10s} {'Std':>10s} {f'{ci_level}% CI':>20s}")
            lines.append("-" * 60)
            
            for i, (mean, std) in enumerate(zip(self.ar_posterior_mean, self.ar_posterior_std)):
                if self.ar_credible_intervals is not None:
                    lower, upper = self.ar_credible_intervals[0][i], self.ar_credible_intervals[1][i]
                    ci_str = f"[{lower:.4f}, {upper:.4f}]"
                else:
                    ci_str = "N/A"
                
                lines.append(f"{'ar' + str(i+1):>12s} {mean:10.4f} {std:10.4f} {ci_str:>20s}")
        
        # MA parameters
        if self.ma_posterior_mean is not None and len(self.ma_posterior_mean) > 0:
            lines.append("")
            lines.append("MA Parameters:")
            lines.append(f"{'Parameter':>12s} {'Mean':>10s} {'Std':>10s} {f'{ci_level}% CI':>20s}")
            lines.append("-" * 60)
            
            for i, (mean, std) in enumerate(zip(self.ma_posterior_mean, self.ma_posterior_std)):
                if self.ma_credible_intervals is not None:
                    lower, upper = self.ma_credible_intervals[0][i], self.ma_credible_intervals[1][i]
                    ci_str = f"[{lower:.4f}, {upper:.4f}]"
                else:
                    ci_str = "N/A"
                
                lines.append(f"{'ma' + str(i+1):>12s} {mean:10.4f} {std:10.4f} {ci_str:>20s}")
        
        # Intercept
        if self.intercept_posterior_mean is not None:
            lines.append("")
            lines.append("Intercept/Drift:")
            lines.append(f"{'Parameter':>12s} {'Mean':>10s} {'Std':>10s} {f'{ci_level}% CI':>20s}")
            lines.append("-" * 60)
            
            if self.intercept_credible_interval is not None:
                lower, upper = self.intercept_credible_interval
                ci_str = f"[{lower:.4f}, {upper:.4f}]"
            else:
                ci_str = "N/A"
            
            param_name = "drift" if not self.factor.is_stationary else "intercept"
            lines.append(f"{param_name:>12s} {self.intercept_posterior_mean:10.4f} "
                        f"{self.intercept_posterior_std:10.4f} {ci_str:>20s}")
        
        # Variance
        if self.variance_posterior_mean is not None:
            lines.append("")
            lines.append("Error Variance:")
            lines.append(f"{'Parameter':>12s} {'Mean':>10s} {'Std':>10s} {f'{ci_level}% CI':>20s}")
            lines.append("-" * 60)
            
            if self.variance_credible_interval is not None:
                lower, upper = self.variance_credible_interval
                ci_str = f"[{lower:.4f}, {upper:.4f}]"
            else:
                ci_str = "N/A"
            
            lines.append(f"{'sigma2':>12s} {self.variance_posterior_mean:10.4f} "
                        f"{self.variance_posterior_std:10.4f} {ci_str:>20s}")
        
        # Model comparison criteria
        lines.append("")
        lines.append("Model Comparison:")
        if self.log_marginal_likelihood is not None:
            lines.append(f"Log Marginal Likelihood: {self.log_marginal_likelihood:.4f}")
        if self.dic is not None:
            lines.append(f"DIC: {self.dic:.4f}")
        if self.waic is not None:
            lines.append(f"WAIC: {self.waic:.4f}")
        
        # MCMC diagnostics
        if self.effective_sample_sizes is not None:
            lines.append("")
            lines.append("MCMC Diagnostics:")
            lines.append("Effective Sample Sizes:")
            for param_name, ess in self.effective_sample_sizes.items():
                lines.append(f"  {param_name}: {ess:.1f}")
        
        return "\n".join(lines)


class BayesianARIMAModel(BayesianModel):
    """
    Bayesian ARIMA model class
    
    Implements Bayesian estimation for ARIMA models using:
    - Hierarchical priors for parameters
    - State-space representation for likelihood
    - Gibbs sampling for posterior inference
    """
    
    def __init__(self, factor: ARIMAFactor, priors: Optional[Dict[str, Prior]] = None,
                 config: Optional[MCMCConfig] = None):
        """
        Initialize Bayesian ARIMA model
        
        Args:
            factor: ARIMA model specification
            priors: Dictionary of prior distributions
            config: MCMC configuration
        """
        super().__init__(config)
        
        self.factor = factor
        self.kalman_filter = ARIMAKalmanFilter(factor)
        
        # Initialize with default priors if not provided
        if priors is None:
            priors = self._default_priors()
        
        # Add priors to model
        for param_name, prior in priors.items():
            self.add_prior(param_name, prior)
        
        # Set up parameter blocks for efficient sampling
        self._setup_parameter_blocks()
        
        # Data storage
        self.data = None
        self.differenced_data = None
    
    def _default_priors(self) -> Dict[str, Prior]:
        """Set up default hierarchical priors for ARIMA parameters"""
        priors = {}
        
        # AR parameters: Normal priors with hierarchical variance
        ar_lags, ma_lags = self.factor.get_polynomial_orders()
        
        if len(ar_lags) > 0:
            # AR coefficients: Normal(0, σ²_ar) with σ²_ar ~ InverseGamma(3, 2)
            ar_variance_prior = InverseGammaPrior(shape=3.0, scale=2.0, name="AR_variance")
            # Don't add hyperprior as a parameter - it's just used for hierarchical structure
            
            for i, lag in enumerate(ar_lags):
                # For now, use simple normal priors instead of hierarchical
                # TODO: Implement proper hierarchical sampling
                ar_prior = NormalPrior(0.0, 0.5, name=f"AR_{lag}")
                priors[f'ar_{i}'] = ar_prior
        
        if len(ma_lags) > 0:
            # MA coefficients: Similar structure
            ma_variance_prior = InverseGammaPrior(shape=3.0, scale=2.0, name="MA_variance")
            # Don't add hyperprior as a parameter
            
            for i, lag in enumerate(ma_lags):
                # For now, use simple normal priors instead of hierarchical
                # TODO: Implement proper hierarchical sampling
                ma_prior = NormalPrior(0.0, 0.5, name=f"MA_{lag}")
                priors[f'ma_{i}'] = ma_prior
        
        # Intercept/drift: Normal prior with automatic scaling
        if not self.factor.is_stationary:
            # For non-stationary models, this is a drift term
            priors['intercept'] = NormalPrior(0.0, 1.0, name="drift", auto_scale=True)
        else:
            # For stationary models, this is an intercept
            priors['intercept'] = NormalPrior(0.0, 10.0, name="intercept", auto_scale=True)
        
        # Error variance: InverseGamma prior
        priors['sigma2'] = InverseGammaPrior(shape=3.0, scale=2.0, name="error_variance")
        
        return priors
    
    def _setup_parameter_blocks(self) -> None:
        """Set up parameter blocks for efficient Gibbs sampling"""
        # AR parameters block
        ar_lags, ma_lags = self.factor.get_polynomial_orders()
        
        if len(ar_lags) > 0:
            ar_param_names = [f'ar_{i}' for i in range(len(ar_lags))]
            self.add_parameter_block("ar_block", ar_param_names, "ar_coefficients")
        
        # MA parameters block
        if len(ma_lags) > 0:
            ma_param_names = [f'ma_{i}' for i in range(len(ma_lags))]
            self.add_parameter_block("ma_block", ma_param_names, "ma_coefficients")
        
        # Variance parameters block
        variance_params = ['sigma2']
        if 'ar_variance' in self.priors:
            variance_params.append('ar_variance')
        if 'ma_variance' in self.priors:
            variance_params.append('ma_variance')
        
        self.add_parameter_block("variance_block", variance_params, "variance")
        
        # Intercept block
        if 'intercept' in self.priors:
            self.add_parameter_block("intercept_block", ["intercept"], "intercept")
    
    def fit_data(self, serie: Serie) -> None:
        """
        Prepare data for Bayesian estimation
        
        Args:
            serie: Time series data to fit
        """
        self.data = serie
        
        # Apply differencing if needed
        if self.factor.num_diff_operations > 0:
            # Apply regular differencing
            differenced = serie
            for _ in range(self.factor.diff_order):
                # Simple first differencing
                data_array = differenced._data.to_numpy()
                diff_data = np.diff(data_array)
                # Create new serie with differenced data
                # This is simplified - full implementation would handle dates properly
                differenced = Serie(data=diff_data)
            
            # Apply seasonal differencing if needed
            if self.factor.seasonal_diff_order > 0:
                for _ in range(self.factor.seasonal_diff_order):
                    data_array = differenced._data.to_numpy()
                    s = self.factor.season_length
                    if len(data_array) > s:
                        seasonal_diff = data_array[s:] - data_array[:-s]
                        differenced = Serie(data=seasonal_diff)
            
            self.differenced_data = differenced
        else:
            self.differenced_data = serie
        
        # Auto-scale priors based on data characteristics
        self._auto_scale_priors()
    
    def _auto_scale_priors(self) -> None:
        """Automatically scale priors based on data characteristics"""
        if self.differenced_data is None:
            return
        
        data_array = self.differenced_data._data.to_numpy()
        data_array = data_array[~np.isnan(data_array)]
        
        if len(data_array) == 0:
            return
        
        # Data scale
        data_std = np.std(data_array)
        data_mean = np.mean(data_array)
        
        # Scale intercept prior
        if 'intercept' in self.priors:
            intercept_prior = self.priors['intercept']
            if hasattr(intercept_prior, 'auto_scale_variance'):
                intercept_prior.auto_scale_variance(abs(data_mean))
    
    def log_likelihood(self, parameters: Dict[str, np.ndarray]) -> float:
        """
        Evaluate log-likelihood using Kalman filter
        
        Args:
            parameters: Dictionary of parameter values
            
        Returns:
            Log-likelihood value
        """
        if self.differenced_data is None:
            return -np.inf
        
        try:
            # Extract parameters
            ar_params = self._extract_ar_parameters(parameters)
            ma_params = self._extract_ma_parameters(parameters)
            sigma2 = parameters.get('sigma2', 1.0)
            intercept = parameters.get('intercept')
            
            # Get data
            data = self.differenced_data._data.to_numpy()
            
            # Compute likelihood using Kalman filter
            log_lik, _ = self.kalman_filter.compute_likelihood(
                data, ar_params, ma_params, sigma2, intercept
            )
            
            return log_lik
            
        except Exception as e:
            warnings.warn(f"Error computing log-likelihood: {str(e)}")
            return -np.inf
    
    def log_prior(self, parameters: Dict[str, np.ndarray]) -> float:
        """
        Evaluate log-prior for all parameters
        
        Args:
            parameters: Dictionary of parameter values
            
        Returns:
            Log-prior value
        """
        log_prior_total = 0.0
        
        try:
            for param_name, param_value in parameters.items():
                if param_name in self.priors:
                    prior = self.priors[param_name]
                    log_prior_val = prior.log_density(param_value)
                    
                    if not np.isfinite(log_prior_val):
                        return -np.inf
                    
                    log_prior_total += log_prior_val
            
            return log_prior_total
            
        except Exception as e:
            warnings.warn(f"Error computing log-prior: {str(e)}")
            return -np.inf
    
    def _extract_ar_parameters(self, parameters: Dict[str, np.ndarray]) -> np.ndarray:
        """Extract AR parameters in correct order"""
        ar_lags, _ = self.factor.get_polynomial_orders()
        ar_params = []
        
        for i in range(len(ar_lags)):
            param_name = f'ar_{i}'
            if param_name in parameters:
                ar_params.append(parameters[param_name])
        
        return np.array(ar_params)
    
    def _extract_ma_parameters(self, parameters: Dict[str, np.ndarray]) -> np.ndarray:
        """Extract MA parameters in correct order"""
        _, ma_lags = self.factor.get_polynomial_orders()
        ma_params = []
        
        for i in range(len(ma_lags)):
            param_name = f'ma_{i}'
            if param_name in parameters:
                ma_params.append(parameters[param_name])
        
        return np.array(ma_params)


class BayesianARIMA:
    """
    Main Bayesian ARIMA class
    
    Provides high-level interface for Bayesian ARIMA modeling
    """
    
    def __init__(self, factor: ARIMAFactor, priors: Optional[Dict[str, Prior]] = None,
                 config: Optional[MCMCConfig] = None):
        """
        Initialize Bayesian ARIMA
        
        Args:
            factor: ARIMA model specification
            priors: Prior distributions
            config: MCMC configuration
        """
        self.factor = factor
        self.model = BayesianARIMAModel(factor, priors, config)
        self.results: Optional[BayesianARIMAResults] = None
    
    def fit(self, serie: Serie, **kwargs) -> BayesianARIMAResults:
        """
        Fit Bayesian ARIMA model
        
        Args:
            serie: Time series data
            **kwargs: Additional arguments for MCMC sampling
            
        Returns:
            Bayesian ARIMA results
        """
        # Prepare data
        self.model.fit_data(serie)
        
        # Create Gibbs sampler
        sampler = GibbsSampler(self.model, self.model.config)
        
        # Run MCMC
        mcmc_results = sampler.run_chain()
        
        # Compile results
        self.results = self._compile_results(mcmc_results, serie)
        
        return self.results
    
    def _compile_results(self, mcmc_results: Dict[str, Any], 
                        original_serie: Serie) -> BayesianARIMAResults:
        """Compile MCMC results into BayesianARIMAResults"""
        
        parameter_store = mcmc_results['parameter_store']
        
        # Extract parameter samples
        ar_samples = self._extract_parameter_samples(parameter_store, 'ar')
        ma_samples = self._extract_parameter_samples(parameter_store, 'ma')
        
        # Extract other samples
        intercept_samples = None
        if 'intercept' in parameter_store.parameter_names:
            intercept_history = parameter_store.get_history('intercept')
            if len(intercept_history) > 0:
                intercept_samples = intercept_history
        
        variance_samples = None
        if 'sigma2' in parameter_store.parameter_names:
            variance_history = parameter_store.get_history('sigma2')
            if len(variance_history) > 0:
                variance_samples = variance_history
        
        # Compute posterior summaries
        results = BayesianARIMAResults(
            factor=self.factor,
            ar_samples=ar_samples,
            ma_samples=ma_samples,
            intercept_samples=intercept_samples,
            variance_samples=variance_samples,
            original_series=original_serie,
            n_samples=mcmc_results['n_samples'],
            burn_in=self.model.config.mcmc_burnin,
            mcmc_diagnostics=mcmc_results.get('diagnostics'),
            effective_sample_sizes=self._compute_effective_sample_sizes(mcmc_results)
        )
        
        # Compute posterior summaries
        self._compute_posterior_summaries(results)
        
        # Compute model comparison criteria
        self._compute_model_comparison(results, mcmc_results)
        
        return results
    
    def _extract_parameter_samples(self, parameter_store: ParameterStore, 
                                 param_type: str) -> Optional[np.ndarray]:
        """Extract samples for parameter type (ar, ma)"""
        samples = []
        
        if param_type == 'ar':
            ar_lags, _ = self.factor.get_polynomial_orders()
            for i in range(len(ar_lags)):
                param_name = f'ar_{i}'
                if param_name in parameter_store.parameter_names:
                    history = parameter_store.get_history(param_name)
                    if len(history) > 0:
                        samples.append(history)
        
        elif param_type == 'ma':
            _, ma_lags = self.factor.get_polynomial_orders()
            for i in range(len(ma_lags)):
                param_name = f'ma_{i}'
                if param_name in parameter_store.parameter_names:
                    history = parameter_store.get_history(param_name)
                    if len(history) > 0:
                        samples.append(history)
        
        if len(samples) > 0:
            return np.column_stack(samples)
        return None
    
    def _compute_posterior_summaries(self, results: BayesianARIMAResults) -> None:
        """Compute posterior means, standard deviations, and credible intervals"""
        alpha = 0.05  # 95% credible intervals
        
        # AR parameters
        if results.ar_samples is not None:
            results.ar_posterior_mean = np.mean(results.ar_samples, axis=0)
            results.ar_posterior_std = np.std(results.ar_samples, axis=0)
            results.ar_credible_intervals = (
                np.quantile(results.ar_samples, alpha/2, axis=0),
                np.quantile(results.ar_samples, 1-alpha/2, axis=0)
            )
        
        # MA parameters
        if results.ma_samples is not None:
            results.ma_posterior_mean = np.mean(results.ma_samples, axis=0)
            results.ma_posterior_std = np.std(results.ma_samples, axis=0)
            results.ma_credible_intervals = (
                np.quantile(results.ma_samples, alpha/2, axis=0),
                np.quantile(results.ma_samples, 1-alpha/2, axis=0)
            )
        
        # Intercept
        if results.intercept_samples is not None:
            results.intercept_posterior_mean = np.mean(results.intercept_samples)
            results.intercept_posterior_std = np.std(results.intercept_samples)
            results.intercept_credible_interval = (
                np.quantile(results.intercept_samples, alpha/2),
                np.quantile(results.intercept_samples, 1-alpha/2)
            )
        
        # Variance
        if results.variance_samples is not None:
            results.variance_posterior_mean = np.mean(results.variance_samples)
            results.variance_posterior_std = np.std(results.variance_samples)
            results.variance_credible_interval = (
                np.quantile(results.variance_samples, alpha/2),
                np.quantile(results.variance_samples, 1-alpha/2)
            )
    
    def _compute_effective_sample_sizes(self, mcmc_results: Dict[str, Any]) -> Dict[str, float]:
        """Compute effective sample sizes for all parameters"""
        ess_dict = {}
        
        if 'diagnostics' in mcmc_results:
            diagnostics = mcmc_results['diagnostics']
            for param_name, param_diag in diagnostics.items():
                if 'effective_sample_size' in param_diag:
                    ess_dict[param_name] = param_diag['effective_sample_size']
        
        return ess_dict
    
    def _compute_model_comparison(self, results: BayesianARIMAResults, 
                                mcmc_results: Dict[str, Any]) -> None:
        """Compute model comparison criteria"""
        # This is a simplified implementation
        # Full implementation would compute DIC, WAIC, and marginal likelihood
        
        # Placeholder for DIC computation
        if results.variance_samples is not None:
            # Simple approximation - would need proper implementation
            mean_deviance = -2 * np.mean([
                self.model.log_likelihood({'sigma2': var}) 
                for var in results.variance_samples[:100]  # Sample subset
            ])
            results.dic = mean_deviance  # Simplified
    
    def forecast(self, steps: int, alpha: float = 0.05) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Generate Bayesian forecasts with credible intervals
        
        Args:
            steps: Number of forecast steps
            alpha: Significance level for credible intervals
            
        Returns:
            Tuple of (forecast_mean, lower_bounds, upper_bounds)
        """
        if self.results is None:
            raise ValueError("Model must be fitted before forecasting")
        
        # Generate forecasts from posterior samples
        n_forecast_samples = min(200, len(self.results.variance_samples) if self.results.variance_samples is not None else 100)
        forecast_samples = []
        
        for i in range(n_forecast_samples):
            # Sample parameters from posterior
            if self.results.ar_samples is not None:
                ar_params = self.results.ar_samples[i]
            else:
                ar_params = np.array([])
            
            if self.results.ma_samples is not None:
                ma_params = self.results.ma_samples[i]
            else:
                ma_params = np.array([])
            
            if self.results.variance_samples is not None:
                sigma2 = self.results.variance_samples[i]
            else:
                sigma2 = 1.0
            
            if self.results.intercept_samples is not None:
                intercept = self.results.intercept_samples[i]
            else:
                intercept = None
            
            # Generate forecast using Kalman filter
            data = self.model.differenced_data._data.to_numpy()
            forecasts, _ = self.model.kalman_filter.forecast_kalman(
                data, ar_params, ma_params, sigma2, intercept, steps
            )
            
            forecast_samples.append(forecasts)
        
        # Compute summary statistics
        forecast_samples = np.array(forecast_samples)
        forecast_mean = np.mean(forecast_samples, axis=0)
        lower_bounds = np.quantile(forecast_samples, alpha/2, axis=0)
        upper_bounds = np.quantile(forecast_samples, 1-alpha/2, axis=0)
        
        return forecast_mean, lower_bounds, upper_bounds
    
    def summary(self) -> str:
        """Get summary of fitted model"""
        if self.results is None:
            return f"Bayesian ARIMA{self.factor.order} (not fitted)"
        
        return self.results.summary()
    
    def __str__(self) -> str:
        return f"Bayesian ARIMA{self.factor.order}"
    
    def __repr__(self) -> str:
        fitted_status = "fitted" if self.results is not None else "not fitted"
        return f"BayesianARIMA(factor={self.factor}, {fitted_status})"