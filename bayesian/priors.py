"""
Prior distributions for Bayesian models

Implements TOL's prior specification system with:
- Standard conjugate priors (Normal, Gamma, InverseGamma)
- Hierarchical priors for multi-level models
- Automatic hyperparameter scaling and updates
- Support for constrained parameter spaces

Based on TOL's BysMcmc prior handling and BSR model specifications.
"""

import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from scipy import stats
import warnings

from .core import Prior


class NormalPrior(Prior):
    """
    Normal prior distribution N(μ, σ²)
    
    Conjugate prior for normal likelihood with known variance.
    Implements TOL's automatic scaling based on data characteristics.
    
    Features:
    - Automatic variance scaling for parameter magnitude
    - Conjugate updates for normal-normal models
    - Support for multivariate normal priors
    """
    
    def __init__(self, mean: Union[float, np.ndarray] = 0.0, 
                 variance: Union[float, np.ndarray] = 1.0,
                 name: str = "Normal", auto_scale: bool = True):
        """
        Initialize Normal prior
        
        Args:
            mean: Prior mean (scalar or array)
            variance: Prior variance (scalar or array for diagonal covariance)
            name: Descriptive name
            auto_scale: Whether to automatically scale variance based on data
        """
        super().__init__(name)
        
        self.mean = np.array(mean)
        self.variance = np.array(variance)
        self.auto_scale = auto_scale
        # TODO: Implement proper conjugate sampling for multi-parameter blocks
        self._is_conjugate = False  # Disabled until conjugate sampling is properly implemented
        
        # Store original hyperparameters
        self._hyperparameters = {
            'mean': self.mean.copy(),
            'variance': self.variance.copy(),
            'precision': 1.0 / self.variance,
            'auto_scale': auto_scale
        }
        
        # Validate inputs
        if np.any(self.variance <= 0):
            raise ValueError("Variance must be positive")
    
    def log_density(self, x: np.ndarray) -> float:
        """
        Evaluate log-density of normal prior
        
        Args:
            x: Parameter values to evaluate
            
        Returns:
            Log-density value
        """
        x = np.array(x)
        
        if x.shape != self.mean.shape:
            raise ValueError(f"Parameter shape {x.shape} doesn't match prior mean shape {self.mean.shape}")
        
        # Compute log-density
        diff = x - self.mean
        
        if np.isscalar(self.variance):
            # Scalar variance
            log_density = -0.5 * np.sum(diff**2) / self.variance
            log_density -= 0.5 * len(diff) * np.log(2 * np.pi * self.variance)
        else:
            # Diagonal covariance matrix
            log_density = -0.5 * np.sum(diff**2 / self.variance)
            log_density -= 0.5 * np.sum(np.log(2 * np.pi * self.variance))
        
        return log_density
    
    def sample(self, n: int = 1, random_state: Optional[int] = None) -> np.ndarray:
        """
        Generate samples from normal prior
        
        Args:
            n: Number of samples
            random_state: Random seed
            
        Returns:
            Array of samples
        """
        if random_state is not None:
            np.random.seed(random_state)
        
        if np.isscalar(self.variance):
            std = np.sqrt(self.variance)
            samples = np.random.normal(self.mean, std, size=(n,) + self.mean.shape)
        else:
            std = np.sqrt(self.variance)
            samples = np.random.normal(self.mean, std, size=(n,) + self.mean.shape)
        
        if n == 1:
            return samples[0]
        return samples
    
    def update_hyperparameters(self, data: np.ndarray, likelihood_params: Dict) -> None:
        """
        Update hyperparameters for conjugate normal-normal model
        
        For normal likelihood with known variance σ²:
        Posterior: N(μ_n, σ_n²) where
        μ_n = (τ₀μ₀ + nτ x̄) / (τ₀ + nτ)
        τ_n = τ₀ + nτ
        
        Args:
            data: Observed data
            likelihood_params: {'variance': σ²} from likelihood
        """
        if not self._is_conjugate:
            return
        
        # Get likelihood variance
        likelihood_var = likelihood_params.get('variance', 1.0)
        
        # Compute sufficient statistics
        n = len(data)
        x_bar = np.mean(data)
        
        # Prior precision and posterior precision
        prior_precision = 1.0 / self.variance
        likelihood_precision = 1.0 / likelihood_var
        
        posterior_precision = prior_precision + n * likelihood_precision
        posterior_variance = 1.0 / posterior_precision
        
        # Posterior mean
        posterior_mean = (prior_precision * self.mean + 
                         n * likelihood_precision * x_bar) / posterior_precision
        
        # Update hyperparameters
        self.mean = posterior_mean
        self.variance = posterior_variance
        self._hyperparameters['mean'] = self.mean
        self._hyperparameters['variance'] = self.variance
        self._hyperparameters['precision'] = posterior_precision
    
    def auto_scale_variance(self, data_scale: float) -> None:
        """
        Automatically scale prior variance based on data characteristics
        
        Args:
            data_scale: Characteristic scale of the data (e.g., sample std)
        """
        if self.auto_scale:
            # Scale variance proportional to data scale
            scale_factor = max(data_scale, 0.1)  # Minimum scale
            self.variance = self._hyperparameters['variance'] * (scale_factor ** 2)
            self._hyperparameters['variance'] = self.variance


class GammaPrior(Prior):
    """
    Gamma prior distribution Gamma(α, β)
    
    Conjugate prior for precision parameters (inverse of variance).
    Uses shape-rate parameterization: f(x) ∝ x^(α-1) * exp(-βx)
    
    Common uses:
    - Prior for precision parameters in normal models
    - Prior for variance parameters (via inverse relationship)
    """
    
    def __init__(self, shape: float = 1.0, rate: float = 1.0,
                 name: str = "Gamma", auto_scale: bool = True):
        """
        Initialize Gamma prior
        
        Args:
            shape: Shape parameter α > 0
            rate: Rate parameter β > 0  
            name: Descriptive name
            auto_scale: Whether to automatically scale based on data
        """
        super().__init__(name)
        
        if shape <= 0 or rate <= 0:
            raise ValueError("Shape and rate parameters must be positive")
        
        self.shape = shape
        self.rate = rate
        self.auto_scale = auto_scale
        self._is_conjugate = True
        
        self._hyperparameters = {
            'shape': shape,
            'rate': rate,
            'auto_scale': auto_scale
        }
    
    def log_density(self, x: np.ndarray) -> float:
        """
        Evaluate log-density of gamma prior
        
        Args:
            x: Parameter values (must be positive)
            
        Returns:
            Log-density value
        """
        x = np.array(x)
        
        if np.any(x <= 0):
            return -np.inf
        
        # Log-density: (α-1)*log(x) - β*x + α*log(β) - log(Γ(α))
        log_density = (self.shape - 1) * np.sum(np.log(x))
        log_density -= self.rate * np.sum(x)
        log_density += self.shape * np.log(self.rate)
        log_density -= stats.loggamma(self.shape)
        
        return log_density
    
    def sample(self, n: int = 1, random_state: Optional[int] = None) -> np.ndarray:
        """
        Generate samples from gamma prior
        
        Args:
            n: Number of samples
            random_state: Random seed
            
        Returns:
            Array of samples
        """
        if random_state is not None:
            np.random.seed(random_state)
        
        # scipy uses scale = 1/rate parameterization
        scale = 1.0 / self.rate
        samples = np.random.gamma(self.shape, scale, size=n)
        
        if n == 1:
            return samples[0]
        return samples
    
    def update_hyperparameters(self, data: np.ndarray, likelihood_params: Dict) -> None:
        """
        Update hyperparameters for conjugate normal-gamma model
        
        For normal likelihood with unknown precision τ:
        Posterior: Gamma(α_n, β_n) where
        α_n = α₀ + n/2
        β_n = β₀ + (1/2) * Σ(x_i - μ)²
        
        Args:
            data: Observed data
            likelihood_params: {'mean': μ} from likelihood
        """
        if not self._is_conjugate:
            return
        
        n = len(data)
        mean = likelihood_params.get('mean', np.mean(data))
        
        # Sum of squared deviations
        sum_sq_dev = np.sum((data - mean) ** 2)
        
        # Update parameters
        self.shape = self.shape + n / 2
        self.rate = self.rate + sum_sq_dev / 2
        
        self._hyperparameters['shape'] = self.shape
        self._hyperparameters['rate'] = self.rate


class InverseGammaPrior(Prior):
    """
    Inverse Gamma prior distribution InvGamma(α, β)
    
    Conjugate prior for variance parameters in normal models.
    If X ~ Gamma(α, β), then 1/X ~ InvGamma(α, β)
    
    Commonly used for:
    - Prior for variance in normal models
    - Prior for scale parameters
    """
    
    def __init__(self, shape: float = 1.0, scale: float = 1.0,
                 name: str = "InverseGamma", auto_scale: bool = True):
        """
        Initialize Inverse Gamma prior
        
        Args:
            shape: Shape parameter α > 0
            scale: Scale parameter β > 0
            name: Descriptive name
            auto_scale: Whether to automatically scale based on data
        """
        super().__init__(name)
        
        if shape <= 0 or scale <= 0:
            raise ValueError("Shape and scale parameters must be positive")
        
        self.shape = shape
        self.scale = scale
        self.auto_scale = auto_scale
        self._is_conjugate = True
        
        self._hyperparameters = {
            'shape': shape,
            'scale': scale,
            'auto_scale': auto_scale
        }
    
    def log_density(self, x: np.ndarray) -> float:
        """
        Evaluate log-density of inverse gamma prior
        
        Args:
            x: Parameter values (must be positive)
            
        Returns:
            Log-density value
        """
        x = np.array(x)
        
        if np.any(x <= 0):
            return -np.inf
        
        # Log-density: α*log(β) - log(Γ(α)) - (α+1)*log(x) - β/x
        log_density = self.shape * np.log(self.scale)
        log_density -= stats.loggamma(self.shape)
        log_density -= (self.shape + 1) * np.sum(np.log(x))
        log_density -= self.scale * np.sum(1.0 / x)
        
        return log_density
    
    def sample(self, n: int = 1, random_state: Optional[int] = None) -> np.ndarray:
        """
        Generate samples from inverse gamma prior
        
        Args:
            n: Number of samples
            random_state: Random seed
            
        Returns:
            Array of samples
        """
        if random_state is not None:
            np.random.seed(random_state)
        
        # Sample from gamma and take reciprocal
        gamma_samples = np.random.gamma(self.shape, 1.0/self.scale, size=n)
        samples = 1.0 / gamma_samples
        
        if n == 1:
            return samples[0]
        return samples
    
    def update_hyperparameters(self, data: np.ndarray, likelihood_params: Dict) -> None:
        """
        Update hyperparameters for conjugate normal-inverse-gamma model
        
        For normal likelihood with unknown variance σ²:
        Posterior: InvGamma(α_n, β_n) where
        α_n = α₀ + n/2
        β_n = β₀ + (1/2) * Σ(x_i - μ)²
        
        Args:
            data: Observed data
            likelihood_params: {'mean': μ} from likelihood
        """
        if not self._is_conjugate:
            return
        
        n = len(data)
        mean = likelihood_params.get('mean', np.mean(data))
        
        # Sum of squared deviations
        sum_sq_dev = np.sum((data - mean) ** 2)
        
        # Update parameters
        self.shape = self.shape + n / 2
        self.scale = self.scale + sum_sq_dev / 2
        
        self._hyperparameters['shape'] = self.shape
        self._hyperparameters['scale'] = self.scale


class BetaPrior(Prior):
    """
    Beta prior distribution Beta(α, β)
    
    Conjugate prior for parameters constrained to (0,1).
    Common uses:
    - Prior for probabilities
    - Prior for correlation parameters (after transformation)
    """
    
    def __init__(self, alpha: float = 1.0, beta: float = 1.0,
                 name: str = "Beta"):
        """
        Initialize Beta prior
        
        Args:
            alpha: Shape parameter α > 0
            beta: Shape parameter β > 0
            name: Descriptive name
        """
        super().__init__(name)
        
        if alpha <= 0 or beta <= 0:
            raise ValueError("Alpha and beta parameters must be positive")
        
        self.alpha = alpha
        self.beta = beta
        self._is_conjugate = True
        
        self._hyperparameters = {
            'alpha': alpha,
            'beta': beta
        }
    
    def log_density(self, x: np.ndarray) -> float:
        """
        Evaluate log-density of beta prior
        
        Args:
            x: Parameter values (must be in (0,1))
            
        Returns:
            Log-density value
        """
        x = np.array(x)
        
        if np.any(x <= 0) or np.any(x >= 1):
            return -np.inf
        
        log_density = (self.alpha - 1) * np.sum(np.log(x))
        log_density += (self.beta - 1) * np.sum(np.log(1 - x))
        log_density += stats.loggamma(self.alpha + self.beta)
        log_density -= stats.loggamma(self.alpha) + stats.loggamma(self.beta)
        
        return log_density
    
    def sample(self, n: int = 1, random_state: Optional[int] = None) -> np.ndarray:
        """
        Generate samples from beta prior
        
        Args:
            n: Number of samples
            random_state: Random seed
            
        Returns:
            Array of samples
        """
        if random_state is not None:
            np.random.seed(random_state)
        
        samples = np.random.beta(self.alpha, self.beta, size=n)
        
        if n == 1:
            return samples[0]
        return samples


class MultivariateNormalPrior(Prior):
    """
    Multivariate Normal prior distribution N(μ, Σ)
    
    Conjugate prior for multivariate normal likelihood with known covariance.
    Supports TOL's matrix-valued parameters and automatic scaling.
    """
    
    def __init__(self, mean: np.ndarray, covariance: np.ndarray,
                 name: str = "MultivariateNormal", auto_scale: bool = True):
        """
        Initialize Multivariate Normal prior
        
        Args:
            mean: Prior mean vector
            covariance: Prior covariance matrix
            name: Descriptive name
            auto_scale: Whether to automatically scale covariance
        """
        super().__init__(name)
        
        self.mean = np.array(mean)
        self.covariance = np.array(covariance)
        self.auto_scale = auto_scale
        self._is_conjugate = True
        
        # Validate dimensions
        if self.mean.ndim != 1:
            raise ValueError("Mean must be a 1D array")
        if self.covariance.ndim != 2:
            raise ValueError("Covariance must be a 2D array")
        if self.covariance.shape[0] != self.covariance.shape[1]:
            raise ValueError("Covariance must be square")
        if len(self.mean) != self.covariance.shape[0]:
            raise ValueError("Mean and covariance dimensions must match")
        
        # Check positive definiteness
        try:
            self.precision = np.linalg.inv(self.covariance)
            self.chol_cov = np.linalg.cholesky(self.covariance)
        except np.linalg.LinAlgError:
            raise ValueError("Covariance matrix must be positive definite")
        
        self._hyperparameters = {
            'mean': self.mean.copy(),
            'covariance': self.covariance.copy(),
            'precision': self.precision.copy(),
            'auto_scale': auto_scale
        }
    
    def log_density(self, x: np.ndarray) -> float:
        """
        Evaluate log-density of multivariate normal prior
        
        Args:
            x: Parameter vector
            
        Returns:
            Log-density value
        """
        x = np.array(x)
        
        if x.shape != self.mean.shape:
            raise ValueError(f"Parameter shape {x.shape} doesn't match prior mean shape {self.mean.shape}")
        
        diff = x - self.mean
        quad_form = diff @ self.precision @ diff
        
        log_density = -0.5 * quad_form
        log_density -= 0.5 * len(self.mean) * np.log(2 * np.pi)
        log_density -= 0.5 * np.log(np.linalg.det(self.covariance))
        
        return log_density
    
    def sample(self, n: int = 1, random_state: Optional[int] = None) -> np.ndarray:
        """
        Generate samples from multivariate normal prior
        
        Args:
            n: Number of samples
            random_state: Random seed
            
        Returns:
            Array of samples
        """
        if random_state is not None:
            np.random.seed(random_state)
        
        samples = np.random.multivariate_normal(self.mean, self.covariance, size=n)
        
        if n == 1:
            return samples[0]
        return samples


class HierarchicalPrior(Prior):
    """
    Hierarchical prior for multi-level Bayesian models
    
    Implements TOL's hierarchical modeling approach where parameters
    have their own prior distributions with hyperpriors.
    
    Structure:
    θ ~ f(θ | η)      (parameter prior)
    η ~ g(η | ξ)      (hyperprior)
    
    Common use cases:
    - Random effects models
    - Shrinkage priors
    - Multi-group models
    """
    
    def __init__(self, parameter_prior: Prior, hyperprior: Prior,
                 name: str = "Hierarchical"):
        """
        Initialize Hierarchical prior
        
        Args:
            parameter_prior: Prior distribution for parameters
            hyperprior: Prior distribution for hyperparameters
            name: Descriptive name
        """
        super().__init__(name)
        
        self.parameter_prior = parameter_prior
        self.hyperprior = hyperprior
        self._is_conjugate = False  # Generally not conjugate
        
        self._hyperparameters = {
            'parameter_prior': parameter_prior,
            'hyperprior': hyperprior
        }
    
    def log_density(self, x: np.ndarray, hyperparams: Optional[np.ndarray] = None) -> float:
        """
        Evaluate log-density of hierarchical prior
        
        Args:
            x: Parameter values
            hyperparams: Hyperparameter values (if None, uses prior mean)
            
        Returns:
            Log-density value
        """
        if hyperparams is None:
            # Use prior mean of hyperparameters
            hyperparams = self.hyperprior.sample(1)
        
        # Update parameter prior with hyperparameters
        # This is model-specific and would need to be customized
        log_density = self.parameter_prior.log_density(x)
        log_density += self.hyperprior.log_density(hyperparams)
        
        return log_density
    
    def sample(self, n: int = 1, random_state: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate samples from hierarchical prior
        
        Args:
            n: Number of samples
            random_state: Random seed
            
        Returns:
            Tuple of (parameter_samples, hyperparameter_samples)
        """
        if random_state is not None:
            np.random.seed(random_state)
        
        # Sample hyperparameters first
        hyperparams = self.hyperprior.sample(n)
        
        # Sample parameters given hyperparameters
        params = []
        for i in range(n):
            # Update parameter prior with current hyperparameters
            # This is model-specific
            param_sample = self.parameter_prior.sample(1)
            params.append(param_sample)
        
        params = np.array(params)
        
        if n == 1:
            return params[0], hyperparams[0] if hasattr(hyperparams, '__len__') else hyperparams
        
        return params, hyperparams


class ShrinkagePrior(Prior):
    """
    Shrinkage prior for sparse regression models
    
    Implements priors that shrink parameters toward zero,
    common in Bayesian variable selection and regularization.
    
    Examples:
    - Horseshoe prior
    - Normal-Gamma (Laplace) prior
    - Spike-and-slab prior
    """
    
    def __init__(self, global_scale: float = 1.0, local_scales: Optional[np.ndarray] = None,
                 prior_type: str = "horseshoe", name: str = "Shrinkage"):
        """
        Initialize Shrinkage prior
        
        Args:
            global_scale: Global shrinkage parameter
            local_scales: Local shrinkage parameters (one per coefficient)
            prior_type: Type of shrinkage prior ("horseshoe", "laplace", "spike_slab")
            name: Descriptive name
        """
        super().__init__(name)
        
        self.global_scale = global_scale
        self.local_scales = local_scales
        self.prior_type = prior_type
        self._is_conjugate = False
        
        self._hyperparameters = {
            'global_scale': global_scale,
            'local_scales': local_scales,
            'prior_type': prior_type
        }
    
    def log_density(self, x: np.ndarray) -> float:
        """
        Evaluate log-density of shrinkage prior
        
        Args:
            x: Parameter values
            
        Returns:
            Log-density value
        """
        x = np.array(x)
        
        if self.prior_type == "horseshoe":
            # Horseshoe prior: τ²λ²/(τ²λ² + 1) shrinkage
            # Simplified implementation
            log_density = -np.sum(np.log(1 + x**2 / self.global_scale**2))
            
        elif self.prior_type == "laplace":
            # Laplace (Double exponential) prior
            log_density = -np.sum(np.abs(x)) / self.global_scale
            log_density -= len(x) * np.log(2 * self.global_scale)
            
        elif self.prior_type == "spike_slab":
            # Spike and slab prior (simplified)
            spike_prob = 0.1  # Probability of being in spike
            spike_var = 0.01  # Spike variance
            slab_var = self.global_scale**2  # Slab variance
            
            # Mixture density (approximate)
            spike_density = stats.norm.logpdf(x, 0, np.sqrt(spike_var))
            slab_density = stats.norm.logpdf(x, 0, np.sqrt(slab_var))
            
            log_density = np.sum(np.logaddexp(
                np.log(spike_prob) + spike_density,
                np.log(1 - spike_prob) + slab_density
            ))
        else:
            raise ValueError(f"Unknown shrinkage prior type: {self.prior_type}")
        
        return log_density
    
    def sample(self, n: int = 1, random_state: Optional[int] = None) -> np.ndarray:
        """
        Generate samples from shrinkage prior
        
        Args:
            n: Number of samples
            random_state: Random seed
            
        Returns:
            Array of samples
        """
        if random_state is not None:
            np.random.seed(random_state)
        
        if self.prior_type == "laplace":
            # Laplace distribution sampling
            samples = np.random.laplace(0, self.global_scale, size=n)
            
        elif self.prior_type == "horseshoe":
            # Simplified horseshoe sampling
            # In practice, this requires more sophisticated sampling
            samples = np.random.normal(0, self.global_scale, size=n)
            
        elif self.prior_type == "spike_slab":
            # Spike and slab sampling
            spike_prob = 0.1
            spike_var = 0.01
            slab_var = self.global_scale**2
            
            samples = []
            for _ in range(n):
                if np.random.rand() < spike_prob:
                    # Sample from spike
                    sample = np.random.normal(0, np.sqrt(spike_var))
                else:
                    # Sample from slab
                    sample = np.random.normal(0, np.sqrt(slab_var))
                samples.append(sample)
            
            samples = np.array(samples)
        else:
            raise ValueError(f"Unknown shrinkage prior type: {self.prior_type}")
        
        if n == 1:
            return samples[0]
        return samples