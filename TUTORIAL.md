# TOL Python Tutorial

## Introduction

Welcome to TOL Python! This tutorial will guide you through the essential concepts and features of the TOL Python time series library. Whether you're migrating from TOL or starting fresh with time series analysis in Python, this guide will help you get up to speed quickly.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Basic Concepts](#basic-concepts)
3. [Creating Your First Series](#creating-your-first-series)
4. [Working with Dates](#working-with-dates)
5. [Series Operations](#series-operations)
6. [Data Analysis](#data-analysis)
7. [Advanced Features](#advanced-features)
8. [Real-World Examples](#real-world-examples)

## Getting Started

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd TOL-python

# Install in development mode
pip install -e .

# Optional: Install additional dependencies
pip install -e ".[full]"
```

### Quick Test

```python
from tol_python import Serie, Date, TimeSet
import numpy as np

# Create a simple series
data = [100, 102, 105, 103, 107]
serie = Serie(data=data, first_date="y2023m01d01", last_date="y2023m01d05")

print(f"Series length: {len(serie)}")
print(f"First value: {serie[0]}")
print(f"Mean: {serie.mean():.2f}")
```

## Basic Concepts

### 1. The Serie Class

The `Serie` class is the core of TOL Python. It represents a time series with:
- **Data values**: Numerical data points
- **Dating**: Time stamps for each value
- **Missing value support**: Handles gaps in data naturally

### 2. TOL Date Format

TOL uses a specific date format: `yYYYYmMMdDD`
- `y2023m01d15` = January 15, 2023
- `y2023m01` = January 2023 (monthly series)
- `y2023` = Year 2023 (yearly series)

### 3. TimeSet System

TimeSets define the dating pattern:
- **daily**: Every day
- **monthly**: First day of each month
- **yearly**: January 1st of each year
- **custom**: User-defined patterns

## Creating Your First Series

### Example 1: Daily Stock Prices

```python
from tol_python import Serie
import numpy as np

# Stock price data for a week
stock_prices = [150.00, 152.50, 151.75, 153.25, 155.00]

# Create daily series
stock_serie = Serie(
    data=stock_prices,
    first_date="y2023m10d23",  # October 23, 2023
    last_date="y2023m10d27",   # October 27, 2023
    name="AAPL_Stock_Price"
)

print(f"Stock on Oct 25: ${stock_serie['y2023m10d25']}")
print(f"Average price: ${stock_serie.mean():.2f}")
```

### Example 2: Monthly Sales Data

```python
from tol_python import Serie, TimeSet

# Monthly sales for a year
monthly_sales = [120, 135, 142, 158, 165, 180, 195, 188, 172, 160, 145, 155]

# Create monthly series
sales_serie = Serie(
    data=monthly_sales,
    first_date="y2023m01d01",
    last_date="y2023m12d01",
    dating=TimeSet("monthly"),
    name="Monthly_Sales"
)

print(f"Q1 Average: {sales_serie[0:3].mean():.1f}")
print(f"Best month: {sales_serie.max()}")
```

### Example 3: Series with Missing Values

```python
import numpy as np
from tol_python import Serie

# Data with missing values
temperature_data = [20.5, 22.1, np.nan, 24.8, 23.2, np.nan, 25.1]

temp_serie = Serie(
    data=temperature_data,
    first_date="y2023m07d01",
    last_date="y2023m07d07",
    name="Daily_Temperature"
)

print(f"Valid readings: {temp_serie.count()}")
print(f"Average temp: {temp_serie.mean():.1f}°C")  # Ignores NaN values
```

## Working with Dates

### Creating and Manipulating Dates

```python
from tol_python import Date

# Create dates
date1 = Date("y2023m06d15")
date2 = Date(year=2023, month=6, day=15)  # Alternative syntax

print(f"Date: {date1.to_string()}")
print(f"As datetime: {date1.to_datetime()}")

# Date arithmetic
next_week = date1.add_days(7)
next_month = date1.add_months(1)
next_year = date1.add_years(1)

print(f"Next week: {next_week.to_string()}")
print(f"Next month: {next_month.to_string()}")
```

### Working with TimeSet

```python
from tol_python import TimeSet

# Different TimeSet types
daily = TimeSet("daily")
monthly = TimeSet("monthly")
yearly = TimeSet("yearly")

# Custom TimeSet - last day of month
month_end = TimeSet("last_day_of_month")

# Check if date belongs to TimeSet
print(f"Is Jan 31 a month end? {month_end.contains('y2023m01d31')}")
print(f"Is Jan 15 a month end? {month_end.contains('y2023m01d15')}")

# Generate date sequences
start_date = Date("y2023m01d01")
end_date = Date("y2023m12d01")
monthly_dates = monthly.dates_in_range(start_date, end_date)
print(f"Monthly dates in 2023: {len(monthly_dates)}")
```

### Special Dates: Begin and End

```python
from tol_python import Date, Serie

# Using sentinels for unbounded series
data = [1, 2, 3, 4, 5]

# Series from beginning of time to specific date
serie1 = Serie(data=data, first_date=Date.Begin, last_date="y2023m01d05")

# Series from specific date to end of time  
serie2 = Serie(data=data, first_date="y2023m01d01", last_date=Date.End)

print(f"Serie1 first date: {serie1.first_date}")
print(f"Serie2 last date: {serie2.last_date}")
```

## Series Operations

### Basic Arithmetic

```python
from tol_python import Serie

# Create two series
prices = Serie([100, 102, 105, 103, 107], 
               first_date="y2023m01d01", last_date="y2023m01d05")
volumes = Serie([1000, 1200, 800, 1500, 900], 
                first_date="y2023m01d01", last_date="y2023m01d05")

# Arithmetic operations
doubled_prices = prices * 2
total_value = prices * volumes  # Element-wise multiplication
price_change = prices - prices.lag(1)  # Daily changes

print(f"Price changes: {price_change.values()}")
print(f"Total value on day 3: ${total_value[2]:,.0f}")
```

### Time Series Operations

```python
# Lag and lead operations
lagged_prices = prices.lag(1)      # Yesterday's prices
future_prices = prices.lead(1)     # Tomorrow's prices

# Differences
price_diff = prices.diff()         # First difference
price_diff2 = prices.diff(2)      # Second difference

# Moving averages
ma_3 = prices.moving_average(3)   # 3-day moving average
ma_5 = prices.moving_average(5)   # 5-day moving average

print(f"3-day MA: {ma_3.values()}")
print(f"Price differences: {price_diff.values()}")
```

### Data Access and Slicing

```python
# Access by date
jan_1_price = prices["y2023m01d01"]
jan_3_price = prices["y2023m01d03"]

# Access by index
first_price = prices[0]
last_price = prices[-1]

# Slicing
first_three = prices[0:3]                           # First 3 values
date_range = prices["y2023m01d02":"y2023m01d04"]   # Date range

# Extract subseries
subset = prices.sub_ser("y2023m01d02", "y2023m01d04")

print(f"Subset length: {len(subset)}")
print(f"Subset values: {subset.values()}")
```

## Data Analysis

### Descriptive Statistics

```python
from tol_python import Serie
import numpy as np

# Generate sample data
np.random.seed(42)
returns = np.random.normal(0.001, 0.02, 252)  # Daily returns for a year
cumulative_prices = 100 * np.exp(np.cumsum(returns))

price_serie = Serie(
    data=cumulative_prices,
    first_date="y2023m01d01",
    last_date="y2023m12d29",
    name="Stock_Price"
)

# Basic statistics
print(f"Mean price: ${price_serie.mean():.2f}")
print(f"Std deviation: ${price_serie.std():.2f}")
print(f"Min price: ${price_serie.min():.2f}")
print(f"Max price: ${price_serie.max():.2f}")
print(f"Total observations: {price_serie.count()}")
```

### Advanced Statistics

```python
from tol_python import SerieStatistics

# Create statistics object
stats = SerieStatistics(price_serie)

# Calculate returns
returns_serie = price_serie.pct_change()

# Advanced metrics
autocorr = stats.autocorrelation(lags=10)
volatility = returns_serie.std() * np.sqrt(252)  # Annualized volatility

print(f"Annualized volatility: {volatility:.1%}")
print(f"First-order autocorrelation: {autocorr[1]:.3f}")

# Stationarity test
adf_result = stats.stationarity_test()
print(f"ADF p-value: {adf_result['p_value']:.4f}")
```

### Working with Missing Data

```python
# Series with gaps
data_with_gaps = [100, 102, np.nan, np.nan, 107, 105, np.nan, 108]
gappy_serie = Serie(data=data_with_gaps, 
                   first_date="y2023m01d01", 
                   last_date="y2023m01d08")

# Handle missing values
valid_count = gappy_serie.count()           # Count non-missing
mean_value = gappy_serie.mean()             # Mean excluding missing
filled_serie = gappy_serie.fillna(method='forward')  # Forward fill

print(f"Valid observations: {valid_count}")
print(f"Mean (excluding missing): {mean_value:.2f}")
print(f"Filled series: {filled_serie.values()}")
```

## Advanced Features

### ARIMA Modeling

```python
from tol_python.arima import ARIMAModel, AutoARIMA

# Generate sample time series
np.random.seed(42)
n = 200
ar_coef = [0.6, -0.2]  # AR(2) coefficients
data = []
for i in range(n):
    if i < 2:
        data.append(np.random.randn())
    else:
        value = (ar_coef[0] * data[i-1] + 
                ar_coef[1] * data[i-2] + 
                np.random.randn())
        data.append(value)

ts_serie = Serie(data=data, first_date="y2020m01d01", last_date="y2020m07d18")

# Automatic model selection
auto_arima = AutoARIMA()
best_model = auto_arima.select_model(ts_serie, max_p=3, max_d=2, max_q=3)

print(f"Best model order: {best_model.order}")
print(f"AIC: {best_model.aic:.2f}")

# Forecasting
forecast = best_model.predict(steps=10)
print(f"10-step forecast: {forecast.values()}")
```

### Frequency Domain Analysis

```python
from tol_python.frequency import FrequencyDomain, FrequencyFilters

# Create a signal with noise
t = np.linspace(0, 1, 1000)
signal = np.sin(2 * np.pi * 5 * t) + 0.5 * np.sin(2 * np.pi * 20 * t)
noise = 0.2 * np.random.randn(len(t))
noisy_signal = signal + noise

signal_serie = Serie(data=noisy_signal, 
                    first_date="y2023m01d01", 
                    last_date="y2023m02d09")

# Frequency analysis
freq_domain = FrequencyDomain()
fft_result = freq_domain.fft(signal_serie)
power_spectrum = freq_domain.power_spectrum(signal_serie)

# Apply low-pass filter
filters = FrequencyFilters()
filtered_serie = filters.low_pass_filter(signal_serie, cutoff=10.0)

print(f"Original signal std: {signal_serie.std():.3f}")
print(f"Filtered signal std: {filtered_serie.std():.3f}")
```

### Bayesian Analysis

```python
from tol_python.bayesian import BayesianARIMA

# Bayesian ARIMA modeling
bayesian_model = BayesianARIMA(order=(1, 0, 1))

# MCMC estimation
posterior_samples = bayesian_model.mcmc_estimate(
    ts_serie, 
    n_samples=1000, 
    burn_in=500
)

# Posterior analysis
posterior_mean = np.mean(posterior_samples, axis=0)
credible_intervals = np.percentile(posterior_samples, [2.5, 97.5], axis=0)

print(f"Posterior means: {posterior_mean}")
print(f"95% credible intervals: {credible_intervals}")

# Posterior predictive
pred_samples = bayesian_model.posterior_predictive(n_steps=5)
pred_mean = np.mean(pred_samples, axis=0)
pred_intervals = np.percentile(pred_samples, [2.5, 97.5], axis=0)

print(f"Predictive mean: {pred_mean}")
```

## Real-World Examples

### Example 1: Financial Time Series Analysis

```python
from tol_python import Serie, SerieStatistics
from tol_python.arima import AutoARIMA
import numpy as np

# Simulate daily stock returns
np.random.seed(42)
returns = np.random.normal(0.0005, 0.015, 500)  # 500 days of returns
prices = 100 * np.exp(np.cumsum(returns))

stock_serie = Serie(
    data=prices,
    first_date="y2022m01d03",
    last_date="y2023m11d17",
    name="Stock_Price"
)

# 1. Calculate key metrics
returns_serie = stock_serie.pct_change()
volatility = returns_serie.std() * np.sqrt(252)
max_drawdown = (stock_serie / stock_serie.expanding_max() - 1).min()

print("=== Financial Analysis ===")
print(f"Total return: {(stock_serie[-1] / stock_serie[0] - 1):.1%}")
print(f"Annualized volatility: {volatility:.1%}")
print(f"Maximum drawdown: {max_drawdown:.1%}")

# 2. Statistical tests
stats = SerieStatistics(returns_serie)
adf_result = stats.stationarity_test()
jb_result = stats.normality_test()

print(f"Returns are stationary: {adf_result['p_value'] < 0.05}")
print(f"Returns are normal: {jb_result['p_value'] > 0.05}")

# 3. ARIMA modeling for volatility
abs_returns = returns_serie.abs()
vol_model = AutoARIMA()
fitted_vol = vol_model.select_model(abs_returns)

vol_forecast = fitted_vol.predict(steps=20)
print(f"Avg forecasted volatility (next 20 days): {vol_forecast.mean():.4f}")
```

### Example 2: Economic Data Analysis

```python
# Simulate quarterly GDP data
quarters = 40  # 10 years of quarterly data
gdp_growth = np.random.normal(0.006, 0.008, quarters)  # 0.6% avg quarterly growth
gdp_data = [100]  # Base GDP

for growth in gdp_growth:
    gdp_data.append(gdp_data[-1] * (1 + growth))

gdp_serie = Serie(
    data=gdp_data,
    first_date="y2014m01d01",
    last_date="y2024m01d01",
    dating=TimeSet("quarterly"),
    name="Real_GDP"
)

print("=== Economic Analysis ===")

# 1. Growth analysis
growth_rates = gdp_serie.pct_change() * 100  # Convert to percentage
annual_growth = growth_rates.mean() * 4      # Annualized

print(f"Average quarterly growth: {growth_rates.mean():.2f}%")
print(f"Annualized growth rate: {annual_growth:.2f}%")

# 2. Recession detection (2 consecutive quarters of negative growth)
negative_growth = growth_rates < 0
recession_quarters = []

for i in range(1, len(negative_growth)):
    if negative_growth[i] and negative_growth[i-1]:
        recession_quarters.append(i)

print(f"Recession quarters detected: {len(recession_quarters)}")

# 3. Trend analysis
linear_trend = np.polyfit(range(len(gdp_serie)), gdp_serie.values(), 1)[0]
print(f"Linear trend (quarterly): {linear_trend:.2f}")
```

### Example 3: Seasonal Decomposition

```python
# Create data with trend, seasonality, and noise
n_years = 5
n_points = n_years * 12  # Monthly data

# Components
trend = np.linspace(100, 150, n_points)
seasonal = 10 * np.sin(2 * np.pi * np.arange(n_points) / 12)
noise = np.random.normal(0, 2, n_points)
observed = trend + seasonal + noise

seasonal_serie = Serie(
    data=observed,
    first_date="y2019m01d01",
    last_date="y2023m12d01",
    dating=TimeSet("monthly"),
    name="Sales_Data"
)

print("=== Seasonal Analysis ===")

# 1. Calculate seasonal indices (simple method)
monthly_means = np.zeros(12)
for month in range(12):
    month_data = [seasonal_serie[i] for i in range(month, len(seasonal_serie), 12)]
    monthly_means[month] = np.mean(month_data)

overall_mean = np.mean(monthly_means)
seasonal_indices = monthly_means / overall_mean

months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

print("Seasonal indices:")
for i, month in enumerate(months):
    print(f"{month}: {seasonal_indices[i]:.3f}")

# 2. Deseasonalize the data
deseasonalized = []
for i, value in enumerate(seasonal_serie.values()):
    month_idx = i % 12
    deseasonalized.append(value / seasonal_indices[month_idx])

deseason_serie = Serie(
    data=deseasonalized,
    first_date=seasonal_serie.first_date,
    last_date=seasonal_serie.last_date,
    dating=seasonal_serie.dating,
    name="Deseasonalized_Sales"
)

print(f"Original std: {seasonal_serie.std():.2f}")
print(f"Deseasonalized std: {deseason_serie.std():.2f}")
```

### Example 4: Cross-Series Analysis

```python
# Analyze relationship between two time series
np.random.seed(42)

# Generate correlated series
n = 200
x = np.random.randn(n)
y = 0.7 * x + 0.3 * np.random.randn(n)  # 70% correlation

series_x = Serie(data=x, first_date="y2022m01d01", last_date="y2022m07d19", name="Series_X")
series_y = Serie(data=y, first_date="y2022m01d01", last_date="y2022m07d19", name="Series_Y")

print("=== Cross-Series Analysis ===")

# 1. Correlation analysis
correlation = np.corrcoef(series_x.values(), series_y.values())[0, 1]
print(f"Correlation coefficient: {correlation:.3f}")

# 2. Lead-lag analysis
max_lag = 10
cross_correlations = []

for lag in range(-max_lag, max_lag + 1):
    if lag < 0:
        # X leads Y
        x_lead = series_x.values()[:lag]
        y_lag = series_y.values()[-lag:]
    elif lag > 0:
        # Y leads X  
        x_lead = series_x.values()[lag:]
        y_lag = series_y.values()[:-lag]
    else:
        # No lag
        x_lead = series_x.values()
        y_lag = series_y.values()
    
    if len(x_lead) > 0 and len(y_lag) > 0:
        cc = np.corrcoef(x_lead, y_lag)[0, 1]
        cross_correlations.append((lag, cc))

# Find optimal lag
best_lag, best_corr = max(cross_correlations, key=lambda x: abs(x[1]))
print(f"Best correlation at lag {best_lag}: {best_corr:.3f}")

# 3. Rolling correlation
window = 30
rolling_corr = []

for i in range(window, len(series_x)):
    x_window = series_x.values()[i-window:i]
    y_window = series_y.values()[i-window:i]
    corr = np.corrcoef(x_window, y_window)[0, 1]
    rolling_corr.append(corr)

rolling_corr_serie = Serie(
    data=rolling_corr,
    first_date=series_x.dates()[window],
    last_date=series_x.last_date,
    name="Rolling_Correlation"
)

print(f"Average rolling correlation: {rolling_corr_serie.mean():.3f}")
print(f"Correlation stability (std): {rolling_corr_serie.std():.3f}")
```

## Next Steps

### Advanced Topics
- **Complex Series**: Working with complex-valued time series
- **Multivariate Models**: VAR, VECM modeling
- **Machine Learning**: Integration with scikit-learn
- **Big Data**: Handling large time series datasets
- **Real-time Processing**: Streaming data analysis

### Integration Examples
- **Pandas**: Convert between TOL and pandas
- **Matplotlib**: Plotting and visualization
- **Jupyter**: Interactive analysis notebooks
- **Database**: SQL integration for data storage

### Performance Optimization
- **Numba**: JIT compilation for speed
- **Multiprocessing**: Parallel analysis
- **Memory Management**: Efficient large dataset handling

### Resources
- API Reference: `API_REFERENCE.md`
- Developer Guide: `DEVELOPER_GUIDE.md`
- Architecture: `ARCHITECTURE.md`
- Examples: `examples/` directory

This tutorial provides a comprehensive introduction to TOL Python. As you become more comfortable with the basics, explore the advanced features and real-world examples to build sophisticated time series analysis workflows.